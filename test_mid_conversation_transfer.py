#!/usr/bin/env python3
"""
Test Mid-Conversation Transfer Fix
=================================

This script validates that the routing fix works correctly for mid-conversation transfers.
"""

import json
import sys
import os

# Add the project root to Python path
sys.path.append('.')

def test_routing_logic_fix():
    """Test that the routing logic fix works correctly."""
    print("🧪 Testing Mid-Conversation Transfer Fix")
    print("=" * 60)
    
    try:
        from llm_app.pipeline.steps.agentic_framework.autogen.session.conversation_session import ConversationSession
        
        print("✅ [TEST] ConversationSession imported successfully")
        
        # Create a mock session
        session = ConversationSession(None)  # We'll test the logic, not the actual context
        
        # Test the fixed routing logic
        print("\n🔧 [TEST] Testing get_route_agent logic...")
        
        # Simulate the scenario:
        # 1. User starts conversation → Should route to triage
        # 2. Triage routes to booking → Booking agent responds
        # 3. User says "Something's wrong with my flight" → Should route BACK to triage
        
        triage_agent = "Triage_Agent_V2"
        
        # This should always return triage agent now (the fix)
        result = session.get_route_agent(triage_agent)
        
        print(f"📋 [TEST] Route agent result: {result}")
        
        if result == triage_agent:
            print("✅ [TEST] CORRECT: Always routes to triage agent")
            print("✅ [TEST] This fixes the mid-conversation transfer issue!")
            return True
        else:
            print(f"❌ [TEST] WRONG: Should route to {triage_agent}, got {result}")
            return False
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_gen_agent_template_fix():
    """Test that gen_agent template no longer has triage logic."""
    print(f"\n🧪 Testing Gen Agent Template Fix")
    print("=" * 60)
    
    try:
        template_path = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt"
        
        with open(template_path, 'r') as f:
            content = f.read()
        
        # Check that triage logic has been removed
        forbidden_patterns = [
            "route_to_triage_agent",
            "silently run route_to_triage_agent()",
            "transfer"
        ]
        
        found_issues = []
        for pattern in forbidden_patterns:
            if pattern.lower() in content.lower():
                found_issues.append(pattern)
        
        if found_issues:
            print(f"❌ [TEST] Found forbidden triage patterns: {found_issues}")
            return False
        else:
            print("✅ [TEST] No triage logic found in gen_agent template")
            print("✅ [TEST] Gen agents will focus on their specific roles")
            return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        return False


def test_triage_v2_enhancement():
    """Test that triage V2 template has enhanced mid-conversation handling."""
    print(f"\n🧪 Testing Triage V2 Mid-Conversation Enhancement")
    print("=" * 60)
    
    try:
        template_path = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
        
        with open(template_path, 'r') as f:
            content = f.read()
        
        # Check for enhanced mid-conversation handling
        required_patterns = [
            "MID-CONVERSATION TRANSFER SUPPORT",
            "Context Switch Detection",
            "Something's wrong with my flight",
            "Re-evaluate intent based on the LATEST message",
            "Don't assume continuation of previous workflow"
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"❌ [TEST] Missing required patterns: {missing_patterns}")
            return False
        else:
            print("✅ [TEST] All mid-conversation handling patterns found")
            print("✅ [TEST] Triage V2 can handle context switches properly")
            return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        return False


def simulate_conversation_flow():
    """Simulate the conversation flow to show how it should work now."""
    print(f"\n🎭 Simulating Fixed Conversation Flow")
    print("=" * 60)
    
    print("📋 SCENARIO: User in booking flow says 'Something's wrong with my flight'")
    print()
    
    print("🔄 OLD BEHAVIOR (BROKEN):")
    print("  1. User: 'I want to book a flight' → Triage routes to Booking Agent")
    print("  2. Booking Agent: 'Great! Where would you like to go?'")
    print("  3. User: 'Something's wrong with my flight' → ❌ STAYS with Booking Agent")
    print("  4. Booking Agent: Tries to handle flight issues (WRONG!)")
    print()
    
    print("✅ NEW BEHAVIOR (FIXED):")
    print("  1. User: 'I want to book a flight' → Triage routes to Booking Agent")
    print("  2. Booking Agent: 'Great! Where would you like to go?'")
    print("  3. User: 'Something's wrong with my flight' → ✅ ROUTES BACK to Triage")
    print("  4. Triage: Detects context switch, routes to Flight Status/Support")
    print("  5. Flight Support Agent: Handles the flight issue properly")
    print()
    
    print("🎯 KEY CHANGES:")
    print("  ✅ Removed triage logic from gen_agent template")
    print("  ✅ Fixed routing to always go through triage for new messages")
    print("  ✅ Enhanced triage V2 to detect context switches")
    print("  ✅ Triage re-evaluates intent on every user message")
    
    return True


def main():
    """Run all tests for the mid-conversation transfer fix."""
    print("🎯 Mid-Conversation Transfer Fix Validation")
    print("=" * 70)
    
    tests = [
        ("Routing Logic Fix", test_routing_logic_fix),
        ("Gen Agent Template Fix", test_gen_agent_template_fix),
        ("Triage V2 Enhancement", test_triage_v2_enhancement),
        ("Conversation Flow Simulation", simulate_conversation_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SUCCESS: Mid-conversation transfer fix is working!")
        print("\n📋 The fix ensures:")
        print("  ✅ All user messages go through triage agent")
        print("  ✅ Triage can detect context switches")
        print("  ✅ Proper routing based on latest user intent")
        print("  ✅ No more stuck conversations in wrong workflows")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
