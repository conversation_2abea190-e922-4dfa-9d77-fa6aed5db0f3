import time

from stream_app.helpers.logging_helper import print_info_log


def measure_execution_time(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        print_info_log("", f"Execution time for {func.__name__} ", execution_time)
        return result

    return wrapper
