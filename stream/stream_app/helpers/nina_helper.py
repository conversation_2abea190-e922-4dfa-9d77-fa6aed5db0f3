import os
import requests
import json

from stream_app.common.config import fetch_config
from stream_app.services.Framework.multi_agent_framework import async_initiate_chat
from stream_app.helpers.dynamo_helper import get_item_by_primary_key


def get_client_details(chat_id, phone_number, business_id):
    """
    Get client details
    """
    body = {
        "business_id": business_id,
        "phone_number": phone_number,
        "chat_id": chat_id,
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/get_client_account"
    if os.environ["FASTAPI_ENV"] == "demo":
        url = "https://brook-demo.dev-scc-demo.rozie.ai/phorest/get_client_account"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return response_json
        except Exception as e:
            print(str(e))
            return "Failed"


def create_async_initiate_payload(
    call_sid,
    from_phone_number,
    roster_id,
    to_number,
    called_via_number,
    welcome_message_flag="default",
    channel_id="VC",
    channel_name="voice",
):
    """
    Creates a payload for the async request.

    :param text: The message text to include in the request.
    :param call_sid: The unique chat identifier.
    :return: A dictionary representing the payload, or None if an error occurs.
    """
    user_info = {
        "PhoneNumber": from_phone_number[-10:],
        "ToPhoneNumber": to_number,
        "CalledViaNumber": called_via_number,
        "WelcomeMessageFlag": welcome_message_flag
    }
    llm_context = {
        "Customer's Dialed Number": from_phone_number[-10:],
    }

    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": call_sid, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": {**user_info},
        },
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "initiate_event",
                "event_user": {
                    "user_id": {
                        "id": call_sid,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {
                        **user_info,
                    },
                },
                "event_template": {
                    "event_type": "initiate",
                    "rosters_id": roster_id,
                    "llm_context": llm_context,
                    "user_context": user_info,
                }
            }
        ]
    }


def nina_initiate_call(call_sid, from_number, to_number, called_via_number, config_id, agent_name=None):
    business_config = None
    business_config = get_item_by_primary_key(
        f"multi-agent-framework-stream-resources-business-config-{os.getenv('FASTAPI_ENV')}",
        "config_id",
        config_id,
    )
    welcome_message_flag = "default"
    client_id = None
    client_name = None
    roster_id = business_config.get("roster_config").get("voice")
    application_id = business_config.get("application_id")

    # TODO: Get client details from rozie air api

    payload = create_async_initiate_payload(
        call_sid,
        from_number,
        roster_id,
        to_number,
        called_via_number,
        welcome_message_flag
    )

    if agent_name:
        payload["incoming_events"][0]["event_template"]["selected_workflow"] = agent_name
    chat_id = async_initiate_chat(call_sid, from_number, roster_id, payload, application_id)

    return {
        "application_id": application_id,
        "chat_id": chat_id
    }
