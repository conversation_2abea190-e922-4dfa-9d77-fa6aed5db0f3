"""
This helper contains utility function for main handler
"""

import os
import urllib.parse
from twilio.twiml.voice_response import (
    VoiceResponse,
    Hangup,
    Dial,
)
from stream_app.helpers.dynamo_helper import (
    put_ddb_table_item,
    update_ddb_table_item,
    get_item_by_primary_key,
    remove_ddb_table_item_attribute,
)
from stream_app.helpers.logging_helper import print_info_log

os.environ["TWILIO_CONTACT_ATTRIBUTE_TABLE"] = "twilio-contact-attributes-dev"


def get_contact_attributes(contact_id):
    """
    This function returns the list of attribute stored for the contact
    """

    item = get_item_by_primary_key(
        os.getenv("TWILIO_CONTACT_ATTRIBUTE_TABLE"), "contact_id", contact_id
    )

    return item


def update_contact_attribute(contact_id, attributes):
    """
    This function updates the attribute list if contact id already present
    or puts as entry is not present.
    """

    if not attributes.get("contact_id"):
        attributes["contact_id"] = contact_id

    result, key_dict = put_ddb_table_item(
        os.getenv("TWILIO_CONTACT_ATTRIBUTE_TABLE"), attributes, "contact_id"
    )

    if result == "Update":
        update_ddb_table_item(
            os.getenv("TWILIO_CONTACT_ATTRIBUTE_TABLE"), key_dict, attributes
        )


def reset_contact_attributes(contact_id, attribute_list):
    """
    This function resent/deletes the contact attribute provided in list.
    """
    contact_attributes = get_contact_attributes(contact_id)
    key_dict = {"contact_id": contact_id}
    for attribute in attribute_list:
        if attribute not in contact_attributes:
            attribute_list.remove(attribute)
    remove_ddb_table_item_attribute(
        os.getenv("TWILIO_CONTACT_ATTRIBUTE_TABLE"), key_dict, attribute_list
    )


def parse_twilio_body(body) -> dict:
    """
    This function parse body passed by twilio in str format to dict
    """
    parsed_body = urllib.parse.parse_qs(body, keep_blank_values=True)
    for key, value in parsed_body.items():
        parsed_body[key] = value[0]
    print_info_log("", "parsed_body : response", parsed_body)
    return parsed_body


def transfer_call(number, voice_response: VoiceResponse = None):
    """
    This function transfer the call to the provided number
    """
    if not voice_response:
        voice_response = VoiceResponse()
    dial = Dial()
    dial.number(number)
    voice_response.append(dial)
    voice_response.append(Hangup())
    return voice_response
