"""
This module contains DynamoDB helper functions used in the lambda functions.

Functions:
 - put_record_into_dynamodb
 - get_item_by_primary_key
"""

import logging
import boto3
from botocore.exceptions import ClientError
from stream_app.helpers.logging_helper import print_info_log
dynamodb = boto3.resource("dynamodb")


def put_record_into_dynamodb(table_name, record):
    """
    puts item in table
    """
    # Get the DynamoDB table
    table = dynamodb.Table(table_name)
    # Put record into DynamoDB table
    try:
        response = table.put_item(Item=record)
        logging.info("Record added successfully:%s ", response)
    except Exception as e:
        logging.info("Error adding record: %s", e)

def get_item_by_primary_key(table, primary_key_id, primary_key_value):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table (string) : table name
        primary_key_id (string): The primary key attribute name of the item to get from the table.
        primary_key_value (string): The primary key value of the item to get from the table.

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    table = dynamodb.Table(table)
    try:
        key = {primary_key_id: primary_key_value}
        response = table.get_item(Key=key)
        return response.get("Item", {})
    except Exception as error:
        logging.info("Error while getting item from %s ", table)
        raise error

def update_ddb_table_item(table_name, key_dict, item_dict):
    """
    update a specific item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): key dictionary
        item_dict (dict): dictionary of attribute and value to update

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        for _key in key_dict:
            del item_dict[_key]

        update_expression = ",".join(
            list(map(lambda key: f"{key} = :{key}", list(item_dict.keys())))
        )  # converts keys in 'key = :key' format
        expression_attribute_values = {}
        for key in item_dict:
            expression_attribute_values[f":{key}"] = item_dict[key]
        param = {
            "Key": key_dict,
            "UpdateExpression": f"SET {update_expression}",
            "ExpressionAttributeValues": expression_attribute_values,
        }
        table.update_item(**param)
        return "Success"
    except Exception as exception:
        logging.info("Error while putting item from table")
        logging.info(exception)
        return "Error"

def put_table_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        String : A string indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        condition_expression = f"attribute_not_exists({primary_key})"

        if sort_key:
            condition_expression = f"attribute_not_exists({primary_key}) and attribute_not_exists({sort_key})"
        param = {"Item": item_dict, "ConditionExpression": condition_expression}
        table.put_item(**param)
        return "Success", {}
    except ClientError as exception:
        if exception.response["Error"]["Code"] == "ConditionalCheckFailedException":
            key_dict = {primary_key: item_dict[primary_key]}
            if sort_key:
                key_dict[sort_key] = item_dict[sort_key]
            logging.info("key found in table")
            return "Update", key_dict
        logging.info("Error while putting in table")
        return "Error", {}

def create_table(table_configuration):

    dynamodb = boto3.client("dynamodb")
    try:
        response = dynamodb.create_table(**table_configuration)
        return "Success"
    except ClientError as exception:
        if exception.response["Error"]["Code"] == "ResourceInUseException":
            return "Table_Exists"
        logging.info("Error while creating in table")
        logging.info(str(exception))
        return "Error"

def upsert_item(table_name, item_dict, primary_key, sort_key=None):

    status, key_dict = put_table_item(table_name, item_dict, primary_key, sort_key)

    if status == "Update":
        status = update_ddb_table_item(table_name, key_dict, item_dict) 

    return status
def retrieve_dynamo_table_data_with_params(params):
    """
    Retrieves data from a DynamoDB table with specified parameters.

    Parameters:
    - params (dict): A dictionary containing parameters for the DynamoDB scan operation.

    Returns:
    - list: A list containing items retrieved from the DynamoDB table.
    """
    try:
        logging.info('function: retrieveDynamoTableDataWithParams')
        table = dynamodb.Table(params['TableName'])
        # Remove TableName from params as it's not needed in scan operation
        scan_params = {k: v for k, v in params.items() if k != 'TableName'}
        response = table.scan(**scan_params)
        logging.info('response => %s', response)
        return response.get("Items", [])
    except Exception as e:
        logging.error("Error in retrieveDynamoTableDataWithParams: %s", str(e))
        raise e
    
def put_ddb_table_item(table_name, item_dict, primary_key, sort_key=None):
    """
    Gets an item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        item_dict (dict): dictionary of attribute and value to update
        primary_key: "primary key to check if item exists"
        sort_key: "sort key to check if item exists"

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)
        condition_expression = f"attribute_not_exists({primary_key})"
        if sort_key:
            condition_expression = f"attribute_not_exists({primary_key}) and attribute_not_exists({sort_key})"
        param = {"Item": item_dict, "ConditionExpression": condition_expression}
        table.put_item(**param)
        return "Success", {}
    except ClientError as exception:
        if exception.response["Error"]["Code"] == "ConditionalCheckFailedException":
            key_dict = {primary_key: item_dict[primary_key]}
            if sort_key:
                key_dict[sort_key] = item_dict[sort_key]
            print_info_log("WARN", "put_table_item", f"key found in table {table_name}")
            return "Update", key_dict
        print_info_log(
            "ERROR",
            "put_table_item",
            f"Error while added {str(item_dict)} in table {table_name}: {str(exception)}",
        )
        return "Error", {}
    
def remove_ddb_table_item_attribute(table_name, key_dict, attributes_to_remove):
    """
    update a specific item from the specified DynamoDB table.

    Args:
        table_name (string): The DynamoDB table name.
        key_dict (dict): key dictionary
        item_dict (dict): dictionary of attribute and value to update

    Returns:
        dict: A dictionary indicating the success or failure of the operation.
    """
    try:
        table = dynamodb.Table(table_name)

        update_expression = ",".join(attributes_to_remove)
        param = {"Key": key_dict, "UpdateExpression": f"REMOVE {update_expression}"}
        table.update_item(**param)
        return "Success"
    except ClientError as exception:
        print_info_log(
            "ERROR",
            "update_table_item",
            f"Error while removing {str(attributes_to_remove)} in table {table_name}: {str(exception)}",
        )
        return "Error"