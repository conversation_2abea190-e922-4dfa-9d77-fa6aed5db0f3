import os
import requests
import json

from stream_app.common.config import fetch_config
from stream_app.services.Framework.multi_agent_framework import (
    async_initiate_chat,
    sync_send_message,
    sync_send_initiate_message,
)
from stream_app.helpers.dynamo_helper import get_item_by_primary_key, put_ddb_table_item

import time
from twilio.twiml.voice_response import VoiceResponse, Say
from twilio.twiml.messaging_response import MessagingResponse, Message
from stream_app.helpers.logging_helper import print_info_log
from stream_app.helpers.twilio_util import (
    get_contact_attributes,
    update_contact_attribute,
    parse_twilio_body,
)


def get_client_details(chat_id, phone_number, business_id):
    """
    Get client details
    """
    body = {
        "business_id": business_id,
        "phone_number": phone_number,
        "chat_id": chat_id,
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/get_client_account"
    if os.environ["FASTAPI_ENV"] == "demo":
        url = "https://brook-demo.dev-scc-demo.rozie.ai/phorest/get_client_account"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return response_json
        except Exception as e:
            print(str(e))
            return "Failed"


def call_escalation(chat_id, business_id, branch_name):
    """
    Get client details
    """
    body = {
        "business_id": business_id,
        "branch_name": branch_name,
        "chat_id": chat_id,
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/is_salon_open"
    if os.environ["FASTAPI_ENV"] == "demo":
        url = "https://brook-demo.dev-scc-demo.rozie.ai/phorest/is_salon_open"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return False
            response_json = response.json()

            return response_json.get("response").get("is_open")
        except Exception as e:
            print(str(e))
            return False


def create_async_initiate_payload(
    call_sid,
    from_phone_number,
    roster_id,
    business_id,
    client_id,
    first_name,
    last_name,
    to_number,
    called_via_number,
    welcome_message_flag="default",
    salon_location="Prospect Heights",
    channel_id="VC",
    channel_name="voice",
):
    """
    Creates a payload for the async request.

    :param text: The message text to include in the request.
    :param call_sid: The unique chat identifier.
    :return: A dictionary representing the payload, or None if an error occurs.
    """
    user_info = {
        "PhoneNumber": from_phone_number[-10:],
        "ToPhoneNumber": to_number,
        "CalledViaNumber": called_via_number,
        "BusinessId": business_id,
        "WelcomeMessageFlag": welcome_message_flag,
        "Location": salon_location,
    }
    llm_context = {
        "Customer's Dialed Location": salon_location,
        "Customer's Dialed Number": from_phone_number[-10:],
    }
    if client_id is not None:
        user_info["UserName"] = first_name
        user_info["FirstName"] = first_name
        user_info["LastName"] = last_name
        user_info["ClientId"] = client_id
        user_info["llmContext"]["Customer's Name"] = first_name

    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": call_sid, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": {**user_info},
        },
        "channel": {
            "channel_id": channel_id,
            "channel_name": channel_name,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "initiate_event",
                "event_user": {
                    "user_id": {
                        "id": call_sid,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {
                        **user_info,
                    },
                },
                "event_template": {
                    "event_type": "initiate",
                    "rosters_id": roster_id,
                    "llm_context": llm_context,
                    "user_context": user_info,
                },
                # "event_template": {
                #     "event_type": "initiate",
                #     "rosters_id": ROSTER_ID,
                #     "selected_workflow": "Knowledge_base",
                # },
            }
        ],
    }


def brook_initiate_call(
    call_sid, from_number, to_number, called_via_number, config_id, agent_name=None
):
    business_config = None
    business_config = get_item_by_primary_key(
        f"multi-agent-framework-stream-resources-business-config-{os.getenv('FASTAPI_ENV')}",
        "config_id",
        config_id,
    )
    business_id = business_config.get("BUSINESS_ID")
    welcome_message_flag = "default"
    client_id = None
    first_name = None
    last_name = None
    customer_handler = "ai"

    phone_number_transferred_type = business_config.get(
        "phone_number_transferred_type", "to_number"
    )
    print_info_log("", "phone_number_transferred_type", phone_number_transferred_type)
    lookup_number = (
        called_via_number
        if phone_number_transferred_type == "called_via_number"
        else to_number
    )
    print_info_log("", "lookup_number", lookup_number)
    salon_location = business_config.get("default_called_location", "")
    branch_config = business_config.get("branch_config", [])
    call_forwarding_number = None
    print_info_log("", "branch_config", branch_config)
    # Look up the location in branch_config based on assigned_number
    for branch in branch_config:
        if branch.get("assigned_number") == lookup_number:
            salon_location = branch.get("location_name")
            call_forwarding_number = branch.get("transfer_number")
            print_info_log("", "salon_location", salon_location)
            break
    print_info_log("", "salon_location", salon_location)
    roster_id = business_config.get("roster_config").get("voice")

    client_details = get_client_details(call_sid, from_number, business_id)
    if client_details.get("status") != "error":
        welcome_message_flag = "profile_found"
        client_id = client_details.get("client_id")
        first_name = client_details.get("firstName")
        last_name = client_details.get("lastName")
    else:
        customer_handler = business_config.get("new_customer_handler", "ai")
        if not (
            customer_handler == "human"
            and call_escalation(call_sid, business_id, salon_location)
        ):
            customer_handler = "ai"
    chat_id = None
    if customer_handler == "ai":
        print_info_log("", "customer_handler", customer_handler)
        payload = create_async_initiate_payload(
            call_sid,
            from_number,
            roster_id,
            business_id,
            client_id,
            first_name,
            last_name,
            to_number,
            called_via_number,
            welcome_message_flag,
            salon_location,
        )
        if agent_name:
            payload["incoming_events"][0]["event_template"][
                "selected_workflow"
            ] = agent_name
        chat_id = async_initiate_chat(call_sid, from_number, roster_id, payload, "Brook_App_ID")
    return {
        "transfer_number": call_forwarding_number,
        "customer_handler": customer_handler,
        "chat_id": chat_id
    }
