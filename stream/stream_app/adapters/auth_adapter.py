from fastapi import HTT<PERSON>Exception, Depends, Security, status
from fastapi.security import APIKeyHeader
import os

API_KEY = os.environ.get("APP_API_KEY", "Test@123")
API_KEY_NAME = "access_token"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)


async def get_api_key(api_key_header: str = Security(api_key_header)):
    if api_key_header == API_KEY:
        return api_key_header
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
