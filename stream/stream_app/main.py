from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from stream_app.common.constants import ALLOWED_ORIGINS
from stream_app.routers import twilio_onboarding, twilio_voice
from stream_app.helpers.logging_helper import get_log_level, set_logging_level
from stream_app import gpt_direct
from stream_app.twilio_pipecat import server

app = FastAPI()
set_logging_level(get_log_level())

# Middleware for CORS handling
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS.split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the WebSocket router for handling Twilio streams
app.include_router(
    twilio_voice.voice_router, prefix="/twilio/voice", tags=["WebSocket"]
)
# app.include_router(
#     twilio_onboarding.onboarding_router, prefix="/onboarding", tags=["Onboarding"]
# )
app.include_router(gpt_direct.gpt_direct, prefix="/gpt-direct", tags=["GPT Direct"])
app.include_router(server.pipecat_router, prefix="/pipecat", tags=["pipecat"])


@app.get("/readiness")
async def readiness_check():
    """
    Readiness endpoint to confirm the application is up and running.
    This can be used for container health checks in AWS ECS/EKS.
    """
    try:
        # Add any specific health-check logic here, such as verifying database connections
        # or confirming critical services are operational.
        health_status = {"status": "healthy", "details": "Application is ready"}
        return JSONResponse(content=health_status, status_code=200)
    except Exception as e:
        # If there's an issue, return a non-200 status code and error details
        error_status = {"status": "unhealthy", "details": str(e)}
        return JSONResponse(content=error_status, status_code=500)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=80, debug=True)
