import os
from deepgram import (
    DeepgramClient,
    LiveTranscriptionEvents,
    LiveOptions,
    DeepgramClientOptions
)
from queue import Queue
from stream_app.helpers.logging_helper import print_info_log
from stream_app.common.config import get_stt_config 

def on_open(self, open, **kwargs):
    conversation_id = kwargs.get("conversation_id", "")
    print_info_log(conversation_id, "on_open", open)


def on_message(self, result, **kwargs):
    queue = kwargs.get("queue")

    if queue:
        sentence = result.channel.alternatives[0].transcript
        if len(sentence.strip()):
            queue.put({"event": "on_speech_started", "data": sentence})
        if sentence and result.is_final:
            queue.put({"event": "on_message", "data": result})


def on_metadata(self, metadata, **kwargs):
    conversation_id = kwargs.get("conversation_id", "")
    print_info_log(conversation_id, "on_metadata", metadata)


def on_speech_started(self, speech_started, **kwargs):
    conversation_id = kwargs.get("conversation_id", "")
    print_info_log(conversation_id, "on_speech_started", speech_started)


def on_utterance_end(self, utterance_end, **kwargs):
    queue = kwargs.get("queue")
    if queue:
        queue.put({"event": "on_utterance_end", "data": utterance_end})


def on_close(self, close, **kwargs):
    queue = kwargs.get("queue")
    if queue:
        queue.put({"event": "on_close", "data": close})


def on_error(self, error, **kwargs):
    queue = kwargs.get("queue")
    if queue:
        queue.put({"event": "on_error", "data": error})


def on_unhandled(self, unhandled, **kwargs):
    conversation_id = kwargs.get("conversation_id", "")
    print_info_log(conversation_id, "on_unhandled", unhandled)


class DeepgramSpeechToText:
    def __init__(self, event_queue: Queue, conversation_id: str):
        self.event_queue = event_queue
        self.conversation_id = conversation_id
        config = DeepgramClientOptions(
            options={"keepalive": "true"}
        )
        self.deepgram = DeepgramClient(get_stt_config("DEEPGRAM_API_KEY"), config)
        self.dg_connection = self.deepgram.listen.websocket.v("1")

    def setup_event_handlers(self):
        self.dg_connection.on(LiveTranscriptionEvents.Open, on_open)
        self.dg_connection.on(LiveTranscriptionEvents.Transcript, on_message)
        self.dg_connection.on(LiveTranscriptionEvents.Metadata, on_metadata)
        self.dg_connection.on(LiveTranscriptionEvents.SpeechStarted, on_speech_started)
        self.dg_connection.on(LiveTranscriptionEvents.UtteranceEnd, on_utterance_end)
        self.dg_connection.on(LiveTranscriptionEvents.Close, on_close)
        self.dg_connection.on(LiveTranscriptionEvents.Error, on_error)
        self.dg_connection.on(LiveTranscriptionEvents.Unhandled, on_unhandled)

    def start_transcription(self):
        try:
            self.setup_event_handlers()
            options = LiveOptions(
                encoding= get_stt_config('ENCODING'),
                sample_rate= int(get_stt_config('SAMPLE_RATE')),
                model= get_stt_config('TRANSCRIPTION_MODEL'),
                interim_results= bool(get_stt_config('TRANSCRIPTION_INTERIM_RESULTS')),
                endpointing= int(get_stt_config('TRANSCRIPTION_ENDPOINTING')),
                utterance_end_ms= int(get_stt_config('TRANSCRIPTION_UTTERANCE_END_MS')),
                language= get_stt_config('TRANSCRIPTION_LANGUAGE'),
                smart_format= bool(get_stt_config('SMART_FORMAT'))
            )
            _start = self.dg_connection.start(
                options=options, queue=self.event_queue, conversation_id=self.conversation_id
            )
            if not _start:
                print_info_log("", "Event", "Failed to start connection")
                return

        except Exception as e:
            print_info_log("", "Event", f"Could not start transcription: {e}")

    def stop_transcription(self):
        self.dg_connection.finish()
        print_info_log("", "Event", "Finished")
