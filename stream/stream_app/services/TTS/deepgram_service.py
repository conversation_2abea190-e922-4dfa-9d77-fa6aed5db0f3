import os
import requests
import base64
from deepgram import (
    DeepgramClient
)
from queue import Queue
from stream_app.helpers.logging_helper import print_info_log
from stream_app.helpers.decorators import measure_execution_time
from stream_app.common.config import get_tts_config

class DeepgramTextToSpeech:
    def __init__(self, event_queue: Queue):
        self.event_queue = event_queue
        self.deepgram_api_key = os.getenv("DEEPGRAM_API_KEY")
        self.voice_model = get_tts_config("VOICE_MODEL") #os.getenv("VOICE_MODEL", "aura-stella-en")
        self.base_url = "https://api.deepgram.com/v1/speak"
        self.text_replace = get_tts_config("TEXT_REPLACE")

    @measure_execution_time
    def synthesize_speech(self, text, next_action):
        """
        Converts text response to audio using Deepgram TTS.
        :param text:response text.
        """
        try:
            formatted_text = self.format_text(text)
            formatted_text = self.format_reply(formatted_text)
            try:
                response = requests.post(
                    f"{self.base_url}?model={self.voice_model}&encoding=mulaw&sample_rate=8000&container=none",
                    headers={
                        "Authorization": f"Token {self.deepgram_api_key}",
                        "Content-Type": "application/json",
                    },
                    json={"text": formatted_text},
                )
                print_info_log("", "APIRequestSuccess", "Deepgram TTS API request successful.")
                if response.status_code == 200:
                    try:
                        audio_array_buffer = response.content
                        silence_buffer = bytes([0xFF] * 8 * int(get_tts_config("SILENCE_TIME_MS")))  
                        audio_array_buffer += silence_buffer
                        base64_string = base64.b64encode(audio_array_buffer).decode('ascii')
                        mark = ''
                        if next_action == 'say':
                            mark = f"say_{text}"
                        elif next_action == 'gather':
                            mark = f"gather_{text}"
                        elif next_action == 'disconnect':
                            mark = f"disconnect_{text}"
                        self.event_queue.put({
                            "data": base64_string,
                            "mark": mark,
                        })
                        print_info_log("", "ProcessSuccess", "Speech put on the queue successfully.")
                    except Exception as e:
                        print_info_log("", "AudioProcessingError", f"Failed to generate speech response: {e}")
                else:
                    print_info_log("", "APIError", f"Deepgram TTS API error: {response.text}")
            except Exception as e:
                print_info_log("", "APIRequestError", f"Failed during Deepgram TTS request: {e}")
                return False
        except Exception as e:
            print_info_log("", "ProcessFailure", f"TTS generation process failed: {e}")
            return False

    def format_text(self, input_string: str) -> str:
        """
        Prepares and formats the input text to enhance audio output quality.
        :param input_string: The input text string.
        :return: Formatted text string.
        """
        try:
            formatted = input_string.replace(".", ". .").replace(",", ". ")
            formatted = formatted.replace("and", ". and .").replace("or", ". or .")
            formatted = formatted.replace("!", "! . ").replace(":", ": . .")
            formatted = formatted.replace(";", "? , .").replace(" - ", "-")

            print_info_log("", "FormattingSuccess", "Input text formatted successfully.")
            return formatted
        except Exception as e:
            print_info_log("", "FormattingError", f"Failed to format input text: {e}")
            raise
    
    def format_reply(self, input_string):
        for k, v in self.text_replace.items():
            input_string = input_string.replace(k, v)
        return input_string
