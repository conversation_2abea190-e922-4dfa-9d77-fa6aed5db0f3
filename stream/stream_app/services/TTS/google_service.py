import os
import base64
from queue import Queue
from google.cloud import texttospeech
from stream_app.helpers.logging_helper import print_info_log
from stream_app.helpers.decorators import measure_execution_time
from stream_app.common.config import get_tts_config

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = (
    "stream_app/google_Auth/google_text_to_speech_service_cred.json"
)


class GoogleTextToSpeech:
    def __init__(self, event_queue: Queue):
        self.event_queue = event_queue
        self.google_TTS_client = texttospeech.TextToSpeechClient()
        self.voice_config = None
        self.audio_config = None
        self.text_replace = get_tts_config("TEXT_REPLACE")
        self.setup()

    def setup(self):
        self.voice_property = texttospeech.VoiceSelectionParams(
            language_code= get_tts_config("LANGUAGE_CODE"),
            name= get_tts_config('GOOGLE_TTS_VOICE')
        )

        self.audio_config = texttospeech.AudioConfig(
            audio_encoding=texttospeech.AudioEncoding.MULAW,
            effects_profile_id=[
                get_tts_config('GOOGLE_TTS_EFFECTS_PROFILE')
            ],
            speaking_rate= float(get_tts_config('GOOGLE_TTS_SPEAKING_RATE')), 
            pitch= float(get_tts_config('GOOGLE_TTS_PITCH')), 
            sample_rate_hertz=  int(get_tts_config('GOOGLE_TTS_SAMPLE_RATE_HERTZ')) 
        )

    @measure_execution_time
    def synthesize_speech(self, text, next_action):
        try:
            text = self.format_reply(text)
            print_info_log("", "AudioProcessing", f"Text to be synthesized: {text}")
            synthesis_input = texttospeech.SynthesisInput(text = text)
            response = self.google_TTS_client.synthesize_speech(
                input=synthesis_input,
                voice=self.voice_property,
                audio_config=self.audio_config,
            )
            audio_base64 = response.audio_content
            silence_buffer = bytes([0xFF] * 8 * int(get_tts_config("SILENCE_TIME_MS")))  
            trimmed_audio = audio_base64[58:]
            trimmed_audio += silence_buffer
            re_encoded_audio = base64.b64encode(trimmed_audio).decode('ascii')
            mark = None
            if next_action == "say":
                mark = f"say_{text}"
            elif next_action == "gather":
                mark = f"gather_{text}"
            elif next_action == "disconnect":
                mark = f"disconnect_{text}"
            elif next_action == "transfer":
                mark = f"transfer_{text}"
            self.event_queue.put({"data": re_encoded_audio, "mark": mark})
        except Exception as e:
            print_info_log(
                "", "AudioProcessingError", f"Failed to generate speech response: {e}"
            )

    def format_reply(self, input_string):
        for k, v in self.text_replace.items():
            input_string = input_string.replace(k, v)
        return input_string
