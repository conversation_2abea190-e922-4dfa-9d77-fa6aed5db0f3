import os
import time
import async<PERSON>
from queue import Queue
import json
from concurrent.futures import ThreadPoolExecutor
import threading

from stream_app.helpers.logging_helper import print_info_log
from stream_app.services.STT.deepgram_service import DeepgramSpeechToText
from stream_app.services.TTS.deepgram_service import DeepgramTextToSpeech
from stream_app.services.TTS.google_service import GoogleTextToSpeech
from stream_app.services.Framework.multi_agent_framework import (
    async_send_request,
    async_get_response,
    async_end_chat
)
from stream_app.assets.typing_sound import typing_sound
from stream_app.common.config import get_stt_config, get_tts_config

class Manager:
    def __init__(self, conversation_id, call_sid, websocket, application_id):
        self.text_to_speech_queue = Queue()
        self.speech_to_text_queue = Queue()
        self.application_id = application_id
        self.conversation_id = conversation_id
        self.call_sid = call_sid
        self.websocket = websocket
        self.stream_id = ""
        self.ideal_timer =  None
        self.idle_count = 0 
        self.max_idle_count = int(get_stt_config("MAX_CONSECUTIVE_IDLE_MESSAGES_COUNT"))
        self.waiting_music = False
        self.call_complete = False

        self.STT_provider = get_stt_config("STT_PROVIDER")
        self.TTS_provider = get_tts_config("TTS_PROVIDER")
        self.WAITING_MUSIC_FLAG = get_tts_config("WAITING_MUSIC_FLAG")

        self.speech_to_text_provider = ["deepgram"]
        self.text_to_speech_provider = ["deepgram", "google"]

        self.thread_executor = ThreadPoolExecutor()
        self.init()

        self.chat_mode = "Say"
        self.STT_text = ""
        self.framework_response = time.time()

        loop = asyncio.get_event_loop()
        loop.run_in_executor(
            self.thread_executor, self.process_text_to_speech_queue_thread
        )
        loop.run_in_executor(
            self.thread_executor, self.process_speech_to_text_queue_thread
        )
        loop.run_in_executor(self.thread_executor, self.play_agent_response_thread)

    def init(self):
        if self.STT_provider not in self.speech_to_text_provider:
            raise ValueError(
                f"Speech To Text provider '{self.STT_provider}' is not supported"
            )
        if self.TTS_provider not in self.text_to_speech_provider:
            raise ValueError(
                f"Text to Speech provider '{self.TTS_provider}' is not supported"
            )
        if self.STT_provider == "deepgram":
            self.STT_service = DeepgramSpeechToText(
                self.speech_to_text_queue, self.conversation_id
            )
        if self.TTS_provider == "deepgram":
            self.TTS_service = DeepgramTextToSpeech(self.text_to_speech_queue)
        if self.TTS_provider == "google":
            self.TTS_service = GoogleTextToSpeech(self.text_to_speech_queue)

    async def speech_to_text(self):
        while not self.call_complete:
            if self.speech_to_text_queue.empty():
                await asyncio.sleep(0.1)
            result = self.speech_to_text_queue.get()
            if result["event"] == "on_message":
                sentence = result["data"].channel.alternatives[0].transcript
                if sentence:
                    self.STT_text += sentence
                    print_info_log(self.conversation_id, "sentence", sentence)
            elif result["event"] == "on_utterance_end":
                print_info_log(self.conversation_id, "utterance_end", result["data"])
                print_info_log(self.conversation_id, "#time_log STT speech completed", [time.time(), len(self.STT_text), self.STT_text])
                if self.chat_mode == "Gather":
                    if self.WAITING_MUSIC_FLAG:
                        self.text_to_speech_queue.put(
                                {
                                    "data": typing_sound,
                                    "mark": "waiting_music",
                                }
                            )
                        self.waiting_music = True
                    async_send_request(self.call_sid, self.STT_text, self.application_id)
                    self.framework_response = time.time()
                    self.chat_mode = "Say"
                    self.STT_text = ""
            elif result["event"] == "on_speech_started":
                print_info_log(self.conversation_id, "speech_started", result["data"])
                print_info_log(self.conversation_id, "#time_log STT speech detected started transcribing", time.time())
                self.stop_idle_check()
            

    async def text_to_speech(self):
        while not self.call_complete:
            if self.chat_mode == "Say":
                response = async_get_response(self.conversation_id, self.application_id)
                if response and response.get("next_action") == "wait":
                    await asyncio.sleep(0.1)
                    continue
                elif response and response.get("next_action") == "disconnect":
                    self.calculate_framework_response_time()
                    self.call_complete = True
                    self.chat_mode = response.get("next_action").capitalize()
                    print_info_log("", "response", f"async_get_response for call SID {self.conversation_id}: {response}")
                    print_info_log(self.conversation_id, "#time_log framework_event received:disconnect", time.time())
                    self.TTS_service.synthesize_speech(response.get("response"), response.get("next_action"))
                elif response and response.get("next_action") == "transfer":
                    self.calculate_framework_response_time()
                    self.call_complete = True
                    self.chat_mode = response.get("next_action").capitalize()
                    print_info_log("", "response", f"async_get_response for call SID {self.conversation_id}: {response}")
                    print_info_log(self.conversation_id, "#time_log framework_event received:transfer", time.time())
                    self.TTS_service.synthesize_speech(response.get("response"), response.get("next_action"))
                elif (
                    response.get("next_action") == "say"
                ):
                    self.calculate_framework_response_time()
                    print_info_log("", "response", f"async_get_response for call SID {self.conversation_id}: {response}")
                    print_info_log(self.conversation_id, "#time_log framework_event received:say", time.time())
                    self.TTS_service.synthesize_speech(response.get("response"), response.get("next_action"))
                    self.chat_mode = response.get("next_action").capitalize()
                elif(
                    response.get("next_action") == "gather"
                ):
                    self.calculate_framework_response_time()
                    print_info_log("", "response", f"async_get_response for call SID {self.conversation_id}: {response}")
                    print_info_log(self.conversation_id, "#time_log framework_event received:gather", time.time())
                    text = response.get("response").strip()
                    if text:
                        self.TTS_service.synthesize_speech(response.get("response"), response.get("next_action"))
                    else:
                        self.chat_mode = response.get("next_action").capitalize()
            else:
                await asyncio.sleep(0.1)

    def process_speech_to_text_queue_thread(self):
        asyncio.run(self.speech_to_text())

    def process_text_to_speech_queue_thread(self):
        asyncio.run(self.text_to_speech())

    def play_agent_response_thread(self):
        asyncio.run(self.send_to_twilio())

    def start(self):
        self.STT_service.start_transcription()

    def stop(self):
        self.STT_service.stop_transcription()
        async_end_chat(self.conversation_id, self.application_id)
        self.call_complete = True
        print_info_log(self.conversation_id, "call complete", "call complete")

    def send_to_STT(self, chunk):
        if self.chat_mode == "Gather":
            self.STT_service.dg_connection.send(chunk)

    async def send_to_twilio(self):
        while not self.call_complete:
            if self.text_to_speech_queue.empty():
                await asyncio.sleep(0.1)
            result = self.text_to_speech_queue.get()

            payload = {
                "event": "media",
                "streamSid": self.stream_id,
                "media": {
                    "payload": result.get("data"),
                },
            }
            mark_payload = {
                "event": "mark",
                "streamSid": self.stream_id,
                "mark": {
                    "name": result.get("mark"),
                },
            }
            clear_payload ={"event": "clear", "streamSid": self.stream_id} 
            try:
                if self.waiting_music and result.get("mark") != "waiting_music":
                    print("event sent on websocket clear", clear_payload.get("event"))
                    await self.websocket.send_json(clear_payload)
                    self.waiting_music = False
                print("event sent on websocket media", payload.get("event"))
                await self.websocket.send_json(payload)
                print("event sent on websocket mark", mark_payload.get("event"), mark_payload.get("mark").get("name"))
                await self.websocket.send_json(mark_payload)
            except Exception as e:
                print("error in websocket", e)

    def set_stream_id(self, id):
        self.stream_id = id

    def idle_check(self):
        """Handles the idle check and increments the counter."""
        self.idle_count += 1
        print(f"Idle check triggered {self.idle_count} times consecutively.")

        if self.idle_count >= self.max_idle_count:
            self.TTS_service.synthesize_speech(get_stt_config("NO_RESPONSE_DISCONNECT_MESSAGE"), 'disconnect') 
            self.idle_count = 0  
        self.TTS_service.synthesize_speech(get_stt_config("NO_RESPONSE_MESSAGE"), 'gather')
        if self.ideal_timer:
            self.ideal_timer.cancel()
            self.ideal_timer = None

    def start_idle_check(self):
        """Starts the idle check timer."""
        if self.ideal_timer:
            self.stop_idle_check()
        if self.chat_mode == 'Gather':
            self.ideal_timer = threading.Timer(10.0, self.idle_check)
            self.ideal_timer.start()

    def stop_idle_check(self):
        """Stops the idle check timer if running."""
        if self.ideal_timer:
            self.ideal_timer.cancel()
            print("Idle check timer canceled.")
            self.ideal_timer = None
            self.idle_count = 0

    def calculate_framework_response_time(self):
        response_time = time.time() - self.framework_response
        print_info_log(self.conversation_id, "framework response time", f"{response_time:.4f}")