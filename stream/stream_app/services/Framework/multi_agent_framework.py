import os
import json
import requests

from stream_app.helpers.logging_helper import print_info_log, print_error_log
from stream_app.helpers.decorators import measure_execution_time

@measure_execution_time
def async_initiate_chat(call_sid, from_number, roster_id, payload=None, application_id="Test_ID"):
    if payload is None:
        payload = create_async_initiate_payload(call_sid, from_number, roster_id)
    base_url = os.getenv("MULTI_AGENT_FRAMEWORK_URL")
    access_token = os.getenv("MULTI_AGENT_FRAMEWORK_API_KEY")
    url = f"{base_url}chat/v3_async/initiate_chat"
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token,
        "application-id": application_id,
        "rozie-correlation-id": call_sid,
        "channel": "voice",
        "language": "en-US",
    }

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    # Check if the response was successful
    response.raise_for_status()
    data = response.json()
    chat_id = data["chat_id"]
    print_info_log("", "async_initiate_chat", data)
    return chat_id


@measure_execution_time
def async_get_response(call_sid, application_id="Test_ID"):
    try:
        base_url = os.getenv("MULTI_AGENT_FRAMEWORK_URL")
        access_token = os.getenv("MULTI_AGENT_FRAMEWORK_API_KEY")
        url = f"{base_url}chat/v3_async/get_message?chat_id={call_sid}"
        headers = {
            "Content-Type": "application/json",
            "access_token": access_token,
            "application-id": application_id,
            "rozie-correlation-id": call_sid,
            "channel": "voice",
            "language": "en-US",
        }

        response = requests.get(url, headers=headers)

        # Check if the response was successful
        response.raise_for_status()
        data = response.json()

        if data.get("next_action") == "wait":
            return {"response": data.get("message"), "next_action": data.get("next_action")}
        response_template = data.get("response_map", {}).get("responses", {}).get("default", [{}])[0].get("response_template", {})
        return {
            "response": response_template.get("text"),
            "next_action": "transfer" if response_template.get("response_type") == "transfer" else data.get("next_action"),
        }
    except requests.exceptions.RequestException as e:
        print_error_log(call_sid, e)
        return {"response": None, "next_action": "disconnect"}

@measure_execution_time
def async_send_request(call_sid, text, application_id="Test_ID"):
    base_url = os.getenv("MULTI_AGENT_FRAMEWORK_URL")
    access_token = os.getenv("MULTI_AGENT_FRAMEWORK_API_KEY")
    url = f"{base_url}chat/v3_async/send_message"
    headers = {
        "Content-Type": "application/json",
        "access_token": access_token,
        "application-id": application_id,
        "rozie-correlation-id": call_sid,
        "channel": "voice",
        "language": "en-US",
    }

    data = create_async_request_payload(call_sid, text)
    response = requests.post(url, headers=headers, data=json.dumps(data))

    # Check if the response was successful
    response.raise_for_status()
    print_info_log(call_sid, "async_send_request", response.json())
    return response


def create_async_request_payload(call_sid, text):
    """
    Creates a payload for the async request.

    :param text: The message text to include in the request.
    :param call_sid: The unique chat identifier.
    :return: A dictionary representing the payload, or None if an error occurs.
    """
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": call_sid,
                "id_type": "chat_id",
                "id_resource": "chat",
            },
            "user_info": {},
        },
        "channel": {
            "channel_id": "VC",
            "channel_name": "voice",
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "abc-abc-abc",
                "event_user": {
                    "user_id": {
                        "id": call_sid,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {},
                },
                "event_template": {
                    "event_type": "text",
                    "text": text,
                },
            },
        ],
    }


def create_async_initiate_payload(call_sid, from_phone_number, roster_id):
    """
    Creates a payload for the async request.

    :param text: The message text to include in the request.
    :param call_sid: The unique chat identifier.
    :return: A dictionary representing the payload, or None if an error occurs.
    """
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": call_sid,
                "id_type": "chat_id",
                "id_resource": "chat",
            },
            "user_info": {
                "PhoneNumber": from_phone_number,
                "UserName": "Rozie",
                "ClientId": "K_RoZADU-fhz4KkO_F0Iq_nYVQwcfZ9GOXqyGMxZkNM",
                "BusinessId": "Wj6YQfGEsvSqkLskUdhu3g",
                "WelcomeMessageFlag": "profile_found",
                "Location": "Bay Ridge",
                "llmContext": {"Customer's Dialed Location": "Bay Ridge"},
            },
        },
        "channel": {
            "channel_id": "VC",
            "channel_name": "voice",
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "abc-abc-abc",
                "event_user": {
                    "user_id": {
                        "id": call_sid,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {
                        "PhoneNumber": "+***********",
                        "UserName": "Rozie",
                        "ClientId": "K_RoZADU-fhz4KkO_F0Iq_nYVQwcfZ9GOXqyGMxZkNM",
                        "BusinessId": "Wj6YQfGEsvSqkLskUdhu3g",
                        "WelcomeMessageFlag": "profile_found",
                        "Location": "Bay Ridge",
                        "llmContext": {"Customer's Dialed Location": "Bay Ridge"},
                    },
                },
                "event_template": {
                    "event_type": "initiate",
                    "rosters_id": roster_id,
                },
            },
        ],
    }


@measure_execution_time
def async_end_chat(call_sid, application_id="Test_ID"):
    try:
        base_url = os.getenv("MULTI_AGENT_FRAMEWORK_URL")
        access_token = os.getenv("MULTI_AGENT_FRAMEWORK_API_KEY")
        url = f"{base_url}chat/v3_async/end_chat?chatId={call_sid}"
        headers = {
            "Content-Type": "application/json",
            "access_token": access_token,
            "application-id": application_id,
            "rozie-correlation-id": call_sid,
            "channel": "voice",
            "language": "en-US",
        }

        response = requests.post(url, headers=headers)

        return response
    except requests.exceptions.RequestException as e:
        print_error_log(call_sid, e)
        return None

@measure_execution_time
def sync_send_initiate_message(data):
    status, result = sync_post_request(
        url= f"{os.environ.get('MULTI_AGENT_FRAMEWORK_URL')}chat/v3/send_message",
        data=data,
        headers={"access_token": os.environ.get('MULTI_AGENT_FRAMEWORK_API_KEY')},
    )
    print("status", status)
    print("sync_send_message response inside", result)
    responses = result.get("response_map", {}).get("responses", {}).get("default", [])
    reply_message = " ".join(item.get("response_template", {}).get("text", "") for item in responses)
    return reply_message

@measure_execution_time
def sync_send_message(chat_id, phone_number, message, channel= 'voice'):
    status, result = sync_post_request(
        url=f"{os.environ.get('MULTI_AGENT_FRAMEWORK_URL')}chat/v3/send_message",
        data=get_send_message_request(chat_id, phone_number, message, channel),
        headers={"access_token": os.environ.get('MULTI_AGENT_FRAMEWORK_API_KEY')},
    )
    print("sync_send_message response inside", result)
    responses = result.get("response_map", {}).get("responses", {}).get("default", [])
    reply_message = extract_reply_message(responses)
    return reply_message

def extract_reply_message(responses):

    reply_message = ""
    for item in responses:
        response_template = item.get('response_template', {})
        if response_template.get('response_type') == 'text':
            reply_message += f""" {response_template.get('text', "")}"""
        elif response_template.get('response_type') == 'quick_reply':
            quick_replies = response_template.get('items', [])
            for idx, quick_reply in enumerate(quick_replies, 1):
                reply_message += f"""\n{idx} {quick_reply.get('label', '')}"""
    return reply_message

def sync_post_request(url, data=None, headers=None):
    """
    This function is used for invoking the POST method and waits for the result.
    """
    param = {"url": url, "timeout": 30}

    if data:
        param["data"] = json.dumps(data)

    if headers:
        param["headers"] = headers
    else:
        param["headers"] = {}

    try:
        print("sync_post_request param: ", param)
        response = requests.post(**param)
        print("sync_post_request response: ", response)
        if response.status_code != 200:
            print(response.json())
            return "Failed", {}

        response = response.json()
        return "Success", response

    except Exception as e:
        print_error_log("", e)
        return "Failed", str(e)
    
def get_send_message_request(chat_id, phone_number, message, channel_name="voice"):
    user_info = {
        "PhoneNumber": phone_number,
    }

    user_id = {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"}

    send_message_request = {
        "version": "1.0",
        "user_info": {
            "user_id": user_id,
            "user_info": user_info,
        },
        "channel": {
            "channel_id": "VC",
            "channel_name": channel_name,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "abc-abc-abc",
                "event_user": {
                    "user_id": user_id,
                    "user_info": user_info,
                },
                "event_template": {
                    "event_type": "text",
                    "text": message,
                },
            }
        ],
    }
    return send_message_request