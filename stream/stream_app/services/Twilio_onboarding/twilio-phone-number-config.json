{"use_case": "", "stt_config": {"DEEPGRAM_API_KEY": "****************************************", "ENCODING": "mulaw", "hints": "Court, Street, Vanderbilt, Avenue", "NO_RESPONSE_MESSAGE": "With out response, I can not continue.", "SAMPLE_RATE": "8000", "SMART_FORMAT": "true", "STT_PROVIDER": "deepgram", "TRANSCRIPTION_ENDPOINTING": "1250", "TRANSCRIPTION_INTERIM_RESULTS": "true", "TRANSCRIPTION_LANGUAGE": "en-US", "TRANSCRIPTION_MODEL": "nova-2-phonecall", "TRANSCRIPTION_UTTERANCE_END_MS": "2000"}, "tts_config": {"GOOGLE_TTS_EFFECTS_PROFILE": "telephony-class-application", "GOOGLE_TTS_PITCH": "0.0", "GOOGLE_TTS_SAMPLE_RATE_HERTZ": "8000", "GOOGLE_TTS_SPEAKING_RATE": "1.0", "GOOGLE_TTS_VOICE": "en-US-Journey-F", "LANGUAGE_CODE": "en-US", "SILENCE_TIME_MS": "500", "TEXT_REPLACE": {}, "TTS_PROVIDER": "google"}}