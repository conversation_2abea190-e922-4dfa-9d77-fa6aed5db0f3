import os
from twilio.rest import Client
import json

from stream_app.helpers.logging_helper import print_info_log
from stream_app.helpers.dynamo_helper import put_record_into_dynamodb

account_sid = os.environ.get("TWILIO_ACCOUNT_SID")
auth_token = os.environ.get("TWILIO_AUTH_TOKEN")
client = Client(account_sid, auth_token)


def search_available_numbers(country_code="US", area_code=None, limit=10):
    """
    Searches for available Twilio phone numbers in the US.
    :return: A list of available phone numbers.
    """
    try:
        available_numbers = client.available_phone_numbers(country_code).local.list(
            limit=limit, area_code=area_code
        )
        print_info_log(
            "", "AvailableNumbers", f"Available numbers: {available_numbers}"
        )
        return available_numbers

    except Exception as e:
        print_info_log(
            "", "SearchError", f"Failed to search for available numbers: {e}"
        )
        return []


def purchase_twilio_number(phone_number, friendly_name=None):
    """
    Purchases a Twilio phone number.
    :param phone_number: The phone number to purchase.
    :return: The purchased phone number object.
    """
    try:
        purchased_number = client.incoming_phone_numbers.create(
            phone_number=phone_number,
            friendly_name=friendly_name,
            voice_url="https://twilio-stream.dev-scc-demo.rozie.ai/twilio/voice",
        )
        print_info_log("", "PurchaseNumber", f"Purchased number: {purchased_number.phone_number}")
        return purchased_number
    except Exception as e:
        print_info_log("", "PurchaseError", f"Failed to purchase number: {e}")
        return None


def twilio_claim_random_number(country_code="US", area_code=None, friendly_name=None):
    """
    Claims a random Twilio phone number.
    :return: The claimed phone number object.
    """
    available_numbers = search_available_numbers(
        country_code=country_code, area_code=area_code
    )
    if available_numbers:
        phone_number = purchase_twilio_number(
            phone_number=available_numbers[0].phone_number, friendly_name=friendly_name
        )
        return phone_number.phone_number
    else:
        print_info_log("", "ClaimError", "No available numbers found.")
        return None


def twilio_phone_number_set_up(phone_number, business_id, use_case):
    """
    Create business configs and the phone configs
    :input:
        - phone_number
        - business_id
    :return:
        - success or failure
    """
    try:
        if not phone_number or not business_id:
            print_info_log(
                "",
                "PhoneNumberSetUpError",
                f"Failed to set up phone number: {phone_number} or business_id: {business_id}",
            )
            return "failure"
        phone_number_config = get_config("twilio-phone-number-config.json")
        phone_number_config["phone_number"] = phone_number
        phone_number_config["business_id"] = business_id
        phone_number_config["use_case"] = use_case
        put_record_into_dynamodb(
            f"twilio-phone-number-config-{os.getenv('FASTAPI_ENV')}",
            phone_number_config,
        )
        return "Success"
    except Exception as e:
        print_info_log(
            "", "PhoneNumberSetUpError", f"Failed to set up phone number: {e}"
        )
        return "failure"


def get_config(file_path):
    """
    Get the config from the file
    :input:
        - file_path
    :return:
        - config
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, file_path)
    try:
        with open(file_path, "r") as file:
            item = json.load(file)
        return item
    except Exception as e:
        print_info_log("", "GetConfigError", f"Failed to get config: {e}")
        return None
