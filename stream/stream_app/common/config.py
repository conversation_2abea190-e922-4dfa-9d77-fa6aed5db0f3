from stream_app.helpers.dynamo_helper import get_item_by_primary_key

cofiguration = {} 

def fetch_config(table, primary_key_id, primary_key_value):
    global cofiguration 
    config = get_item_by_primary_key(table, primary_key_id, primary_key_value)
    if config:
        cofiguration = config 
    return cofiguration

def get_stt_config(key):
    return cofiguration.get('stt_config').get(key, '')

def get_tts_config(key):
    return cofiguration.get('tts_config').get(key, '')
    