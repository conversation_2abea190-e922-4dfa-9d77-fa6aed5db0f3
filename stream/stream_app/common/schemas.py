from typing import Dict, Generic, Optional, TypeVar, List, Any
from datetime import date
from datetime import datetime


from pydantic import BaseModel, Field

T = TypeVar("T")

class ApiResponse(BaseModel, Generic[T]):
    id: str
    object: str
    created: int
    data: Optional[T] = None


class SuccessResponse(ApiResponse[T]):
    success: bool = True
    message: Optional[str] = "Operation completed successfully."


class ErrorResponse(ApiResponse[T]):
    success: bool = False
    message: Optional[str] = "Operation failed."
