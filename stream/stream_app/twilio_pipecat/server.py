#
# Copyright (c) 2025, Daily
#
# SPDX-License-Identifier: BSD 2-Clause License
#

import argparse
import json

import uvicorn
from stream_app.twilio_pipecat.bot import run_bot
from fastapi import APIRouter, WebSocket, Request
from starlette.responses import HTMLResponse

pipecat_router = APIRouter()

@pipecat_router.post("")
async def start_call():
    print("POST TwiML")
    return HTMLResponse(content=open("stream_app/twilio_pipecat/templates/streams.xml").read(), media_type="application/xml")


@pipecat_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    start_data = websocket.iter_text()
    await start_data.__anext__()
    call_data = json.loads(await start_data.__anext__())
    print(call_data, flush=True)
    stream_sid = call_data["start"]["streamSid"]
    call_sid = call_data["start"]["callSid"]
    print("WebSocket connection accepted")
    await run_bot(websocket, stream_sid, call_sid, True)

