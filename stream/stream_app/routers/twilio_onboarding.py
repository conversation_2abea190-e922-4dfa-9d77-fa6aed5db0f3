import json
import base64
from fastapi import APIRouter, Request, Depends
from stream_app.helpers.logging_helper import print_info_log
from stream_app.services.manager import Manager
from stream_app.services.Twilio_onboarding.onboarding_service import twilio_claim_random_number, twilio_phone_number_set_up
from stream_app.adapters.auth_adapter import get_api_key


onboarding_router = APIRouter()


@onboarding_router.get("/twilio/claim_random_number", dependencies=[Depends(get_api_key)])
async def claim_random_number():
    """
    Handle WebSocket connections for media streaming from Twilio.
    Each connection corresponds to a unique call SID.
    """
    phone_number = twilio_claim_random_number()
    return {"status": 200, "message": "success", "phone_number": phone_number}

@onboarding_router.post("/twilio/set_up_config", dependencies=[Depends(get_api_key)])
async def set_up_config(request: Request):
    """
    Setup phone number config and the business config
    """
    request_body = await request.json()  # Use `await` to get the JSON body
    phone_number = request_body.get("phone_number")
    business_id = request_body.get("business_id")
    use_case = request_body.get("use_case")

    if not phone_number or not business_id or not use_case:
        return {"status": 400, "message": "Missing required fields"}

    twilio_phone_number_set_up(phone_number, business_id, use_case)
    return {"status": 200, "message": "success"}
