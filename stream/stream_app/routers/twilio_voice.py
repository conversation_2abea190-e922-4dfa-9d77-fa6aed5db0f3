import os
import json
import base64
import tracemalloc
import traceback
import time
from fastapi import API<PERSON>out<PERSON>, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import PlainTextResponse
from twilio.twiml.voice_response import (
    VoiceResponse,
    Stream,
    Connect,
    Redirect,
    Hangup,
    Dial,
)

from stream_app.services.manager import Manager
from stream_app.common.config import fetch_config
from stream_app.helpers.brook_helper import brook_initiate_call
from stream_app.helpers.nina_helper import nina_initiate_call
from stream_app.helpers.twilio_util import transfer_call
from stream_app.helpers.logging_helper import print_info_log

voice_router = APIRouter()
active_connections = {}
tracemalloc.start()


async def get_websocket_for_call(call_sid: str):
    """
    Fetch the WebSocket connection for the given call SID.
    """
    return active_connections.get(call_sid)


async def remove_connection(call_sid: str):
    """
    Remove a WebSocket connection for the given call SID.
    """
    if call_sid in active_connections:
        active_connections[call_sid]["deepgram_client"].stop()
        del active_connections[call_sid]
    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics("lineno")
    for stat in top_stats[:10]:
        print_info_log("", "Memory Usage", stat)


@voice_router.post("")
async def twilio_voice_webhook(request: Request):
    """
    Handle incoming calls from Twilio and provide a WebSocket URL.
    """
    form_data = await request.form()
    call_sid = form_data.get("CallSid")
    from_number = form_data.get("From")
    to_number = form_data.get("To")
    called_via_number = form_data.get("CalledVia", "")
    # Generate the WebSocket URL based on the Call SID
    domain = os.getenv("DOMAIN_NAME")
    sub_domain = os.getenv("STREAM_SUB_DOMAIN")
    websocket_url = (
        f"wss://{sub_domain}.{domain}/twilio/voice/ws/{call_sid}"
    )
    config = fetch_config(
        f"multi-agent-framework-stream-resources-phone-config-{os.getenv('FASTAPI_ENV')}",
        "phone_number",
        to_number,
    )
    active_connections[call_sid] = {
        "conversation_id": call_sid,
        "use_case": config.get("use_case"),
        "status": "initiated",
    }
    if config.get("use_case") == "Brook":
        business_config = brook_initiate_call(
            call_sid,
            from_number,
            to_number,
            called_via_number,
            config.get("business_id"),
            config.get("single_agent_mode", None),
        )
        active_connections[call_sid]["transfer_number"] = business_config.get(
            "transfer_number"
        )
        active_connections[call_sid]["chat_id"] = business_config.get(
            "chat_id"
        )
        active_connections[call_sid]["application_id"] = "Brook_App_ID"
        if business_config.get("customer_handler") == "human":
            return PlainTextResponse(
                content=str(transfer_call(business_config.get("transfer_number")))
            )
    elif config.get("use_case") == "Nina":
        config = nina_initiate_call(
            call_sid,
            from_number,
            to_number,
            called_via_number,
            config.get("business_id"),
            config.get("single_agent_mode", None),
        )
        active_connections[call_sid]["application_id"] = config.get("application_id", "Nina_App_ID")
        active_connections[call_sid]["chat_id"] = config.get("chat_id")
    response = VoiceResponse()

    # Create a Connect element and add the Stream to it
    connect = Connect()
    stream = Stream(url=websocket_url)
    redirect = Redirect(
        url=f"https://{sub_domain}.{domain}/twilio/voice/post_stream/{call_sid}"
    )
    connect.append(stream)
    response.append(connect)
    response.append(redirect)

    # Return the TwiML response as plain text
    return PlainTextResponse(content=str(response))


@voice_router.websocket("/ws/{call_sid}")
async def media_endpoint(websocket: WebSocket, call_sid: str):
    """
    Handle WebSocket connections for media streaming from Twilio.
    Each connection corresponds to a unique call SID.
    """
    print("websocket", websocket)
    print("call_sid", call_sid)
    await websocket.accept()
    print_info_log("", "Connection", f"Connection accepted for call SID: {call_sid}")

    # Register the connection for the current call SID
    chat_id = active_connections[call_sid].get("chat_id")
    application_id = active_connections[call_sid].get("application_id")
    manager_object = Manager(chat_id, call_sid, websocket, application_id)
    manager_object.start()
    active_connections[call_sid]["twilio_socket"] = websocket
    active_connections[call_sid]["deepgram_client"] = manager_object
    active_connections[call_sid]["status"] = "connected"
    message_count = 0
    try:
        while True:
            message = await websocket.receive_text()
            if not message:
                print_info_log("", "Message", "No message received...")
                continue

            # Messages are a JSON encoded string
            data = json.loads(message)

            # Using the event type to determine the type of message
            event = data.get("event")
            if event == "connected":
                print_info_log(
                    "",
                    "Event",
                    f"Connected Message received for call SID {call_sid}: {message}",
                )
            elif event == "start":
                manager_object.stream_id = data.get("start").get("streamSid")
                print_info_log(
                    "",
                    "Event",
                    f"Start Message received for call SID {call_sid}: {message}",
                )
            elif event == "media":
                payload = data["media"]["payload"]
                chunk = base64.b64decode(payload)
                active_connections[call_sid]["deepgram_client"].send_to_STT(chunk)
            elif event == "mark":
                if data["mark"]["name"].startswith("say_"):
                    print_info_log(
                        "",
                        "Event",
                        f"mark event received for call SID {call_sid}: {message}",
                    )
                    print_info_log(call_sid, "#time_log twilio_event speech completed next say", time.time())
                elif data["mark"]["name"].startswith("gather_"):
                    manager_object.chat_mode = "Gather"
                    manager_object.start_idle_check()
                    print_info_log(
                        "",
                        "Event",
                        f"mark event received for call SID {call_sid}: {message}",
                    )
                    print_info_log(call_sid, "#time_log twilio_event speech completed next listen", time.time())
                elif data["mark"]["name"].startswith("disconnect_"):
                    manager_object.chat_mode = "Disconnect"
                    active_connections[call_sid]["status"] = "disconnect"
                    print_info_log(
                        "",
                        "Event",
                        f"mark event received for call SID {call_sid}: {message}",
                    )
                    print_info_log(call_sid, "#time_log twilio_event speech completed next disconnect", time.time())
                    await websocket.close()
                    break
                elif data["mark"]["name"].startswith("transfer_"):
                    manager_object.chat_mode = "Transfer"
                    active_connections[call_sid]["status"] = "transfer"
                    print_info_log(
                        "",
                        "Event",
                        f"mark event received for call SID {call_sid}: {message}",
                    )
                    print_info_log(call_sid, "#time_log twilio_event speech completed next transfer", time.time())
                    await websocket.close()
                    break

            elif event == "stop":
                print_info_log(
                    "",
                    "Event",
                    f"Stop Message received for call SID {call_sid}: {message}",
                )
                await websocket.close()
                break

            message_count += 1
    except WebSocketDisconnect:
        print_info_log(
            "", "Connection", f"WebSocket connection for call SID {call_sid} closed"
        )

    except Exception as e:
        traceback.print_exc(e)
        print_info_log("", "Error", f"Error occurred for call SID {call_sid}: {e}")

    finally:
        manager_object.stop()
        print_info_log(
            "",
            "Summary",
            f"Connection for call SID {call_sid} closed. Received a total of {message_count} messages",
        )


@voice_router.post("/post_stream/{call_sid}")
async def post_stream(request: Request, call_sid: str):
    """
    Handle the final response after the stream is completed.
    """
    print_info_log("", "Call SID", f"Call SID: {call_sid}")
    status = active_connections[call_sid]["status"]
    response = VoiceResponse()
    if status == "transfer":
        transfer_number = active_connections[call_sid]["transfer_number"]
        if transfer_number:
            dial = Dial(number=transfer_number)
            response.append(dial)
            print_info_log(
                "", "Transfer", f"Transfer Message received for call SID {call_sid}"
            )
    hangup = Hangup()
    response.append(hangup)
    await remove_connection(call_sid)
    return PlainTextResponse(content=str(response))
