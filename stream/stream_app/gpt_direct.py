import os
import json
import base64
import asyncio
import websockets
import zoneinfo
from datetime import datetime
from fastapi import APIRouter, WebSocket, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.websockets import WebSocketDisconnect
from twilio.twiml.voice_response import VoiceResponse, Connect, Say, Stream, Parameter
from dotenv import load_dotenv

load_dotenv()

TOOLS_SCHEMA = [
    {
        "name": "get_locations",
        "type": "function",
        "description": "This function provides a list of valid salon locations along with their address details.",
        "parameters": {"type": "object", "properties": {}, "required": [], "additionalProperties": False},
    },
    {
        "name": "get_service_package_details",
        "type": "function",
        "description": "The get_service_package_details function helps you find service and package details like name, category, description and duration. Details for package additionally include the services included in that package.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "Represents the valid salon location name selected by customer.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Salon_Service": {
                    "description": "Represents a list of valid services names whose details need to be retrieved.",
                    "items": {},
                    "title": "Salon Service",
                    "type": "array",
                },
            },
            "required": ["Salons_Branch_Name", "Salon_Service"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_valid_service_package_options",
        "type": "function",
        "description": "Validates the service name provided by the customer against the list of valid service package options available at a specific salon branch. This ensures that the correct service name is consistently used throughout subsequent processes. This function returns validated service name, the list of valid service package options (if requested), or an error message if validation fails.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "A required parameter. Represents the valid salon location name selected by customer.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Salon_Service": {
                    "default": [],
                    "description": "An optional parameter. Represents list of valid salon services selected by the customer. If parameter provided, the function will filter and return only stylists who offer these specific services.",
                    "items": {},
                    "title": "Salon Service",
                    "type": "array",
                },
            },
            "required": ["Salons_Branch_Name"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_stylist_time_availability",
        "type": "function",
        "description": "To check the availability of stylists, use this function which retrieve a list of available appointment slots. If you want to view slots for a specific stylist, apply the Stylist_Name filter, ensuring the results are limited to that stylist. The Stylist_Name should be a valid full name obtained through the get_salon_stylists function. Additionally, you can use the Preferred_Date filter to specify a particular date for which you wish to check availability. The function returns a comprehensive list of appointment slots, each providing details such as the start time, end time, stylist's name, and the schedule for every service. This makes it easier to identify and book suitable appointments tailored to your preferences and availability.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "This parameter specifies the location of the selected salon branch.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Salon_Service": {
                    "description": "A list of salon services selected by the customer. This will filter salon locations based on the services offered at each location.",
                    "items": {},
                    "title": "Salon Service",
                    "type": "array",
                },
                "Preferred_Date": {
                    "description": "Customer preferred date for appointment in `%Y-%m-%d` format strictly.",
                    "title": "Preferred Date",
                    "type": "string",
                },
                "Stylist_Name": {
                    "default": "",
                    "description": "An stylist full name to filter based on stylist availability",
                    "title": "Stylist Name",
                    "type": "string",
                },
                "Preferred_Time": {
                    "default": "",
                    "description": "Customer preferred time for appointment in `HH:MM:SS` format strictly.",
                    "title": "Preferred Time",
                    "type": "string",
                },
            },
            "required": ["Salons_Branch_Name", "Salon_Service", "Preferred_Date"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_valid_stylist_name",
        "type": "function",
        "description": "Validates the stylist name provided by the customer against the list of valid stylists for a specific salon branch. This ensures that the correct stylist name is consistently used throughout subsequent processes. This function returns details of the validated stylist name, the list of valid stylists options (if requested) or an error message if validation fails.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "A required parameter. Represents the valid salon location name selected by customer.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Salon_Service": {
                    "default": [],
                    "description": "An optional parameter. Represents list of valid salon services selected by the customer. If parameter provided, the function will filter and return only stylists who offer these specific services.",
                    "items": {},
                    "title": "Salon Service",
                    "type": "array",
                },
                "Stylist_Name": {
                    "default": "",
                    "description": "An optional parameter. Represents stylist name provided by the customer. If parameter provided, the function will return valid stylist names matching the requested name.",
                    "title": "Stylist Name",
                    "type": "string",
                },
            },
            "required": ["Salons_Branch_Name"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_stylist_details",
        "type": "function",
        "description": "The get_stylist_details function provides information about stylists, including their level or seniority, which reflects their experience, along with other relevant details.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "This parameter specifies the location selected by customer.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Stylist_Name": {
                    "description": "Represents a list of valid stylist names whose details need to be retrieved.",
                    "items": {},
                    "title": "Stylist Name",
                    "type": "array",
                },
            },
            "required": ["Salons_Branch_Name", "Stylist_Name"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_salon_prices",
        "type": "function",
        "description": "The get_salon_prices function retrieves the prices for services at a specific salon branch. It provides a list of selected services along with their corresponding prices. Use this function to quickly check the service prices at a particular salon, including the prices set by individual stylists.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "This parameter specifies the location selected by customer.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "salon_service": {
                    "description": "List of salon services selected by the customer.",
                    "items": {},
                    "title": "Salon Service",
                    "type": "array",
                },
            },
            "required": ["Salons_Branch_Name", "salon_service"],
            "additionalProperties": False,
        },
    },
    {
        "name": "create_client",
        "type": "function",
        "description": "Function to crate new client account, using the provided phone number and first name, last_name",
        "parameters": {
            "type": "object",
            "properties": {
                "first_name": {"description": "First name of the customer", "type": "string"},
                "phone_number": {"description": "The phone number of the user/client", "type": "string"},
                "last_name": {"description": "Last name of the customer", "type": "string"},
            },
            "required": ["first_name", "phone_number", "last_name"],
            "additionalProperties": False,
        },
    },
    {
        "name": "get_client",
        "type": "function",
        "description": "The get_client function verifies if a client exists in the Phorest system by checking their phone number. It is helpful for confirming whether a client is already registered in the system. If the Phone_Number is not provided, the default phone number will be the number from which the customer dialed in.",
        "parameters": {
            "type": "object",
            "properties": {
                "Phone_Number": {
                    "default": "",
                    "description": "A valid US PhoneNumber, That customer wants to book appointment with.",
                    "title": "Phone Number",
                    "type": "string",
                }
            },
            "required": [],
            "additionalProperties": False,
        },
    },
    {
        "name": "make_salon_booking",
        "type": "function",
        "description": "The make_salon_booking function allows you to schedule an appointment at a salon by providing essential details such as the branch name, appointment date in YYYY-MM-DD format, time in HH:MM:SS format, and a list of services along with the corresponding stylist’s name for each service. You can also include an optional appointment note. This function helps streamline the booking process by ensuring the right services and stylist are scheduled at the specified time and date, making it easier to manage salon appointments.",
        "parameters": {
            "type": "object",
            "properties": {
                "Salons_Branch_Name": {
                    "description": "This parameter specifies the location of the selected salon branch.",
                    "title": "Salons Branch Name",
                    "type": "string",
                },
                "Date_of_Appointment": {
                    "description": "Specifies the date of the appointment in YYYY-MM-DD format.",
                    "title": "Date Of Appointment",
                    "type": "string",
                },
                "Service_Description": {
                    "description": "It should list of dictionary like [{'Salon_Service':'service name', 'Stylist_Name':'stylist name'}], A list of services with associated stylist details. Each entry should be an object with 'Salon_Service' (service name) and 'Stylist_Name' (stylist's name).",
                    "items": {},
                    "title": "Service Description",
                    "type": "array",
                },
                "Appointment_Time": {
                    "description": "Specifies the time of the appointment in HH:MM:SS format.",
                    "title": "Appointment Time",
                    "type": "string",
                },
                "Phone_Number": {
                    "description": "A valid US PhoneNumber, That customer wants to book appointment with.",
                    "title": "Phone Number",
                    "type": "string",
                },
                "Appointment_Note": {
                    "default": "",
                    "description": "Any additional notes for the appointment.",
                    "title": "Appointment Note",
                    "type": "string",
                },
            },
            "required": [
                "Salons_Branch_Name",
                "Date_of_Appointment",
                "Service_Description",
                "Appointment_Time",
                "Phone_Number",
            ],
            "additionalProperties": False,
        },
    },
    {
        "name": "retrieve_previously_booked_appointments",
        "type": "function",
        "description": "This function used for retrieving client appointment history. Returns JSON with past appointments including dates, times, services, and stylist details. This function only returns past information, not future appointments.",
        "parameters": {
            "type": "object",
            "properties": {
                "phone_number": {
                    "description": "The phone number of the client whose appointment history needs to be retrieved.",
                    "title": "Phone Number",
                    "type": "string",
                },
                "stylist_name": {
                    "anyOf": [{"type": "string"}, {"type": "null"}],
                    "default": None,
                    "description": "The name of the stylist to filter appointments by.",
                    "title": "Stylist Name",
                },
                "service_name": {
                    "anyOf": [{"type": "string"}, {"type": "null"}],
                    "default": None,
                    "description": "The name of the service to filter appointments by.",
                    "title": "Service Name",
                },
            },
            "required": ["phone_number"],
            "additionalProperties": False,
        },
    },
]

import json
from typing import Annotated
import requests
from stream_app.helpers.dynamo_helper import get_item_by_primary_key


def create_client(
    phone_number: Annotated[str, "The phone number of the user/client."],
    first_name: Annotated[str, "The first name of the user/client."],
    last_name: Annotated[str, "The last name of the user/client."],
    context_arguments: dict = {},
) -> str:
    """Generic POST request handler for Phorest API interactions."""
    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "phone_number": phone_number,
        "client_first_name": first_name,
        "client_last_name": last_name,
        "chat_id": context_arguments.get("chat_id"),
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/create_client_account"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)


def get_client(
    Phone_Number: Annotated[str, "A valid US PhoneNumber, That customer wants to book appointment with."] = "",
    context_arguments: dict = {},
) -> str:
    """
    This function checks if a client exists based on the phone number
    """

    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "phone_number": Phone_Number if Phone_Number else context_arguments.get("PhoneNumber"),
        "chat_id": context_arguments.get("chat_id"),
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/get_client_account"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)


def get_locations(context_arguments: dict = {}) -> str:
    """
    Retrieves valid salon locations based on provided filters.

    Args:
    - salon_service (str, optional): Selected salon's service.
    - stylist_name (str, optional): Selected stylist's name.

    Returns:
    - list: List of valid salon locations with names and addresses.
    """
    body = {"business_id": "Wj6YQfGEsvSqkLskUdhu3g", "chat_id": context_arguments.get("chat_id")}
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/get_location_details"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_locations:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_salon_prices(
    Salons_Branch_Name: Annotated[str, "This parameter specifies the location selected by customer."],
    salon_service: Annotated[list, "List of salon services selected by the customer."],
    context_arguments: dict = {},
) -> str:
    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Salon_Service": salon_service,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_salon_prices"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }

    if body:
        param["data"] = json.dumps(body)
    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_salon_prices:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_service_package_details(
    Salons_Branch_Name: Annotated[str, "Represents the valid salon location name selected by customer."],
    Salon_Service: Annotated[list, "Represents a list of valid services names whose details need to be retrieved."],
    context_arguments: dict = {},
) -> str:

    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Salon_Service": Salon_Service,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_service_package_details"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_locations:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_stylist_details(
    Salons_Branch_Name: Annotated[str, "This parameter specifies the location selected by customer."],
    Stylist_Name: Annotated[list, "Represents a list of valid stylist names whose details need to be retrieved."],
    context_arguments: dict = {},
) -> str:

    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Stylist_Names": Stylist_Name,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_stylist_details"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_locations:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_stylist_time_availability(
    Salons_Branch_Name: Annotated[str, "This parameter specifies the location of the selected salon branch."],
    Salon_Service: Annotated[
        list,
        "A list of salon services selected by the customer. This will filter salon locations based on the services offered at each location.",
    ],
    Preferred_Date: Annotated[str, "Customer preferred date for appointment in `%Y-%m-%d` format strictly."],
    Stylist_Name: Annotated[str, "An stylist full name to filter based on stylist availability"] = "",
    Preferred_Time: Annotated[str, "Customer preferred time for appointment in `HH:MM:SS` format strictly."] = "",
    context_arguments: dict = {},
) -> str:
    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Salon_Service": Salon_Service,
        "Stylist_Name": Stylist_Name,
        "Preferred_Date": Preferred_Date,
        "Preferred_Time": Preferred_Time,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_available_slots"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)
    try:
        response = requests.post(**param)
        print("response:", response.text)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_stylist_time_availability :", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_valid_service_package_options(
    Salons_Branch_Name: Annotated[
        str, "A required parameter. Represents the valid salon location name selected by customer."
    ],
    Salon_Service: Annotated[
        list,
        "An optional parameter. Represents list of valid salon services selected by the customer. If parameter provided, the function will filter and return only stylists who offer these specific services.",
    ] = [],
    context_arguments: dict = {},
) -> str:

    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Salon_Service": Salon_Service,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_valid_service_package_options"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_locations:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def get_valid_stylist_name(
    Salons_Branch_Name: Annotated[
        str, "A required parameter. Represents the valid salon location name selected by customer."
    ],
    Salon_Service: Annotated[
        list,
        "An optional parameter. Represents list of valid salon services selected by the customer. If parameter provided, the function will filter and return only stylists who offer these specific services.",
    ] = [],
    Stylist_Name: Annotated[
        str,
        "An optional parameter. Represents stylist name provided by the customer. If parameter provided, the function will return valid stylist names matching the requested name.",
    ] = "",
    context_arguments: dict = {},
) -> str:

    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Salons_Branch_Name": Salons_Branch_Name,
        "Salon_Service": Salon_Service,
        "Stylist_Name": Stylist_Name,
    }
    url = "http://brook.dev-scc-demo.rozie.ai/phorest/get_valid_stylist_options"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in get_locations:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def make_salon_booking(
    Salons_Branch_Name: Annotated[str, "This parameter specifies the location of the selected salon branch."],
    Date_of_Appointment: Annotated[str, "Specifies the date of the appointment in YYYY-MM-DD format."],
    Service_Description: Annotated[
        list,
        "It should list of dictionary like [{'Salon_Service':'service name', 'Stylist_Name':'stylist name'}], A list of services with associated stylist details. Each entry should be an object with 'Salon_Service' (service name) and 'Stylist_Name' (stylist's name).",
    ],
    Appointment_Time: Annotated[str, "Specifies the time of the appointment in HH:MM:SS format."],
    Phone_Number: Annotated[str, "A valid US PhoneNumber, That customer wants to book appointment with."],
    Appointment_Note: Annotated[str, "Any additional notes for the appointment."] = "",
    context_arguments: dict = {},
) -> str:
    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "chat_id": context_arguments.get("chat_id"),
        "Date_of_Appointment": Date_of_Appointment,
        "Salons_Branch_Name": Salons_Branch_Name,
        "Service_Description": Service_Description,
        "Appointment_Time": Appointment_Time,
        "Appointment_Note": Appointment_Note,
        "phone_number": Phone_Number if Phone_Number else context_arguments.get("PhoneNumber"),
    }
    url = "https://brook.dev-scc-demo.rozie.ai/phorest/make_booking"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    if body:
        param["data"] = json.dumps(body)

    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            if response.json():
                return "Failed"
            return "Failed"
        response_data = response.json()
    except Exception as e:
        print("Error in make_salon_booking:", e)
        return f"Failed {str(e)}"

    return json.dumps(response_data, indent=2)


def retrieve_previously_booked_appointments(
    phone_number: Annotated[str, "The phone number of the client whose appointment history needs to be retrieved."],
    stylist_name: Annotated[str | None, "The name of the stylist to filter appointments by."] = None,
    service_name: Annotated[str | None, "The name of the service to filter appointments by."] = None,
    context_arguments: dict = {},
) -> str:
    """
    Retrieves the previously booked appointments for a client from the Phorest system.

    Args:
        phone_number: Client's phone number to lookup their appointment history
        stylist_name: Optional stylist name to filter appointments
        service_name: Optional service name to filter appointments
        context_arguments: Additional context parameters including chat_id

    Returns:
        str: JSON string containing the appointment history or error message
    """
    body = {
        "business_id": "Wj6YQfGEsvSqkLskUdhu3g",
        "phone_number": phone_number,
        "chat_id": context_arguments.get("chat_id"),
        "stylist_name": stylist_name,
        "service_name": service_name,
    }

    url = "https://brook.dev-scc-demo.rozie.ai/phorest/retrieve_previously_booked_appointments"
    headers = {"Content-Type": "application/json"}
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }

    if body:
        param["data"] = json.dumps(body)
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)


available_functions = {
    "get_locations": get_locations,
    "get_service_package_details": get_service_package_details,
    "get_valid_service_package_options": get_valid_service_package_options,
    "get_stylist_time_availability": get_stylist_time_availability,
    "get_valid_stylist_name": get_valid_stylist_name,
    "get_stylist_details": get_stylist_details,
    "get_salon_prices": get_salon_prices,
    "create_client": create_client,
    "get_client": get_client,
    "make_salon_booking": make_salon_booking,
    "retrieve_previously_booked_appointments": retrieve_previously_booked_appointments,
}
# Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
PORT = int(os.getenv("PORT", "5050"))
SYSTEM_MESSAGE = None
VOICE = "coral"
LOG_EVENT_TYPES = [
    "error",
    "response.content.done",
    "rate_limits.updated",
    "response.done",
    "input_audio_buffer.committed",
    "input_audio_buffer.speech_stopped",
    "input_audio_buffer.speech_started",
    "session.created",
]
SHOW_TIMING_MATH = False
gpt_direct = APIRouter()

if not OPENAI_API_KEY:
    raise ValueError("Missing the OpenAI API key. Please set it in the .env file.")


@gpt_direct.get("/", response_class=JSONResponse)
async def index_page():
    return {"message": "Twilio Media Stream Server is running!"}


@gpt_direct.api_route("/incoming-call", methods=["GET", "POST"])
async def handle_incoming_call(request: Request):
    """Handle incoming call and return TwiML response to connect to Media Stream."""
    response = VoiceResponse()
    # <Say> punctuation to improve text-to-speech flow
    # response.say("Welcome to [salon]718  Bay Ridge! I'm Brook, a virtual assistant. ")
    # response.pause(length=1)
    # response.say("How can I help you today?")
    host = request.url.hostname
    form_data = await request.form()
    call_sid = form_data.get("CallSid")
    from_number = form_data.get("From")
    to_number = form_data.get("To")

    item = get_item_by_primary_key(
        table="gpt-direct-attributes-dev",
        primary_key_id="attribute_set_id",
        primary_key_value="1",
    )
    global VOICE
    global SYSTEM_MESSAGE
    VOICE = item.get("voice")

    timezone = zoneinfo.ZoneInfo(item.get("timezone"))
    current_time = datetime.now(timezone)
    TODAYS_DATE = current_time.strftime("%d")
    TODAYS_MONTH = current_time.strftime("%B")
    TODAYS_YEAR = current_time.strftime("%Y")
    TODAYS_DAY = current_time.strftime("%A")

    prompt = item.get("prompt")

    SYSTEM_MESSAGE = prompt.format(
        LOCATION=item.get("location"),
        PHONE_NUMBER=from_number,
        CUSTOMER_NAME=item.get("customer_name"),
        TODAYS_DATE=TODAYS_DATE,
        TODAYS_MONTH=TODAYS_MONTH,
        TODAYS_YEAR=TODAYS_YEAR,
        TODAYS_DAY=TODAYS_DAY,
    )
    print("SYSTEM_MESSAGE", SYSTEM_MESSAGE)
    connect = Connect()
    url = f"wss://{host}/gpt-direct/media-stream"
    stream = Stream(url=url)
    stream.parameter(name="CallSid", value=call_sid)
    stream.parameter(name="From", value=from_number)
    stream.parameter(name="To", value=to_number)
    stream.parameter(name="Voice", value=VOICE)
    connect.append(stream)
    response.append(connect)
    return HTMLResponse(content=str(response), media_type="application/xml")


@gpt_direct.websocket("/media-stream")
async def handle_media_stream(websocket: WebSocket):
    """Handle WebSocket connections between Twilio and OpenAI."""
    print("Client connected")
    call_sid = None
    from_number = None
    to_number = None
    await websocket.accept()

    async with websockets.connect(
        "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview",
        extra_headers={"Authorization": f"Bearer {OPENAI_API_KEY}", "OpenAI-Beta": "realtime=v1"},
    ) as openai_ws:
        await initialize_session(openai_ws)

        # Connection specific state
        stream_sid = None
        latest_media_timestamp = 0
        last_assistant_item = None
        mark_queue = []
        response_start_timestamp_twilio = None

        async def receive_from_twilio():
            """Receive audio data from Twilio and send it to the OpenAI Realtime API."""
            nonlocal stream_sid, latest_media_timestamp
            try:
                async for message in websocket.iter_text():
                    data = json.loads(message)
                    if data["event"] == "media" and openai_ws.state.name == "OPEN":
                        latest_media_timestamp = int(data["media"]["timestamp"])
                        audio_append = {"type": "input_audio_buffer.append", "audio": data["media"]["payload"]}
                        await openai_ws.send(json.dumps(audio_append))
                    elif data["event"] == "start":
                        stream_sid = data["start"]["streamSid"]
                        call_sid = data["start"]["customParameters"].get("CallSid", None)
                        from_number = data["start"]["customParameters"].get("From", None)
                        to_number = data["start"]["customParameters"].get("To", None)
                        print(f"Incoming stream has started {stream_sid}")
                        response_start_timestamp_twilio = None
                        latest_media_timestamp = 0
                        last_assistant_item = None
                    elif data["event"] == "mark":
                        if mark_queue:
                            mark_queue.pop(0)
            except WebSocketDisconnect:
                print("Client disconnected.")
                if openai_ws.state.name == "OPEN":
                    await openai_ws.close()

        async def send_to_twilio():
            """Receive events from the OpenAI Realtime API, send audio back to Twilio."""
            nonlocal stream_sid, last_assistant_item, response_start_timestamp_twilio, to_number, from_number, call_sid
            try:
                async for openai_message in openai_ws:
                    response = json.loads(openai_message)
                    if response["type"] in LOG_EVENT_TYPES:
                        print(f"Received event: {response['type']}", response)

                    if response.get("type") == "response.audio.delta" and "delta" in response:
                        audio_payload = base64.b64encode(base64.b64decode(response["delta"])).decode("utf-8")
                        audio_delta = {"event": "media", "streamSid": stream_sid, "media": {"payload": audio_payload}}
                        await websocket.send_json(audio_delta)

                        if response_start_timestamp_twilio is None:
                            response_start_timestamp_twilio = latest_media_timestamp
                            if SHOW_TIMING_MATH:
                                print(f"Setting start timestamp for new response: {response_start_timestamp_twilio}ms")

                        # Update last_assistant_item safely
                        if response.get("item_id"):
                            last_assistant_item = response["item_id"]

                        await send_mark(websocket, stream_sid)
                    if response["type"] == "response.output_item.done":
                        if "item" in response and response["item"]["type"] == "function_call":
                            item = response["item"]
                            function_to_call = available_functions[item["name"]]
                            function_args = json.loads(item["arguments"])
                            function_args["context_arguments"] = {
                                "chat_id": call_sid if call_sid else "TEST_LOCAL_c941d7fc-1ec7-4d0b-99b3-ae6c8f834468",
                                "agent_role": "Assisting customers in booking salon appointments by providing information about salon services, stylists, packages, prices, and stylist availability.",
                                "illuminar_config": {
                                    "base_url": "https://api-manager-sandbox.rozie.ai/event-adapter",
                                    "search_url": "/v1/adapters-illuminar",
                                    "application_id": "application_f7421e21-83c9-4f99-ba9c-f308abd129e1",
                                    "api_key": "5b5fadd08ddc4295abfa854244cbfbb2",
                                    "enhancement": False,
                                    "params": {"num_of_results": 2, "folders": ["Salon718"]},
                                },
                                "UserName": "RozieAI",
                                "PhoneNumber": from_number,
                                "WelcomeMessageFlag": "profile_found",
                                "Location": "Bay Ridge",
                                "llmContext": {
                                    "Customer's Dialed Location": "Bay Ridge",
                                    "Customer's Dialed Number": from_number,
                                    "Customer's Name": "RozieAI",
                                },
                                "roster_id": "salon718-Dev",
                            }
                            try:
                                function_response = function_to_call(**function_args)
                                print(f'function_name: {item["name"]}\nresponse: {function_response}')
                            except Exception as e:
                                print(f"Error calling function {item['name']}: {e}")
                            await openai_ws.send(
                                json.dumps(
                                    {
                                        "type": "conversation.item.create",
                                        "item": {
                                            "type": "function_call_output",
                                            "call_id": item["call_id"],
                                            "output": function_response,
                                        },
                                    }
                                )
                            )
                            # define a response_create object to
                            # trigger the response
                            response_create = {"type": "response.create"}
                            await openai_ws.send(json.dumps(response_create))
                    # Trigger an interruption. Your use case might work better using `input_audio_buffer.speech_stopped`, or combining the two.
                    if response.get("type") == "input_audio_buffer.speech_started":
                        print("Speech started detected.")
                        if last_assistant_item:
                            print(f"Interrupting response with id: {last_assistant_item}")
                            await handle_speech_started_event()
            except Exception as e:
                print(f"Error in send_to_twilio: {e}")

        async def handle_speech_started_event():
            """Handle interruption when the caller's speech starts."""
            nonlocal response_start_timestamp_twilio, last_assistant_item
            print("Handling speech started event.")
            if mark_queue and response_start_timestamp_twilio is not None:
                elapsed_time = latest_media_timestamp - response_start_timestamp_twilio
                if SHOW_TIMING_MATH:
                    print(
                        f"Calculating elapsed time for truncation: {latest_media_timestamp} - {response_start_timestamp_twilio} = {elapsed_time}ms"
                    )

                if last_assistant_item:
                    if SHOW_TIMING_MATH:
                        print(f"Truncating item with ID: {last_assistant_item}, Truncated at: {elapsed_time}ms")

                    truncate_event = {
                        "type": "conversation.item.truncate",
                        "item_id": last_assistant_item,
                        "content_index": 0,
                        "audio_end_ms": elapsed_time,
                    }
                    await openai_ws.send(json.dumps(truncate_event))

                await websocket.send_json({"event": "clear", "streamSid": stream_sid})

                mark_queue.clear()
                last_assistant_item = None
                response_start_timestamp_twilio = None

        async def send_mark(connection, stream_sid):
            if stream_sid:
                mark_event = {"event": "mark", "streamSid": stream_sid, "mark": {"name": "responsePart"}}
                await connection.send_json(mark_event)
                mark_queue.append("responsePart")

        await asyncio.gather(receive_from_twilio(), send_to_twilio())


async def send_initial_conversation_item(openai_ws):
    """Send initial conversation item if AI talks first."""
    initial_conversation_item = {
        "type": "conversation.item.create",
        "item": {
            "type": "message",
            "role": "user",
            "content": [
                {
                    "type": "input_text",
                    "text": "Greet the user with 'Welcome to [salon]718 {Location}! I'm Brook, a virtual assistant. How can I help you today?'",
                }
            ],
        },
    }
    await openai_ws.send(json.dumps(initial_conversation_item))
    await openai_ws.send(json.dumps({"type": "response.create"}))


async def initialize_session(openai_ws):
    """Control initial session with OpenAI."""
    session_update = {
        "type": "session.update",
        "session": {
            "turn_detection": {"type": "server_vad"},
            "input_audio_format": "g711_ulaw",
            "output_audio_format": "g711_ulaw",
            "voice": VOICE,
            "instructions": SYSTEM_MESSAGE,
            "modalities": ["text", "audio"],
            "temperature": 0.8,
            "tools": TOOLS_SCHEMA,
        },
    }
    print("Sending session update:", json.dumps(session_update))
    await openai_ws.send(json.dumps(session_update))

    # Uncomment the next line to have the AI speak first
    await send_initial_conversation_item(openai_ws)
