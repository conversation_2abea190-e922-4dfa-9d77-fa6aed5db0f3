[tool.poetry]
authors = []
description = "FastAPI service for Elsa Conversation Insights."
name = "scc-llm-autogen"
readme = "README.md"
version = "0.1.0"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
fastapi = "^0.115.0"
uvicorn = {extras = ["standard"], version = "^0.27.0.post1"}
pydantic = ">=1,<3"
pydantic_settings = ">=1,<3"
httpx = "*"
boto3 = "^1.34.49"
requests= "^2.31.0"
twilio= "^9.2.3"
deepgram-sdk ="3.7.7"
fastapi-utilities= "0.2.0"
python-multipart = "^0.0.18"
google-cloud-texttospeech = "^2.23.0"
websockets = "13.1"
pipecat-ai = "^0.0.65"
python-dotenv = "^1.1.0"
onnxruntime = "^1.21.1"
cartesia = "^2.0.2"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"

[tool.poetry.group.codespell]
optional = true

[tool.poetry.group.codespell.dependencies]
codespell = "^2.2.6"

[tool.poetry.group.lint]
optional = true

[tool.poetry.group.lint.dependencies]
ruff = "^0.1.15"
pre-commit = "^3.6.0"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^7.3.0"
pytest-cov = "^4.0.0"

[tool.poetry.group.typing]
optional = true

[tool.poetry.group.typing.dependencies]
mypy = "^1.8.0"

[tool.codespell]
check-filenames = true
check-hidden = true
ignore-words-list = "astroid,gallary,momento,narl,ot,rouge"
# Feel free to un-skip examples, and experimental, you will just need to
# work through many typos (--write-changes and --interactive will help)
skip = "*.csv,*.html,*.json,*.jsonl,*.pdf,*.txt,*.ipynb"

[tool.coverage.run]
omit = [
  "tests/*"
]

[tool.mypy]
disallow_untyped_defs = true
# Remove venv skip when integrated with pre-commit
exclude = [".venv", "examples"]
follow_imports = "skip"
ignore_missing_imports = true
python_version = "3.11"
strict_optional = false

[tool.ruff]
target-version = "py311"

[tool.ruff.flake8-annotations]
mypy-init-return = true

[tool.ruff.pydocstyle]
convention = "google"

[build-system]
build-backend = "poetry.core.masonry.api"
requires = ["poetry-core"]