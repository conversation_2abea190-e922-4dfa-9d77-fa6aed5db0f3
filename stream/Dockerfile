################################
# PYTHON-BASE
# Sets up all our shared environment variables
################################
FROM python:3.11.2-bullseye as python-base
ARG FASTAPI_ENV
ARG AWS_REGION
ARG TWILIO_ACCOUNT_SID
ARG TWILIO_AUTH_TOKEN
ARG APP_API_KEY
ARG MULTI_AGENT_FRAMEWORK_URL
ARG MULTI_AGENT_FRAMEWORK_API_KEY
ARG DEEPGRAM_API_KEY
ARG OPENAI_API_KEY
ARG DOMAIN_NAME
ARG STREAM_SUB_DOMAIN
ENV PYTHONUNBUFFERED=1 \
    # prevents python creating .pyc files
    PYTHONDONTWRITEBYTECODE=1 \
    # pip
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    \
    # Poetry
    # https://python-poetry.org/docs/configuration/#using-environment-variables
    POETRY_VERSION=1.7.1 \
    # make poetry install to this location
    POETRY_HOME="/opt/poetry" \
    # make poetry create the virtual environment in the project's root
    # it gets named `.venv`
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    # do not ask any interactive question
    POETRY_NO_INTERACTION=1 \
    \
    # paths
    # this is where our requirements + virtual environment will live
    PYSETUP_PATH="/opt/pysetup" \
    VENV_PATH="/opt/pysetup/.venv" \
    FASTAPI_ENV=$FASTAPI_ENV \
    AWS_REGION=$AWS_REGION \
    TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID \
    TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN \
    APP_API_KEY=$APP_API_KEY \
    MULTI_AGENT_FRAMEWORK_URL=$MULTI_AGENT_FRAMEWORK_URL \
    MULTI_AGENT_FRAMEWORK_API_KEY=$MULTI_AGENT_FRAMEWORK_API_KEY \
    DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY \
    DOMAIN_NAME=$DOMAIN_NAME \
    STREAM_SUB_DOMAIN=$STREAM_SUB_DOMAIN \
    OPENAI_API_KEY=$OPENAI_API_KEY
# Add environment variables to the above stack as required

# prepend poetry and venv to path
ENV PATH="$POETRY_HOME/bin:$VENV_PATH/bin:$PATH"

################################
# BUILDER-BASE
# Used to build deps + create our virtual environment
################################
FROM python-base as builder-base
RUN apt-get update \
    && apt-get install --no-install-recommends -y \
        # deps for installing poetry
        curl \
        # deps for building python deps
        build-essential

# install poetry - respects $POETRY_VERSION & $POETRY_HOME
# The --mount will mount the buildx cache directory to where
# Poetry and Pip store their cache so that they can reuse it
RUN curl -sSL https://install.python-poetry.org | python -

# copy project requirement files here to ensure they will be cached
WORKDIR $PYSETUP_PATH
COPY poetry.lock pyproject.toml ./

# install runtime deps - uses $POETRY_VIRTUALENVS_IN_PROJECT internally
RUN poetry install --no-root --only main

################################
# Testing
# Image used during testing
################################
FROM python-base as testing
ENV FASTAPI_ENV=test
WORKDIR $PYSETUP_PATH

# copy in our built poetry + venv
COPY --from=builder-base $POETRY_HOME $POETRY_HOME
COPY --from=builder-base $PYSETUP_PATH $PYSETUP_PATH

# quicker install as runtime deps are already installed
RUN poetry install --no-root --with test

# copy project files
COPY ./stream_app /app/stream_app

# Set the entrypoint to run tests using Poetry
ENTRYPOINT ["poetry", "run", "pytest", "--cov", "--cov-config", ".coveragerc", "--cov-report", "xml", "--cov-report", "term-missing:skip-covered"]

# Set the default command to run all unit tests
CMD ["/app/stream_app/tests"]

################################
# PRODUCTION
# Final image used for runtime
################################
FROM python-base as production
COPY --from=builder-base $PYSETUP_PATH $PYSETUP_PATH

# copy project files
COPY ./stream_app /stream_app

EXPOSE 80
CMD ["uvicorn", "stream_app.main:app", "--host", "0.0.0.0", "--port", "80"]
