#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON>'s Requirements Validation Script
========================================

This script validates the current implementation against <PERSON><PERSON><PERSON>'s specific requirements:
1. Talk-back conversation capability
2. Clarification for ambiguous requests  
3. Unknown-info handling with graceful fallbacks
4. Mid-conversation transfer support
5. Three-path decision model (direct help/route/clarify/out-of-scope)

Usage:
    python validate_shubham_requirements.py
    python validate_shubham_requirements.py --detailed
    python validate_shubham_requirements.py --fix-issues
"""

import json
import sys
import os
import argparse
from typing import Dict, List, Any, Tuple

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append('multi-agent-framework')


class ShubhamRequirementsValidator:
    """Validator for <PERSON><PERSON><PERSON>'s specific requirements."""
    
    def __init__(self, detailed: bool = False):
        """
        Initialize the validator.
        
        Args:
            detailed (bool): Enable detailed validation output
        """
        self.detailed = detailed
        self.validation_results = {}
        self.issues_found = []
        self.recommendations = []
        
    def validate_all_requirements(self) -> Dict[str, Any]:
        """
        Validate all of Shubham's requirements.
        
        Returns:
            Dict[str, Any]: Validation results summary
        """
        print("🔍 Validating Shubham's Requirements")
        print("=" * 50)
        
        requirements = [
            ("Talk-back Conversation Capability", self._validate_talk_back_capability),
            ("Clarification for Ambiguous Requests", self._validate_clarification_handling),
            ("Unknown-info Graceful Fallbacks", self._validate_unknown_info_handling),
            ("Mid-conversation Transfer Support", self._validate_mid_conversation_transfer),
            ("Three-path Decision Model", self._validate_three_path_decision_model)
        ]
        
        total_score = 0
        max_score = len(requirements)
        
        for requirement_name, validator_func in requirements:
            print(f"\n📋 Validating: {requirement_name}")
            print("-" * 40)
            
            try:
                result = validator_func()
                self.validation_results[requirement_name] = result
                
                if result["compliant"]:
                    print(f"✅ COMPLIANT - {result['score']:.1f}/10")
                    total_score += 1
                else:
                    print(f"❌ NON-COMPLIANT - {result['score']:.1f}/10")
                    self.issues_found.extend(result.get("issues", []))
                
                if self.detailed and result.get("details"):
                    for detail in result["details"]:
                        print(f"   • {detail}")
                        
            except Exception as e:
                print(f"❌ ERROR: {str(e)}")
                self.validation_results[requirement_name] = {
                    "compliant": False,
                    "score": 0,
                    "error": str(e)
                }
        
        # Calculate overall compliance
        compliance_rate = (total_score / max_score) * 100
        
        summary = {
            "overall_compliance": compliance_rate,
            "compliant_requirements": total_score,
            "total_requirements": max_score,
            "detailed_results": self.validation_results,
            "issues_found": self.issues_found,
            "recommendations": self.recommendations
        }
        
        self._print_summary(summary)
        return summary
    
    def _validate_talk_back_capability(self) -> Dict[str, Any]:
        """Validate talk-back conversation capability."""
        score = 0
        details = []
        issues = []
        
        # Check triage agent V2 template
        template_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Check for talk-back patterns
            talk_back_patterns = [
                "talking back conversationally",
                "conversational responses",
                "before making routing decisions",
                "provide excellent front-desk customer service"
            ]
            
            found_patterns = 0
            for pattern in talk_back_patterns:
                if pattern.lower() in content.lower():
                    found_patterns += 1
                    details.append(f"Found talk-back pattern: '{pattern}'")
            
            score = (found_patterns / len(talk_back_patterns)) * 10
            
            if score < 7:
                issues.append("Insufficient talk-back conversation patterns in triage template")
        else:
            issues.append("Triage agent V2 template not found")
            score = 0
        
        # Check implementation
        wrapper_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/agent/triage_agent_v2_wrapper.py"
        if os.path.exists(wrapper_path):
            details.append("Triage agent V2 wrapper implementation found")
            score += 2
        else:
            issues.append("Triage agent V2 wrapper implementation not found")
        
        return {
            "compliant": score >= 7,
            "score": min(score, 10),
            "details": details,
            "issues": issues
        }
    
    def _validate_clarification_handling(self) -> Dict[str, Any]:
        """Validate clarification handling for ambiguous requests."""
        score = 0
        details = []
        issues = []
        
        # Check template for clarification patterns
        template_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                content = f.read()
            
            clarification_patterns = [
                "clarifying questions",
                "when intent is unclear",
                "ask clarification",
                "max 2 attempts",
                "ambiguous requests"
            ]
            
            found_patterns = 0
            for pattern in clarification_patterns:
                if pattern.lower() in content.lower():
                    found_patterns += 1
                    details.append(f"Found clarification pattern: '{pattern}'")
            
            score = (found_patterns / len(clarification_patterns)) * 8
            
            if score < 6:
                issues.append("Insufficient clarification handling patterns")
        
        # Check for clarification attempt limits
        if "max 2 attempts" in content.lower() or "maximum 2" in content.lower():
            score += 2
            details.append("Found clarification attempt limits")
        else:
            issues.append("No clarification attempt limits specified")
        
        return {
            "compliant": score >= 7,
            "score": min(score, 10),
            "details": details,
            "issues": issues
        }
    
    def _validate_unknown_info_handling(self) -> Dict[str, Any]:
        """Validate unknown-info handling with graceful fallbacks."""
        score = 0
        details = []
        issues = []
        
        template_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                content = f.read()
            
            graceful_patterns = [
                "graceful fallbacks",
                "unknown requests",
                "out-of-scope",
                "helpful alternatives",
                "gracefully within your domain"
            ]
            
            found_patterns = 0
            for pattern in graceful_patterns:
                if pattern.lower() in content.lower():
                    found_patterns += 1
                    details.append(f"Found graceful handling pattern: '{pattern}'")
            
            score = (found_patterns / len(graceful_patterns)) * 10
            
            if score < 7:
                issues.append("Insufficient graceful fallback patterns")
        
        return {
            "compliant": score >= 7,
            "score": min(score, 10),
            "details": details,
            "issues": issues
        }
    
    def _validate_mid_conversation_transfer(self) -> Dict[str, Any]:
        """Validate mid-conversation transfer support."""
        score = 0
        details = []
        issues = []
        
        # Check conversation manager for transfer support
        conv_manager_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/conversation_manager.py"
        
        if os.path.exists(conv_manager_path):
            with open(conv_manager_path, 'r') as f:
                content = f.read()
            
            transfer_patterns = [
                "mid-conversation transfer",
                "context switch",
                "seamless handoff",
                "preserve context",
                "transfer support"
            ]
            
            found_patterns = 0
            for pattern in transfer_patterns:
                if pattern.lower() in content.lower():
                    found_patterns += 1
                    details.append(f"Found transfer pattern: '{pattern}'")
            
            score = (found_patterns / len(transfer_patterns)) * 8
        
        # Check for session context preservation
        session_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/session/conversation_session.py"
        if os.path.exists(session_path):
            score += 2
            details.append("Conversation session implementation found")
        else:
            issues.append("Conversation session implementation not found")
        
        return {
            "compliant": score >= 7,
            "score": min(score, 10),
            "details": details,
            "issues": issues
        }
    
    def _validate_three_path_decision_model(self) -> Dict[str, Any]:
        """Validate three-path decision model."""
        score = 0
        details = []
        issues = []
        
        template_path = "multi-agent-framework/llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
        
        if os.path.exists(template_path):
            with open(template_path, 'r') as f:
                content = f.read()
            
            decision_paths = [
                "direct help",
                "route",
                "clarify", 
                "out-of-scope",
                "three-path",
                "decision model"
            ]
            
            found_paths = 0
            for path in decision_paths:
                if path.lower() in content.lower():
                    found_paths += 1
                    details.append(f"Found decision path: '{path}'")
            
            score = (found_paths / len(decision_paths)) * 10
            
            if score < 7:
                issues.append("Incomplete three-path decision model implementation")
        
        return {
            "compliant": score >= 7,
            "score": min(score, 10),
            "details": details,
            "issues": issues
        }
    
    def _print_summary(self, summary: Dict[str, Any]):
        """Print validation summary."""
        print(f"\n📊 Validation Summary")
        print("=" * 40)
        print(f"Overall Compliance: {summary['overall_compliance']:.1f}%")
        print(f"Compliant Requirements: {summary['compliant_requirements']}/{summary['total_requirements']}")
        
        if summary['issues_found']:
            print(f"\n❌ Issues Found ({len(summary['issues_found'])}):")
            for i, issue in enumerate(summary['issues_found'], 1):
                print(f"  {i}. {issue}")
        
        if summary['overall_compliance'] >= 80:
            print(f"\n✅ OVERALL: COMPLIANT with Shubham's requirements")
        else:
            print(f"\n❌ OVERALL: NON-COMPLIANT with Shubham's requirements")
            print(f"   Recommendation: Address the issues listed above")
    
    def generate_compliance_report(self, summary: Dict[str, Any], output_file: str = "shubham_requirements_compliance.md"):
        """Generate a detailed compliance report."""
        report_content = f"""# Shubham's Requirements Compliance Report

## Overall Compliance: {summary['overall_compliance']:.1f}%

### Requirements Status
- **Compliant**: {summary['compliant_requirements']}/{summary['total_requirements']} requirements
- **Status**: {'✅ COMPLIANT' if summary['overall_compliance'] >= 80 else '❌ NON-COMPLIANT'}

## Detailed Results

"""
        
        for req_name, result in summary['detailed_results'].items():
            status = "✅ COMPLIANT" if result['compliant'] else "❌ NON-COMPLIANT"
            report_content += f"### {req_name}\n"
            report_content += f"- **Status**: {status}\n"
            report_content += f"- **Score**: {result['score']:.1f}/10\n"
            
            if result.get('details'):
                report_content += "- **Details**:\n"
                for detail in result['details']:
                    report_content += f"  - {detail}\n"
            
            if result.get('issues'):
                report_content += "- **Issues**:\n"
                for issue in result['issues']:
                    report_content += f"  - {issue}\n"
            
            report_content += "\n"
        
        if summary['issues_found']:
            report_content += "## Issues Summary\n\n"
            for i, issue in enumerate(summary['issues_found'], 1):
                report_content += f"{i}. {issue}\n"
        
        with open(output_file, 'w') as f:
            f.write(report_content)
        
        print(f"\n📄 Compliance report generated: {output_file}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Validate Shubham's Requirements")
    parser.add_argument("--detailed", action="store_true", help="Enable detailed validation output")
    parser.add_argument("--report", action="store_true", help="Generate compliance report")
    
    args = parser.parse_args()
    
    validator = ShubhamRequirementsValidator(detailed=args.detailed)
    summary = validator.validate_all_requirements()
    
    if args.report:
        validator.generate_compliance_report(summary)
    
    # Exit with appropriate code
    exit_code = 0 if summary['overall_compliance'] >= 80 else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
