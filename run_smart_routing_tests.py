#!/usr/bin/env python3
"""
Smart Routing Test Runner
========================

This script demonstrates how to run the comprehensive smart routing test suite
with different configurations and scenarios.

Usage Examples:
    # Run all tests
    python run_smart_routing_tests.py

    # Run only routing tests
    python run_smart_routing_tests.py --category routing

    # Run high priority tests only
    python run_smart_routing_tests.py --priority 1

    # Run with verbose output
    python run_smart_routing_tests.py --verbose

    # Run specific test scenarios
    python run_smart_routing_tests.py --scenario edge_cases
"""

import asyncio
import sys
import os
import argparse
from typing import Dict, Any

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from comprehensive_smart_routing_test_suite import SmartRoutingTestSuite, TestCategory


class TestRunner:
    """Test runner for smart routing test suite."""
    
    def __init__(self):
        """Initialize the test runner."""
        self.suite = None
        
    async def run_comprehensive_tests(self, args):
        """Run comprehensive test suite."""
        print("🚀 Starting Comprehensive Smart Routing Test Suite")
        print("=" * 60)
        
        # Initialize test suite
        self.suite = SmartRoutingTestSuite(verbose=args.verbose)
        
        # Determine test category
        category = TestCategory(args.category) if args.category != "all" else TestCategory.ALL
        
        # Run tests
        summary = await self.suite.run_test_suite(category, args.priority)
        
        # Print detailed results if requested
        if args.verbose:
            self._print_detailed_results(summary)
        
        return summary
    
    def _print_detailed_results(self, summary: Dict[str, Any]):
        """Print detailed test results."""
        print(f"\n📋 Detailed Test Results")
        print("=" * 40)
        
        for result in summary["results"]:
            test_case = result.test_case
            status_icon = "✅" if result.result.value == "PASSED" else "❌"
            
            print(f"\n{status_icon} {test_case.name}")
            print(f"   Category: {test_case.category.value}")
            print(f"   Description: {test_case.description}")
            print(f"   Execution Time: {result.execution_time:.2f}s")
            print(f"   Result: {result.result.value}")
            
            if result.error_message:
                print(f"   Error: {result.error_message}")
            
            if result.output and args.verbose:
                print(f"   Output: {result.output}")
    
    async def run_scenario_tests(self, scenario: str):
        """Run specific test scenarios."""
        print(f"🎯 Running Scenario: {scenario}")
        print("=" * 40)
        
        if scenario == "shubham_requirements":
            await self._run_shubham_requirements_scenario()
        elif scenario == "edge_cases":
            await self._run_edge_cases_scenario()
        elif scenario == "performance":
            await self._run_performance_scenario()
        elif scenario == "integration":
            await self._run_integration_scenario()
        else:
            print(f"❌ Unknown scenario: {scenario}")
    
    async def _run_shubham_requirements_scenario(self):
        """Run Shubham's requirements validation scenario."""
        print("📋 Validating Shubham's Requirements")
        print("-" * 40)
        
        # Test talk-back capability
        print("1. Testing talk-back conversation capability...")
        
        # Test clarification handling
        print("2. Testing clarification for ambiguous requests...")
        
        # Test unknown-info handling
        print("3. Testing unknown-info handling with graceful fallbacks...")
        
        # Test mid-conversation transfer
        print("4. Testing mid-conversation transfer support...")
        
        # Test three-path decision model
        print("5. Testing three-path decision model...")
        
        print("✅ Shubham's requirements validation complete")
    
    async def _run_edge_cases_scenario(self):
        """Run edge cases scenario."""
        print("🔍 Testing Edge Cases")
        print("-" * 40)
        
        edge_cases = [
            "Empty input handling",
            "Very long input handling", 
            "Special character handling",
            "Rapid context switching",
            "Agent unavailability",
            "Malformed requests"
        ]
        
        for i, case in enumerate(edge_cases, 1):
            print(f"{i}. {case}...")
        
        print("✅ Edge cases testing complete")
    
    async def _run_performance_scenario(self):
        """Run performance testing scenario."""
        print("⚡ Testing Performance")
        print("-" * 40)
        
        performance_tests = [
            "Routing decision speed",
            "Memory usage stability",
            "Concurrent request handling",
            "Large conversation handling"
        ]
        
        for i, test in enumerate(performance_tests, 1):
            print(f"{i}. {test}...")
        
        print("✅ Performance testing complete")
    
    async def _run_integration_scenario(self):
        """Run integration testing scenario."""
        print("🔗 Testing Integration")
        print("-" * 40)
        
        integration_tests = [
            "End-to-end conversation flows",
            "Multi-agent handoffs",
            "Configuration validation",
            "Template compliance"
        ]
        
        for i, test in enumerate(integration_tests, 1):
            print(f"{i}. {test}...")
        
        print("✅ Integration testing complete")
    
    def generate_test_report(self, summary: Dict[str, Any], output_file: str = None):
        """Generate a comprehensive test report."""
        if not output_file:
            output_file = "smart_routing_test_report.md"
        
        report_content = self._create_markdown_report(summary)
        
        with open(output_file, 'w') as f:
            f.write(report_content)
        
        print(f"📄 Test report generated: {output_file}")
    
    def _create_markdown_report(self, summary: Dict[str, Any]) -> str:
        """Create markdown test report."""
        report = f"""# Smart Routing Test Report

## Summary
- **Total Tests**: {summary['total_tests']}
- **Passed**: {summary['passed']} ✅
- **Failed**: {summary['failed']} ❌
- **Errors**: {summary['errors']} 🚨
- **Success Rate**: {summary['success_rate']:.1f}%
- **Total Execution Time**: {summary['total_time']:.2f}s

## Test Categories

### Routing Logic Tests
Tests for smart routing decisions and agent domain handling.

### Requirements Compliance Tests  
Validation of Shubham's specific requirements implementation.

### Integration Tests
End-to-end conversation flows and agent handoffs.

### Edge Cases Tests
Handling of malformed inputs and error scenarios.

### Performance Tests
Response time and memory usage validation.

## Detailed Results

"""
        
        for result in summary["results"]:
            status = "✅ PASSED" if result.result.value == "PASSED" else "❌ FAILED"
            report += f"### {result.test_case.name}\n"
            report += f"- **Status**: {status}\n"
            report += f"- **Category**: {result.test_case.category.value}\n"
            report += f"- **Description**: {result.test_case.description}\n"
            report += f"- **Execution Time**: {result.execution_time:.2f}s\n"
            
            if result.error_message:
                report += f"- **Error**: {result.error_message}\n"
            
            report += "\n"
        
        return report


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Smart Routing Test Runner")
    parser.add_argument("--category", choices=[c.value for c in TestCategory], default="all",
                       help="Test category to run")
    parser.add_argument("--priority", type=int, choices=[1, 2, 3], 
                       help="Run tests of specific priority")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--scenario", choices=["shubham_requirements", "edge_cases", "performance", "integration"],
                       help="Run specific test scenario")
    parser.add_argument("--report", action="store_true", help="Generate test report")
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.scenario:
        await runner.run_scenario_tests(args.scenario)
    else:
        summary = await runner.run_comprehensive_tests(args)
        
        if args.report:
            runner.generate_test_report(summary)


if __name__ == "__main__":
    asyncio.run(main())
