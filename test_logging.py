#!/usr/bin/env python3
"""
Test Triage Agent V2 Logging
============================

This script tests the logging functionality of the enhanced triage agent.
"""

import json
import sys
import os

# Add the project root to Python path
sys.path.append('.')

def test_prompt_generation_logging():
    """Test that prompt generation logging works."""
    print("🧪 Testing Triage Agent V2 Prompt Generation Logging")
    print("=" * 60)
    
    try:
        from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import (
            _get_triage_agent_v2_system_prompt
        )
        
        # Load test configuration
        config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"📋 [TEST] Loaded config for: {config.get('roster_id')}")
        print(f"🎛️  [TEST] V2 enabled: {config.get('use_triage_agent_v2')}")
        print(f"👤 [TEST] Avatar: {config.get('avatar_name')}")
        print(f"🏢 [TEST] Company: {config.get('company_name')}")
        
        print(f"\n🔧 [TEST] Calling _get_triage_agent_v2_system_prompt...")
        print("-" * 60)
        
        # This should trigger all the logging in the prompt generation
        system_prompt = _get_triage_agent_v2_system_prompt(config)
        
        print("-" * 60)
        print(f"✅ [TEST] Prompt generation completed successfully!")
        print(f"📏 [TEST] Final prompt length: {len(system_prompt)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_conversation_manager_logging():
    """Test conversation manager logging (without actually starting a session)."""
    print(f"\n🧪 Testing Conversation Manager Logging")
    print("=" * 60)
    
    try:
        # Just test the import and basic setup
        from llm_app.pipeline.steps.agentic_framework.autogen.conversation_manager import ConversationManager
        
        print(f"✅ [TEST] ConversationManager imported successfully")
        
        # Test configuration detection
        config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        use_v2 = config.get("use_triage_agent_v2", False)
        print(f"🎛️  [TEST] Configuration check: use_triage_agent_v2 = {use_v2}")
        
        if use_v2:
            print(f"✅ [TEST] V2 would be enabled with this configuration")
        else:
            print(f"⚠️  [TEST] V2 would NOT be enabled with this configuration")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_wrapper_instantiation():
    """Test that the V2 wrapper can be instantiated (without runtime dependencies)."""
    print(f"\n🧪 Testing Triage Agent V2 Wrapper Instantiation")
    print("=" * 60)
    
    try:
        # Test import
        from llm_app.pipeline.steps.agentic_framework.autogen.agent.triage_agent_v2_wrapper import TriageAgentV2Wrapper
        
        print(f"✅ [TEST] TriageAgentV2Wrapper imported successfully")
        
        # Load test configuration
        config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"📋 [TEST] Loaded config: {config.get('roster_id')}")
        print(f"🎛️  [TEST] V2 enabled: {config.get('use_triage_agent_v2')}")
        
        # Note: We can't actually instantiate the wrapper without runtime dependencies
        # But we can test that the class is available and the import works
        print(f"✅ [TEST] Wrapper class is available for instantiation")
        print(f"📝 [TEST] Class docstring: {TriageAgentV2Wrapper.__doc__[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all logging tests."""
    print("🎯 Triage Agent V2 Logging Test Suite")
    print("=" * 70)
    
    tests = [
        ("Prompt Generation Logging", test_prompt_generation_logging),
        ("Conversation Manager Logging", test_conversation_manager_logging),
        ("Wrapper Instantiation", test_wrapper_instantiation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All logging tests passed!")
        print("\n📋 When you run the application with V2 enabled, you should see:")
        print("  🔧 [TRIAGE_V2] Initialization logs")
        print("  📝 [PROMPT_GEN_V2] Prompt generation logs")
        print("  🚀 [CONVERSATION_MANAGER] V2 selection logs")
        print("  🎯 [TRIAGE_V2_HANDLER] Message processing logs")
        print("  🔀 [TRIAGE_V2_HANDLER] Delegation logs")
        print("  ✅ [TRIAGE_V2_HANDLER] Response logs")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
