#!/usr/bin/env python3
"""
Synthetic Data Generator for Smart Routing Tests
===============================================

This module generates large-scale synthetic datasets for comprehensive testing
of the smart routing logic across all categories mentioned by <PERSON><PERSON><PERSON>.

Categories:
1. Talk-back conversation capability
2. Clarification for ambiguous requests
3. Unknown-info handling with graceful fallbacks
4. Mid-conversation transfer support
5. Three-path decision model validation

Usage:
    python synthetic_data_generator.py --size 1000 --category all
    python synthetic_data_generator.py --size 500 --category talk_back
"""

import json
import random
import sys
import os
import argparse
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import itertools

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


class DataCategory(Enum):
    """Categories for synthetic data generation."""
    TALK_BACK = "talk_back"
    CLARIFICATION = "clarification"
    UNKNOWN_INFO = "unknown_info"
    MID_CONVERSATION = "mid_conversation"
    THREE_PATH = "three_path"
    ROUTING_LOGIC = "routing_logic"
    EDGE_CASES = "edge_cases"
    PERFORMANCE = "performance"
    ALL = "all"


@dataclass
class SyntheticTestCase:
    """Individual synthetic test case."""
    id: str
    category: DataCategory
    scenario: str
    input_data: Dict[str, Any]
    expected_output: Dict[str, Any]
    context: Dict[str, Any]
    priority: int
    complexity: str  # simple, medium, complex


class SyntheticDataGenerator:
    """Generator for synthetic test data."""
    
    def __init__(self):
        """Initialize the synthetic data generator."""
        self.test_cases = []
        self.conversation_templates = self._load_conversation_templates()
        self.domain_keywords = self._load_domain_keywords()
        self.ambiguous_patterns = self._load_ambiguous_patterns()
        self.unknown_requests = self._load_unknown_requests()
        
    def generate_dataset(self, size: int, category: DataCategory = DataCategory.ALL) -> List[SyntheticTestCase]:
        """
        Generate synthetic dataset.
        
        Args:
            size (int): Number of test cases to generate
            category (DataCategory): Category to focus on
            
        Returns:
            List[SyntheticTestCase]: Generated test cases
        """
        print(f"🔄 Generating {size} synthetic test cases for category: {category.value}")
        
        if category == DataCategory.ALL:
            # Distribute across all categories
            categories = [c for c in DataCategory if c != DataCategory.ALL]
            cases_per_category = size // len(categories)
            remainder = size % len(categories)
            
            for i, cat in enumerate(categories):
                cat_size = cases_per_category + (1 if i < remainder else 0)
                self.test_cases.extend(self._generate_category_data(cat_size, cat))
        else:
            self.test_cases.extend(self._generate_category_data(size, category))
        
        print(f"✅ Generated {len(self.test_cases)} synthetic test cases")
        return self.test_cases
    
    def _generate_category_data(self, size: int, category: DataCategory) -> List[SyntheticTestCase]:
        """Generate data for specific category."""
        cases = []
        
        if category == DataCategory.TALK_BACK:
            cases = self._generate_talk_back_data(size)
        elif category == DataCategory.CLARIFICATION:
            cases = self._generate_clarification_data(size)
        elif category == DataCategory.UNKNOWN_INFO:
            cases = self._generate_unknown_info_data(size)
        elif category == DataCategory.MID_CONVERSATION:
            cases = self._generate_mid_conversation_data(size)
        elif category == DataCategory.THREE_PATH:
            cases = self._generate_three_path_data(size)
        elif category == DataCategory.ROUTING_LOGIC:
            cases = self._generate_routing_logic_data(size)
        elif category == DataCategory.EDGE_CASES:
            cases = self._generate_edge_cases_data(size)
        elif category == DataCategory.PERFORMANCE:
            cases = self._generate_performance_data(size)
        
        return cases
    
    def _generate_talk_back_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate talk-back conversation capability test cases."""
        cases = []
        
        # Base patterns for talk-back scenarios
        talk_back_scenarios = [
            "initial_greeting",
            "help_request",
            "service_inquiry",
            "problem_statement",
            "general_question"
        ]
        
        user_inputs = [
            "Hi there, I need some help",
            "Hello, can you assist me?",
            "I have a question about my flight",
            "Good morning, I need help with something",
            "Can someone help me please?",
            "I'm looking for assistance",
            "Hi, I need support",
            "Hello, I have an issue",
            "Can you help me with my booking?",
            "I need some information"
        ]
        
        expected_responses = [
            "I'd be happy to help you! What specifically can I assist you with today?",
            "Hello! I'm here to help. Could you tell me more about what you need?",
            "Good day! I'd be glad to assist you. What can I help you with?",
            "Hi there! I'm ready to help. What would you like assistance with?",
            "Welcome! I'm here to support you. How can I help today?"
        ]
        
        for i in range(size):
            scenario = random.choice(talk_back_scenarios)
            user_input = random.choice(user_inputs)
            expected_response = random.choice(expected_responses)
            
            case = SyntheticTestCase(
                id=f"talk_back_{i+1:04d}",
                category=DataCategory.TALK_BACK,
                scenario=scenario,
                input_data={
                    "user_input": user_input,
                    "context": "initial_interaction",
                    "agent": "Triage_Agent_V2"
                },
                expected_output={
                    "has_conversational_response": True,
                    "response_length_min": 20,
                    "includes_follow_up_question": True,
                    "tone": "helpful_and_professional",
                    "expected_pattern": "help.*assist.*what.*need"
                },
                context={
                    "conversation_stage": "initial",
                    "user_intent": "seeking_help",
                    "complexity": "simple"
                },
                priority=1,
                complexity="simple"
            )
            cases.append(case)
        
        return cases
    
    def _generate_clarification_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate clarification handling test cases."""
        cases = []
        
        ambiguous_inputs = [
            "I have a problem",
            "Something is wrong",
            "I need help",
            "There's an issue",
            "Can you fix this?",
            "I'm having trouble",
            "This isn't working",
            "I need assistance",
            "Help me please",
            "Something's not right"
        ]
        
        clarification_questions = [
            "Could you tell me more about what specific problem you're experiencing?",
            "I'd be happy to help! What kind of issue are you having?",
            "Can you provide more details about what's not working?",
            "What specifically would you like assistance with?",
            "Could you describe the trouble you're experiencing?"
        ]
        
        for i in range(size):
            user_input = random.choice(ambiguous_inputs)
            expected_clarification = random.choice(clarification_questions)
            
            case = SyntheticTestCase(
                id=f"clarification_{i+1:04d}",
                category=DataCategory.CLARIFICATION,
                scenario="ambiguous_request",
                input_data={
                    "user_input": user_input,
                    "context": "ambiguous_request",
                    "agent": "Triage_Agent_V2",
                    "attempt_number": 1
                },
                expected_output={
                    "asks_clarification": True,
                    "max_attempts": 2,
                    "clarification_pattern": "could you.*more.*specific",
                    "maintains_helpful_tone": True,
                    "provides_examples": False
                },
                context={
                    "ambiguity_level": "high",
                    "domain_context": "airline",
                    "user_frustration": "low"
                },
                priority=1,
                complexity="medium"
            )
            cases.append(case)
        
        return cases
    
    def _load_conversation_templates(self) -> Dict[str, List[str]]:
        """Load conversation templates for different scenarios."""
        return {
            "booking_flow": [
                "I want to book a flight",
                "Where would you like to go?",
                "To New York",
                "When would you like to travel?"
            ],
            "status_flow": [
                "What's my flight status?",
                "I can help you check your flight status. What's your flight number?",
                "AA123",
                "Let me check that for you"
            ],
            "support_flow": [
                "I need help with my booking",
                "I'd be happy to help with your booking. What specific assistance do you need?",
                "I want to change my seat",
                "I can help you with seat changes"
            ]
        }
    
    def _load_domain_keywords(self) -> Dict[str, List[str]]:
        """Load domain-specific keywords."""
        return {
            "booking": ["book", "flight", "travel", "destination", "ticket", "reservation"],
            "status": ["status", "delayed", "cancelled", "on-time", "departure", "arrival"],
            "support": ["help", "change", "cancel", "refund", "seat", "baggage"],
            "out_of_scope": ["weather", "hotel", "car", "insurance", "visa", "restaurant"]
        }
    
    def _load_ambiguous_patterns(self) -> List[str]:
        """Load patterns that indicate ambiguous requests."""
        return [
            "I have a problem",
            "Something is wrong", 
            "I need help",
            "There's an issue",
            "Can you help me?",
            "I'm having trouble",
            "This isn't working",
            "Something's not right"
        ]
    
    def _load_unknown_requests(self) -> List[str]:
        """Load requests that are outside the airline domain."""
        return [
            "What's the weather like?",
            "Can you book me a hotel?",
            "I need car rental information",
            "Help with my travel insurance",
            "Restaurant recommendations",
            "Currency exchange rates",
            "Local transportation options",
            "Tourist attractions",
            "Visa requirements",
            "Pet travel documents"
        ]

    def _generate_unknown_info_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate unknown-info handling test cases."""
        cases = []

        unknown_requests = [
            "What's the weather like in Paris?",
            "Can you help me book a hotel?",
            "I need car rental information",
            "Help with my travel insurance claim",
            "Where can I find good restaurants?",
            "What's the currency exchange rate?",
            "Can you help with my visa application?",
            "I need pet travel documents",
            "Local transportation options?",
            "Tourist attraction recommendations"
        ]

        for i in range(size):
            unknown_request = random.choice(unknown_requests)

            case = SyntheticTestCase(
                id=f"unknown_info_{i+1:04d}",
                category=DataCategory.UNKNOWN_INFO,
                scenario="out_of_domain_request",
                input_data={
                    "user_input": unknown_request,
                    "context": "out_of_domain",
                    "agent": "Triage_Agent_V2"
                },
                expected_output={
                    "provides_graceful_fallback": True,
                    "suggests_alternatives": True,
                    "maintains_helpful_tone": True,
                    "acknowledges_limitation": True,
                    "offers_relevant_services": True
                },
                context={
                    "domain_relevance": "none",
                    "fallback_type": "graceful",
                    "alternative_suggestions": True
                },
                priority=1,
                complexity="medium"
            )
            cases.append(case)

        return cases

    def _generate_mid_conversation_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate mid-conversation transfer test cases."""
        cases = []

        conversation_contexts = [
            {
                "initial_flow": "booking",
                "switch_to": "status",
                "history": [
                    {"role": "user", "content": "I want to book a flight"},
                    {"role": "assistant", "content": "I'd be happy to help you book a flight. Where would you like to go?"}
                ],
                "switch_input": "Actually, what's my current flight status?"
            },
            {
                "initial_flow": "status",
                "switch_to": "booking",
                "history": [
                    {"role": "user", "content": "What's my flight status?"},
                    {"role": "assistant", "content": "I can help you check your flight status. What's your flight number?"}
                ],
                "switch_input": "Never mind, I want to book a new flight instead"
            },
            {
                "initial_flow": "booking",
                "switch_to": "support",
                "history": [
                    {"role": "user", "content": "Book me a flight to London"},
                    {"role": "assistant", "content": "I'll help you book a flight to London. When would you like to travel?"}
                ],
                "switch_input": "Actually, I need to change my existing booking first"
            }
        ]

        for i in range(size):
            context = random.choice(conversation_contexts)

            case = SyntheticTestCase(
                id=f"mid_conversation_{i+1:04d}",
                category=DataCategory.MID_CONVERSATION,
                scenario="context_switch",
                input_data={
                    "conversation_history": context["history"],
                    "user_input": context["switch_input"],
                    "current_agent": f"{context['initial_flow'].title()}_Agent",
                    "context": "mid_conversation_switch"
                },
                expected_output={
                    "supports_transfer": True,
                    "preserves_context": True,
                    "seamless_handoff": True,
                    "target_agent": f"{context['switch_to'].title()}_Agent",
                    "acknowledges_switch": True
                },
                context={
                    "initial_flow": context["initial_flow"],
                    "target_flow": context["switch_to"],
                    "conversation_depth": len(context["history"]),
                    "switch_type": "explicit"
                },
                priority=1,
                complexity="complex"
            )
            cases.append(case)

        return cases

    def _generate_three_path_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate three-path decision model test cases."""
        cases = []

        decision_scenarios = [
            {
                "input": "I want to book a flight to New York",
                "expected_path": "route",
                "target_agent": "Flight_Booking_Agent",
                "confidence": "high"
            },
            {
                "input": "What's my flight status for AA123?",
                "expected_path": "route",
                "target_agent": "Flight_Status_Agent",
                "confidence": "high"
            },
            {
                "input": "I have a question",
                "expected_path": "clarify",
                "target_agent": "Triage_Agent_V2",
                "confidence": "medium"
            },
            {
                "input": "I need help",
                "expected_path": "clarify",
                "target_agent": "Triage_Agent_V2",
                "confidence": "medium"
            },
            {
                "input": "What's the weather like?",
                "expected_path": "out_of_scope",
                "target_agent": "Triage_Agent_V2",
                "confidence": "high"
            },
            {
                "input": "Can you book me a hotel?",
                "expected_path": "out_of_scope",
                "target_agent": "Triage_Agent_V2",
                "confidence": "high"
            }
        ]

        for i in range(size):
            scenario = random.choice(decision_scenarios)

            case = SyntheticTestCase(
                id=f"three_path_{i+1:04d}",
                category=DataCategory.THREE_PATH,
                scenario="decision_path_validation",
                input_data={
                    "user_input": scenario["input"],
                    "context": "path_decision",
                    "agent": "Triage_Agent_V2"
                },
                expected_output={
                    "decision_path": scenario["expected_path"],
                    "target_agent": scenario["target_agent"],
                    "confidence_level": scenario["confidence"],
                    "reasoning_provided": True,
                    "path_accuracy": True
                },
                context={
                    "decision_complexity": "standard",
                    "domain_clarity": scenario["confidence"],
                    "expected_outcome": scenario["expected_path"]
                },
                priority=1,
                complexity="medium"
            )
            cases.append(case)

        return cases

    def _generate_routing_logic_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate routing logic test cases."""
        cases = []

        routing_scenarios = [
            {
                "current_agent": "Flight_Booking_Agent",
                "user_input": "I want to book a flight",
                "should_route": False,
                "reason": "within_domain"
            },
            {
                "current_agent": "Flight_Booking_Agent",
                "user_input": "My flight is delayed",
                "should_route": True,
                "reason": "out_of_scope",
                "target": "Triage_Agent_V2"
            },
            {
                "current_agent": "Flight_Status_Agent",
                "user_input": "What's my flight status?",
                "should_route": False,
                "reason": "within_domain"
            },
            {
                "current_agent": "Flight_Status_Agent",
                "user_input": "I want to book another flight",
                "should_route": True,
                "reason": "out_of_scope",
                "target": "Triage_Agent_V2"
            }
        ]

        for i in range(size):
            scenario = random.choice(routing_scenarios)

            case = SyntheticTestCase(
                id=f"routing_logic_{i+1:04d}",
                category=DataCategory.ROUTING_LOGIC,
                scenario="agent_routing_decision",
                input_data={
                    "current_agent": scenario["current_agent"],
                    "user_input": scenario["user_input"],
                    "context": "routing_decision"
                },
                expected_output={
                    "should_route": scenario["should_route"],
                    "routing_reason": scenario["reason"],
                    "target_agent": scenario.get("target", scenario["current_agent"]),
                    "decision_confidence": "high"
                },
                context={
                    "agent_domain": scenario["current_agent"].split("_")[1].lower(),
                    "request_type": scenario["reason"],
                    "routing_complexity": "standard"
                },
                priority=1,
                complexity="simple"
            )
            cases.append(case)

        return cases

    def _generate_edge_cases_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate edge cases test data."""
        cases = []

        edge_case_inputs = [
            "",  # Empty input
            "   ",  # Whitespace only
            "🚀✈️🎯",  # Emoji only
            "a" * 1000,  # Very long input
            "SELECT * FROM users;",  # SQL injection attempt
            "<script>alert('xss')</script>",  # XSS attempt
            "NULL",  # Null string
            "undefined",  # Undefined string
            "*********",  # Numbers only
            "!@#$%^&*()",  # Special characters only
        ]

        for i in range(size):
            edge_input = random.choice(edge_case_inputs)

            case = SyntheticTestCase(
                id=f"edge_case_{i+1:04d}",
                category=DataCategory.EDGE_CASES,
                scenario="malformed_input",
                input_data={
                    "user_input": edge_input,
                    "context": "edge_case_testing",
                    "agent": "Triage_Agent_V2"
                },
                expected_output={
                    "handles_gracefully": True,
                    "no_errors": True,
                    "appropriate_response": True,
                    "maintains_security": True,
                    "provides_fallback": True
                },
                context={
                    "input_type": "malformed",
                    "security_risk": "potential",
                    "handling_complexity": "high"
                },
                priority=2,
                complexity="complex"
            )
            cases.append(case)

        return cases

    def _generate_performance_data(self, size: int) -> List[SyntheticTestCase]:
        """Generate performance test data."""
        cases = []

        performance_scenarios = [
            {
                "test_type": "response_time",
                "request_count": 100,
                "max_response_time": 2.0,
                "concurrent_requests": 10
            },
            {
                "test_type": "memory_usage",
                "conversation_length": 50,
                "max_memory_growth": 0.1
            },
            {
                "test_type": "throughput",
                "requests_per_second": 50,
                "duration_seconds": 60
            }
        ]

        for i in range(size):
            scenario = random.choice(performance_scenarios)

            case = SyntheticTestCase(
                id=f"performance_{i+1:04d}",
                category=DataCategory.PERFORMANCE,
                scenario=scenario["test_type"],
                input_data={
                    "test_type": scenario["test_type"],
                    "parameters": scenario,
                    "context": "performance_testing"
                },
                expected_output={
                    "meets_performance_criteria": True,
                    "response_time_acceptable": True,
                    "memory_usage_stable": True,
                    "no_performance_degradation": True
                },
                context={
                    "performance_category": scenario["test_type"],
                    "load_level": "standard",
                    "measurement_type": "automated"
                },
                priority=3,
                complexity="complex"
            )
            cases.append(case)

        return cases

    def save_dataset(self, filename: str = None) -> str:
        """Save generated dataset to JSON file."""
        if not filename:
            filename = f"synthetic_dataset_{len(self.test_cases)}_cases.json"

        # Convert test cases to JSON-serializable format
        dataset = {
            "metadata": {
                "total_cases": len(self.test_cases),
                "categories": list(set(case.category.value for case in self.test_cases)),
                "generation_timestamp": "2025-06-16T20:00:00Z",
                "version": "1.0"
            },
            "test_cases": [
                {
                    "id": case.id,
                    "category": case.category.value,
                    "scenario": case.scenario,
                    "input_data": case.input_data,
                    "expected_output": case.expected_output,
                    "context": case.context,
                    "priority": case.priority,
                    "complexity": case.complexity
                }
                for case in self.test_cases
            ]
        }

        with open(filename, 'w') as f:
            json.dump(dataset, f, indent=2)

        print(f"💾 Dataset saved to: {filename}")
        return filename

    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the generated dataset."""
        if not self.test_cases:
            return {"error": "No test cases generated"}

        stats = {
            "total_cases": len(self.test_cases),
            "categories": {},
            "complexity_distribution": {},
            "priority_distribution": {},
            "scenarios": {}
        }

        for case in self.test_cases:
            # Category distribution
            cat = case.category.value
            stats["categories"][cat] = stats["categories"].get(cat, 0) + 1

            # Complexity distribution
            comp = case.complexity
            stats["complexity_distribution"][comp] = stats["complexity_distribution"].get(comp, 0) + 1

            # Priority distribution
            prio = case.priority
            stats["priority_distribution"][prio] = stats["priority_distribution"].get(prio, 0) + 1

            # Scenario distribution
            scenario = case.scenario
            stats["scenarios"][scenario] = stats["scenarios"].get(scenario, 0) + 1

        return stats


def main():
    """Main function for synthetic data generation."""
    parser = argparse.ArgumentParser(description="Generate synthetic test data for smart routing")
    parser.add_argument("--size", type=int, default=1000, help="Number of test cases to generate")
    parser.add_argument("--category", choices=[c.value for c in DataCategory], default="all",
                       help="Category of test cases to generate")
    parser.add_argument("--output", type=str, help="Output filename for the dataset")
    parser.add_argument("--stats", action="store_true", help="Show dataset statistics")

    args = parser.parse_args()

    print("🔄 Synthetic Data Generator for Smart Routing Tests")
    print("=" * 60)

    # Initialize generator
    generator = SyntheticDataGenerator()

    # Generate dataset
    category = DataCategory(args.category)
    test_cases = generator.generate_dataset(args.size, category)

    # Save dataset
    filename = generator.save_dataset(args.output)

    # Show statistics
    if args.stats:
        stats = generator.get_statistics()
        print(f"\n📊 Dataset Statistics")
        print("=" * 40)
        print(f"Total Cases: {stats['total_cases']}")

        print(f"\nCategory Distribution:")
        for cat, count in stats['categories'].items():
            percentage = (count / stats['total_cases']) * 100
            print(f"  {cat}: {count} ({percentage:.1f}%)")

        print(f"\nComplexity Distribution:")
        for comp, count in stats['complexity_distribution'].items():
            percentage = (count / stats['total_cases']) * 100
            print(f"  {comp}: {count} ({percentage:.1f}%)")

        print(f"\nPriority Distribution:")
        for prio, count in stats['priority_distribution'].items():
            percentage = (count / stats['total_cases']) * 100
            print(f"  Priority {prio}: {count} ({percentage:.1f}%)")

    print(f"\n✅ Synthetic dataset generation complete!")
    print(f"📁 Dataset file: {filename}")
    print(f"📊 Total test cases: {len(test_cases)}")

    return filename, test_cases


if __name__ == "__main__":
    main()
