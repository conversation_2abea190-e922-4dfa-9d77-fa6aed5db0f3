{"roster_id": "RozieInsurance", "time_zone": "America/New_York", "use_case_domain": "Insurance", "company_name": "<PERSON><PERSON><PERSON>", "avatar_name": "<PERSON><PERSON><PERSON>", "knowledge_base_agent": false, "knowledge_base_config": {"illuminar_config": {}, "function_map": [], "knowledge_base_topics": [], "knowledge_snippets": []}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to Rozie Insurance! I'm <PERSON><PERSON><PERSON>, a virtual assistant for guests. Please describe how I can help you."}], "profile_found": [{"type": "text", "message": "Hello {<PERSON><PERSON><PERSON><PERSON>} Welcome back to Rozie Insurance! Remember me? I'm <PERSON><PERSON><PERSON>, a virtual assistant. Briefly describe how I can help you."}]}, "workflows": {"Insurance_First_Notice_Of_Loss": ["Trigger this workflow if the user's intent revolves around reporting a First Notice Of Loss incident or an accident.", "scope exclusions: claims / claim filing, modification to policy details or any other services not related to First Notice Of Loss."]}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_base": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1"}}