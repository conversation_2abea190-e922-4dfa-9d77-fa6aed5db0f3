service: multi-agent-framework-stream-resources

custom:
  dev:
    LayerBucket: scc-layers
  demo:
    LayerBucket: scc-layers
  test:
    LayerBucket: scc-layers

provider:
  name: aws
  runtime: python3.10
  stage: ${opt:stage, "dev1"}
  region: ${opt:region, "ca-central-1"}
  endpointType: regional
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:*
      Resource: "*"

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    FrameWorkPhoneConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: phone_number
            AttributeType: S
        KeySchema:
          - AttributeName: phone_number
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-phone-config-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    FrameWorkBusinessConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: config_id
            AttributeType: S
        KeySchema:
          - AttributeName: config_id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-business-config-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    GptDirectPocAttributeTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: attribute_set_id
            AttributeType: S
        KeySchema:
          - KeyType: HASH
            AttributeName: attribute_set_id
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-gpt-direct-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True