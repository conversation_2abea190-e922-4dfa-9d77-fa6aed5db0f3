name: Multi Agent Framework Dev Deployment

on:
  push:
    branches:
      - develop
    paths:
      - multi-agent-framework/**
      - multi-agent-framework-resources/**
      - stream-resources/**
      - stream/**
      - langfuse/**
jobs:
  # JOB to run change detection
  changes:
    runs-on: ubuntu-latest
    # Set job outputs to values from filter step
    outputs:
      multi-agent-framework: ${{steps.filter.outputs.multi-agent-framework}}
      multi-agent-framework-resources: ${{steps.filter.outputs.multi-agent-framework-resources}}
      stream-resources: ${{steps.filter.outputs.stream-resources}}
      stream: ${{steps.filter.outputs.stream}}
      langfuse: ${{steps.filter.outputs.langfuse}}
    # For pull requests it's not necessary to checkout the code
    steps:
      - uses: actions/checkout@v2
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          base: ${{ github.ref }}
          filters: |
            multi-agent-framework:
              - 'multi-agent-framework/**'
            multi-agent-framework-resources:
              - 'multi-agent-framework-resources/**'
            stream-resources:
              - 'stream-resources/**'
            stream:
              - 'stream/**'
            langfuse:
              - 'langfuse/**'

  # JOB to build multi-agent-framework
  multi-agent-framework:
    needs: changes
    if: ${{ needs.changes.outputs.multi-agent-framework == 'true' }}
    name: Build and Deploy multi-agent-framework lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      APP_API_KEY: ${{ secrets.APP_API_KEY }}
      IMAGE_TAG: v${{ github.sha }}
      OPENAI_API_KEY: ${{secrets.OPENAI_API_KEY}}
      LANGFUSE_SECRET_KEY: ${{secrets.LANGFUSE_SECRET_KEY}}
      LANGFUSE_PUBLIC_KEY: ${{secrets.LANGFUSE_PUBLIC_KEY}}
      GEMINI_API_KEY: ${{secrets.GEMINI_API_KEY}}
      GROQ_API_KEY: ${{secrets.GROQ_API_KEY}}
      ANTHROPIC_API_KEY: ${{secrets.ANTHROPIC_API_KEY}}
      LANGFUSE_HOST: ${{secrets.LANGFUSE_HOST}}
      ENABLE_LOGS: ${{vars.ENABLE_LOGS}}
      LOG_LEVEL: ${{vars.LOG_LEVEL}}
      DEPLOYMENT_TYPE: ${{vars.DEPLOYMENT_TYPE}}
      domain-name: ${{vars.DOMAIN_NAME}}
      ECR_REPOSITORY: ${{vars.ECR_REPOSITORY}}
      ANALYTICS_URL: ${{vars.ANALYTICS_URL}}
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for insights
        run: |
          echo "$ECR_REPOSITORY-$environment"
          aws ecr describe-repositories --repository-names $ECR_REPOSITORY-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ECR_REPOSITORY-$environment
      - name: Build, tag, and push ECR insights image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          echo "$configtable"
          cd multi-agent-framework
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg APP_API_KEY=$APP_API_KEY \
            --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
            --build-arg LANGFUSE_SECRET_KEY=$LANGFUSE_SECRET_KEY \
            --build-arg LANGFUSE_PUBLIC_KEY=$LANGFUSE_PUBLIC_KEY \
            --build-arg LANGFUSE_HOST=$LANGFUSE_HOST \
            --build-arg ENABLE_LOGS=$ENABLE_LOGS \
            --build-arg GEMINI_API_KEY=$GEMINI_API_KEY \
            --build-arg GROQ_API_KEY=$GROQ_API_KEY \
            --build-arg ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY \
            --build-arg LOG_LEVEL=$LOG_LEVEL \
            --build-arg DEPLOYMENT_TYPE=$DEPLOYMENT_TYPE \
            --build-arg ANALYTICS_URL=$ANALYTICS_URL \
            -t "$ECR_REGISTRY/$ECR_REPOSITORY-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ECR_REPOSITORY-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev multi-agent-framework API
        env:
          ECR_REPO_URI: ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd multi-agent-framework && sls deploy --region ${{ secrets.AWS_REGION }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name

  # JOB to build multi-agent-framework-resources
  multi-agent-framework-resources:
    needs: changes
    if: ${{ needs.changes.outputs.multi-agent-framework-resources == 'true' }}
    name: Build and Deploy multi-agent-framework-resources lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Deploy dev multi-agent-framework-resources API
        run: cd multi-agent-framework-resources && sls deploy --region ca-central-1 --stage dev

  # JOB to stream-resources
  stream-resources:
    needs: changes
    if: ${{ needs.changes.outputs.stream-resources == 'true' }}
    name: Build and Deploy stream-resources
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Deploy dev stream-resources API
        run: cd stream-resources && sls deploy --region ca-central-1 --stage dev 
  
  # JOB to build stream
  stream:
    needs: changes
    if: ${{ needs.changes.outputs.stream == 'true' }}
    name: Build and Deploy stream lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: ${{ vars.STREAM_ECR_REPOSITORY }}
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: ${{vars.STREAM_SUB_DOMAIN_NAME}}.${{vars.STREAM_DOMAIN_NAME}}
      maindomain_name: ${{vars.STREAM_DOMAIN_NAME}}
      subdomain_name: ${{vars.STREAM_SUB_DOMAIN_NAME}}
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      aws-account-id: ${{secrets.AWS_ACCOUNT_ID}}
      TWILIO_ACCOUNT_SID: ${{secrets.TWILIO_ACCOUNT_SID}}
      TWILIO_AUTH_TOKEN: ${{secrets.TWILIO_AUTH_TOKEN}}
      MULTI_AGENT_FRAMEWORK_URL: ${{vars.MULTI_AGENT_FRAMEWORK_URL}}
      MULTI_AGENT_FRAMEWORK_API_KEY: ${{ secrets.APP_API_KEY }}
      IMAGE_TAG: v${{ github.sha }}
      OPENAI_API_KEY: ${{secrets.OPENAI_API_KEY}}
      DEEPGRAM_API_KEY: ${{secrets.DEEPGRAM_API_KEY}}
      APP_API_KEY: ${{ secrets.APP_API_KEY }}
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for insights
        run: | 
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR insights image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          echo "$configtable"
          cd stream
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID \
            --build-arg TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN \
            --build-arg APP_API_KEY=$APP_API_KEY \
            --build-arg MULTI_AGENT_FRAMEWORK_URL=$MULTI_AGENT_FRAMEWORK_URL \
            --build-arg MULTI_AGENT_FRAMEWORK_API_KEY=$MULTI_AGENT_FRAMEWORK_API_KEY \
            --build-arg DOMAIN_NAME=$maindomain_name \
            --build-arg STREAM_SUB_DOMAIN=$subdomain_name \
            --build-arg DEEPGRAM_API_KEY=$DEEPGRAM_API_KEY \
            --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT 
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev stream ECS
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd stream && sls deploy --region ${{ secrets.AWS_REGION }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name
  
  # JOB to run langfuse
  langfuse:
    needs: changes
    if: ${{ needs.changes.outputs.langfuse == 'true' }}
    name: Build and Deploy langfuse
    runs-on: ubuntu-latest
    environment: dev
    env:
      ENVIRONMENT: dev
      ECR_REPOSITORY: ${{ vars.LANGFUSE_ECR_REPOSITORY_NAME }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      DOMAIN_NAME: ${{ vars.LANGFUSE_DOMAIN_NAME }}
      IMAGE_TAG: v${{ github.sha }}
      DB_USERNAME: ${{ vars.LANGFUSE_DB_USERNAME }}
      DB_PASSWORD: ${{ secrets.LANGFUSE_DB_PASSWORD }}
      NEXTAUTH_SECRET: ${{secrets.NEXTAUTH_SECRET}}
      SALT: ${{secrets.SALT}}
      NEXTAUTH_URL: ${{vars.NEXTAUTH_URL}}
      IMAGE_URL: ${{ vars.LANGFUSE_IMAGE_URL }}
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0

      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $AWS_ACCESS_KEY_ID --secret $AWS_SECRET_ACCESS_KEY

      - name: Deploy Resources
        env:
          IMAGE_URL: ${{ env.IMAGE_URL }}
          DOMAIN_NAME: ${{ env.DOMAIN_NAME }}
          DB_USERNAME: ${{ env.DB_USERNAME }}
          DB_PASSWORD: ${{ env.DB_PASSWORD }}
          NEXTAUTH_SECRET: ${{env.NEXTAUTH_SECRET}}
          SALT: ${{env.SALT}}
        run: cd langfuse && sls deploy --region $AWS_REGION --stage $ENVIRONMENT -E IMAGE_URL=$IMAGE_URL -E DOMAIN_NAME=$DOMAIN_NAME -E DB_USERNAME=$DB_USERNAME -E DB_PASSWORD=$DB_PASSWORD -E NEXTAUTH_SECRET=$NEXTAUTH_SECRET -E SALT=$SALT -E NEXTAUTH_URL=$NEXTAUTH_URL -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn