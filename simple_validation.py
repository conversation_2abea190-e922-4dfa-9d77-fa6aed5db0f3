#!/usr/bin/env python3
"""
Simple Triage Agent V2 Validation
=================================

This script validates the core implementation without external dependencies.
Focuses on <PERSON><PERSON><PERSON>'s requirements compliance.
"""

import json
import os


def validate_template_content():
    """Validate the triage agent V2 template content."""
    print("📋 Validating Triage Agent V2 Template")
    print("=" * 50)
    
    template_path = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
    
    if not os.path.exists(template_path):
        print(f"❌ Template file not found: {template_path}")
        return False
    
    with open(template_path, 'r') as f:
        content = f.read()
    
    # Check for <PERSON><PERSON><PERSON>'s required components
    required_components = {
        "Conversational Response": "STEP 1: CONVERSATIONAL RESPONSE",
        "Decision Tree": "STEP 2: DECISION TREE", 
        "Direct Help": "A. DIRECT HELP",
        "Route": "B. ROUTE",
        "Clarify": "C. CLARIFY",
        "Out-of-Scope": "D. OUT-OF-SCOPE",
        "Available Workflows": "AVAILABLE WORKFLOWS",
        "Conversation Rules": "CONVERSATION RULES",
        "Mid-Conversation Transfer": "MID-CONVERSATION TRANSFER SUPPORT",
        "Unknown-Info Handling": "UNKNOWN-INFO HANDLING"
    }
    
    missing_components = []
    for name, pattern in required_components.items():
        if pattern not in content:
            missing_components.append(name)
        else:
            print(f"  ✅ {name}")
    
    if missing_components:
        print(f"❌ Missing components: {missing_components}")
        return False
    
    # Check for forbidden elements (confidence scoring)
    forbidden_patterns = [
        "confidence",
        "MMedAgent",
        "0.85",
        "0.5",
        "HIGH CONFIDENCE",
        "MEDIUM CONFIDENCE", 
        "LOW CONFIDENCE"
    ]
    
    found_forbidden = []
    for pattern in forbidden_patterns:
        if pattern.lower() in content.lower():
            found_forbidden.append(pattern)
    
    if found_forbidden:
        print(f"❌ Found forbidden confidence scoring patterns: {found_forbidden}")
        return False
    
    print("✅ Template validation passed!")
    return True


def validate_configuration_support():
    """Validate that configuration supports V2 features."""
    print("\n⚙️ Validating Configuration Support")
    print("=" * 50)
    
    config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
    
    if not os.path.exists(config_path):
        print(f"❌ V2 configuration not found: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    # Check V2 enablement
    if not config.get("use_triage_agent_v2", False):
        print("❌ Triage Agent V2 not enabled in configuration")
        return False
    
    print("✅ V2 enabled in configuration")
    
    # Check required fields for Shubham's requirements
    required_fields = {
        "avatar_name": "Agent persona",
        "company_name": "Company branding",
        "use_case_domain": "Domain context",
        "time_zone": "Timezone info",
        "channel_guidelines": "Channel-specific rules"
    }
    
    missing_fields = []
    for field, description in required_fields.items():
        if field not in config:
            missing_fields.append(f"{field} ({description})")
        else:
            print(f"  ✅ {field}: {config[field]}")
    
    if missing_fields:
        print(f"❌ Missing required fields: {missing_fields}")
        return False
    
    # Check workflow format (should be dict with name/description)
    workflows = config.get("workflows", {})
    if not workflows:
        print("❌ No workflows defined")
        return False
    
    workflow_format_correct = True
    for workflow_id, workflow_data in workflows.items():
        if not isinstance(workflow_data, dict):
            print(f"❌ Workflow {workflow_id} not in dict format")
            workflow_format_correct = False
        elif "name" not in workflow_data or "description" not in workflow_data:
            print(f"❌ Workflow {workflow_id} missing name or description")
            workflow_format_correct = False
    
    if workflow_format_correct:
        print(f"✅ All {len(workflows)} workflows properly formatted")
    
    return workflow_format_correct


def validate_shubham_requirements_compliance():
    """Validate compliance with Shubham's specific requirements."""
    print("\n📋 Validating Shubham's Requirements Compliance")
    print("=" * 50)
    
    # Check template for specific requirements
    template_path = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/triage_agent_v2.txt"
    
    with open(template_path, 'r') as f:
        template_content = f.read()
    
    requirements_check = {
        "Talk-Back Conversation (B-1)": "Always respond conversationally first",
        "Clarification (B-2)": "max 2 attempts",
        "Unknown-Info Handling (B-3)": "UNKNOWN-INFO HANDLING",
        "Out-of-Scope Grace (B-4)": "OUT-OF-SCOPE",
        "Mid-Conversation Transfer (B-5)": "MID-CONVERSATION TRANSFER"
    }
    
    compliance_score = 0
    for requirement, pattern in requirements_check.items():
        if pattern in template_content:
            print(f"  ✅ {requirement}")
            compliance_score += 1
        else:
            print(f"  ❌ {requirement} - Pattern '{pattern}' not found")
    
    # Check for forbidden features
    forbidden_check = {
        "Confidence Scoring": ["confidence", "MMedAgent", "0.85"],
        "BAML Integration": ["baml", "BAML"],
        "Complex JSON": ["```json", "JSON"]
    }
    
    violations = []
    for feature, patterns in forbidden_check.items():
        for pattern in patterns:
            if pattern.lower() in template_content.lower():
                violations.append(f"{feature} ({pattern})")
    
    if violations:
        print(f"❌ Found forbidden features: {violations}")
        return False
    
    print(f"✅ Requirements compliance: {compliance_score}/{len(requirements_check)}")
    return compliance_score == len(requirements_check)


def main():
    """Run all validation checks."""
    print("🎯 Triage Agent V2 - Shubham's Requirements Validation")
    print("=" * 60)
    
    validations = [
        ("Template Content", validate_template_content),
        ("Configuration Support", validate_configuration_support),
        ("Shubham's Requirements Compliance", validate_shubham_requirements_compliance)
    ]
    
    passed = 0
    total = len(validations)
    
    for name, validation_func in validations:
        try:
            if validation_func():
                passed += 1
                print(f"\n✅ {name} - PASSED")
            else:
                print(f"\n❌ {name} - FAILED")
        except Exception as e:
            print(f"\n❌ {name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 VALIDATION RESULTS: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 SUCCESS: Triage Agent V2 fully complies with Shubham's requirements!")
        print("\n📋 Ready for:")
        print("  • Talk-back conversation capability")
        print("  • Clarification for ambiguous requests")
        print("  • Unknown-info handling with graceful fallbacks")
        print("  • Mid-conversation transfer support")
        print("  • Simple conversational responses (no complex JSON)")
        return True
    else:
        print("⚠️ ISSUES FOUND: Please address the failed validations.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
