EVALUATION TEST SET FOR SMART ROUTING TRIAGE AGENT
=====================================================

CATEGORY 1: TALK-BACK CONVERSATION CAPABILITY
==============================================

TEST_001
User Input: "Hi there, I need some help"
Expected Triage Activity: Provide conversational response before routing, ask clarifying question about specific help needed

TEST_002
User Input: "Hello, can you assist me?"
Expected Triage Activity: Respond conversationally with helpful tone, request more details about assistance type

TEST_003
User Input: "Good morning, I have a question"
Expected Triage Activity: Acknowledge greeting warmly, ask what specific question they have

TEST_004
User Input: "Can someone help me please?"
Expected Triage Activity: Respond reassuringly, ask for details about what help is needed

TEST_005
User Input: "I'm looking for support"
Expected Triage Activity: Provide welcoming response, clarify what type of support is needed

CATEGORY 2: CLARIFICATION FOR AMBIGUOUS REQUESTS
===============================================

TEST_006
User Input: "I have a problem"
Expected Triage Activity: Ask clarifying question about what specific problem they're experiencing

TEST_007
User Input: "Something is wrong"
Expected Triage Activity: Request more details about what specifically is wrong

TEST_008
User Input: "I need help with something"
Expected Triage Activity: Ask what specific thing they need help with

TEST_009
User Input: "There's an issue"
Expected Triage Activity: Clarify what type of issue and provide examples if needed

TEST_010
User Input: "Can you fix this?"
Expected Triage Activity: Ask what specifically needs to be fixed

CATEGORY 3: UNKNOWN-INFO GRACEFUL FALLBACKS
==========================================

TEST_011
User Input: "What's the weather like in Paris?"
Expected Triage Activity: Graceful fallback explaining limitation, offer airline-related assistance instead

TEST_012
User Input: "Can you book me a hotel?"
Expected Triage Activity: Politely explain out-of-scope, suggest flight booking or airline services

TEST_013
User Input: "I need car rental information"
Expected Triage Activity: Acknowledge request, explain limitation, offer relevant airline services

TEST_014
User Input: "Help with my travel insurance"
Expected Triage Activity: Graceful fallback, suggest contacting insurance provider, offer flight-related help

TEST_015
User Input: "Restaurant recommendations in Tokyo"
Expected Triage Activity: Polite limitation explanation, redirect to airline services available

CATEGORY 4: MID-CONVERSATION TRANSFER SUPPORT
============================================

TEST_016 - Booking to Status Switch
Initial Context: User in booking flow
User Input: "Actually, what's my flight status for AA123?"
Expected Triage Activity: Recognize context switch, route to status agent, preserve conversation context

TEST_017 - Status to Booking Switch  
Initial Context: User checking flight status
User Input: "Never mind that, I want to book a new flight instead"
Expected Triage Activity: Detect flow change, route to booking agent, maintain context

TEST_018 - Booking to Support Switch
Initial Context: User booking a flight
User Input: "Wait, I need to change my existing reservation first"
Expected Triage Activity: Identify switch to support need, route to customer support, preserve context

TEST_019 - Support to Status Switch
Initial Context: User getting support help
User Input: "Actually, can you just tell me if my flight is on time?"
Expected Triage Activity: Recognize switch to status inquiry, route to status agent

TEST_020 - Status to Support Switch
Initial Context: User checking flight status  
User Input: "My flight is delayed, what can you do about it?"
Expected Triage Activity: Detect escalation to support need, route to customer support agent

CATEGORY 5: THREE-PATH DECISION MODEL
====================================

TEST_021 - Route Path
User Input: "I want to book a flight to New York"
Expected Triage Activity: Identify clear booking intent, route directly to booking agent

TEST_022 - Route Path
User Input: "What's the status of flight UA456?"
Expected Triage Activity: Recognize status request, route directly to status agent

TEST_023 - Clarify Path
User Input: "I have a question about my trip"
Expected Triage Activity: Request clarification about what specific aspect of trip

TEST_024 - Clarify Path
User Input: "I need assistance"
Expected Triage Activity: Ask for more specific details about type of assistance needed

TEST_025 - Out-of-Scope Path
User Input: "What's the best restaurant near the airport?"
Expected Triage Activity: Recognize out-of-scope, provide graceful fallback, offer relevant services

ADDITIONAL UNKNOWN-INFO TEST CASES
=================================

TEST_026 - Weather Request
User Input: "What's the weather forecast for my destination?"
Expected Triage Activity: Acknowledge request, explain limitation, offer flight-related services instead

TEST_027 - Hotel Booking Request
User Input: "Can you help me book a hotel room?"
Expected Triage Activity: Politely explain out-of-scope, suggest airline services, maintain helpful tone

TEST_028 - Car Rental Request
User Input: "I need to rent a car at my destination"
Expected Triage Activity: Graceful fallback, explain domain limits, offer relevant airline services

TEST_029 - Travel Insurance Query
User Input: "What travel insurance options do you recommend?"
Expected Triage Activity: Acknowledge limitation, suggest contacting insurance providers, offer flight services

TEST_030 - Currency Exchange
User Input: "What's the current exchange rate for euros?"
Expected Triage Activity: Explain out-of-domain, suggest appropriate resources, offer airline assistance

TEST_031 - Local Transportation
User Input: "How do I get from the airport to downtown?"
Expected Triage Activity: Acknowledge request, explain limitation, suggest airline ground transportation if available

TEST_032 - Visa Requirements
User Input: "Do I need a visa for my destination country?"
Expected Triage Activity: Graceful fallback, suggest official government resources, offer flight-related help

TEST_033 - Medical/Health Query
User Input: "What vaccinations do I need for travel?"
Expected Triage Activity: Explain limitation, suggest medical professionals, offer airline health policies if relevant

TEST_034 - Restaurant Recommendations
User Input: "Can you recommend good restaurants in Paris?"
Expected Triage Activity: Polite limitation explanation, suggest travel guides, offer airline meal services

TEST_035 - Shopping/Tourism
User Input: "What are the best shopping areas in my destination?"
Expected Triage Activity: Acknowledge request, explain domain limits, offer airline services available

CONVERSATION SEQUENCE SCENARIOS
==============================

SCENARIO_001: Booking to Status Transition
User_1: "I want to book a flight"
User_2: "To Los Angeles"  
User_3: "Actually, before I book, what's the status of my existing flight AA789?"
Expected Triage Activity: Detect mid-conversation switch from booking to status, route to status agent

SCENARIO_002: Status to Booking Transition
User_1: "Check my flight status"
User_2: "Flight number is DL123"
User_3: "It's delayed? I need to book an alternative flight then"
Expected Triage Activity: Recognize switch from status to booking need, route to booking agent

SCENARIO_003: Booking to Support Transition
User_1: "Book me a flight to Miami"
User_2: "Departing tomorrow"
User_3: "Wait, I need to cancel my current booking first"
Expected Triage Activity: Identify switch to support/cancellation need, route to customer support

SCENARIO_004: Support to Status Transition
User_1: "I need help with my reservation"
User_2: "I want to change my seat"
User_3: "Actually, just tell me if the flight is on time first"
Expected Triage Activity: Detect change to status inquiry, route to status agent

SCENARIO_005: Multiple Context Switches
User_1: "Book a flight"
User_2: "Cancel that"
User_3: "Check flight status instead"
User_4: "Never mind, book the original flight"
Expected Triage Activity: Handle rapid context switches, route to final intent (booking agent)

COMPLEX SCENARIOS
================

COMPLEX_001: Ambiguous with Context Switch
User_1: "I have a problem"
User_2: "With my booking"
User_3: "Actually, it's about my flight status"
Expected Triage Activity: Start with clarification, then route to status agent when clarified

COMPLEX_002: Out-of-scope to Valid Request
User_1: "What's the weather in Chicago?"
User_2: "I'm asking because I have a flight there"
User_3: "Can you check if it's delayed?"
Expected Triage Activity: Initial graceful fallback, then route to status agent for valid request

COMPLEX_003: Escalating Complexity
User_1: "Help"
User_2: "With my flight"
User_3: "It's cancelled and I need a new one"
User_4: "And compensation for the inconvenience"
Expected Triage Activity: Progressive clarification, final route to customer support for complex issue

COMPLEX_004: Mixed Intent Resolution
User_1: "I need to book a flight and check another flight's status"
Expected Triage Activity: Identify multiple intents, prioritize or ask which to handle first

COMPLEX_005: Unclear to Clear Intent
User_1: "Something's wrong"
User_2: "With the airline"
User_3: "My flight AA456 is showing wrong information"
Expected Triage Activity: Clarification sequence leading to status agent routing

UNKNOWN-INFO CONVERSATION SEQUENCES
==================================

UNKNOWN_001: Out-of-Domain to In-Domain Transition
User_1: "What's the weather like in London?"
User_2: "I'm asking because I have a flight there"
User_3: "Can you check if my flight is on time?"
Expected Triage Activity: Initial graceful fallback, then route to status agent for valid request

UNKNOWN_002: Multiple Out-of-Domain Requests
User_1: "Can you book me a hotel?"
User_2: "Also need car rental information"
User_3: "And restaurant recommendations"
Expected Triage Activity: Consistent graceful fallbacks, offer airline services for each request

UNKNOWN_003: Mixed Domain Requests
User_1: "I need help with my flight and hotel booking"
Expected Triage Activity: Handle flight part, graceful fallback for hotel, offer airline services

UNKNOWN_004: Persistent Out-of-Domain User
User_1: "What's the weather forecast?"
User_2: "But you must have weather information for flights"
User_3: "All airlines check weather"
Expected Triage Activity: Patient explanation of limitations, consistent graceful handling

UNKNOWN_005: Out-of-Domain with Urgency
User_1: "URGENT: I need medical advice for travel"
Expected Triage Activity: Acknowledge urgency, graceful fallback, suggest appropriate medical resources

AGENT TRANSFER TO TRIAGE FOR UNKNOWN-INFO
=========================================

TRANSFER_001: Booking Agent Encounters Unknown Request
Initial Context: User talking to booking agent
User Input: "Can you help me with travel insurance instead?"
Expected Triage Activity: Receive transfer, provide graceful fallback for insurance, offer booking services

TRANSFER_002: Status Agent Gets Out-of-Domain Request
Initial Context: User checking flight status
User Input: "Actually, what's the weather like at my destination?"
Expected Triage Activity: Handle transfer, explain weather limitation, offer flight-related services

TRANSFER_003: Knowledge Base Agent Routes Unknown Query
Initial Context: User asking knowledge base questions
User Input: "Do you know good hotels near the airport?"
Expected Triage Activity: Receive transfer, graceful hotel booking fallback, suggest airline services

TRANSFER_004: Multiple Agent Transfers for Same Unknown Request
Initial Context: User bounced between agents
User Input: "I keep asking about car rentals but no one can help"
Expected Triage Activity: Acknowledge frustration, explain limitation clearly, offer comprehensive airline alternatives

TRANSFER_005: Agent Transfer with Context Preservation
Initial Context: User was booking flight, now asks about visa
User Input: "Before I complete this booking, do I need a visa?"
Expected Triage Activity: Acknowledge booking context, graceful visa fallback, suggest completing flight booking first

EDGE CASE SCENARIOS
===================

EDGE_001: Rapid Fire Requests
User_1: "Book flight"
User_2: "Cancel"
User_3: "Status check"
User_4: "Book again"
Expected Triage Activity: Handle rapid context switches without confusion, route to final clear intent

EDGE_002: Contradictory Requests
User_1: "I want to book a flight"
User_2: "Actually I don't want to travel"
User_3: "But check my existing flight status"
Expected Triage Activity: Navigate contradictions, focus on final actionable request (status check)

EDGE_003: Emotional/Frustrated User
User_1: "This is ridiculous!"
User_2: "My flight is always delayed"
User_3: "What's the status of UA789?"
Expected Triage Activity: Acknowledge frustration with empathy, route to status agent

EDGE_004: Technical Jargon
User_1: "I need to modify my PNR"
User_2: "Change the routing"
User_3: "Update passenger manifest"
Expected Triage Activity: Understand airline terminology, route to appropriate support agent

EDGE_005: Multiple Passengers
User_1: "I need to book flights for my family"
User_2: "Also check status of my business trip"
User_3: "And cancel my wife's flight"
Expected Triage Activity: Identify multiple requests, ask which to prioritize or handle sequentially

STRESS TEST SCENARIOS
=====================

STRESS_001: Very Long Request
User Input: "Hi there, I hope you're having a great day, I'm calling because I have this really complicated situation where I booked a flight last week but then my plans changed and now I need to check if I can modify it or maybe cancel it and book a new one, but first I should probably check the status of my current flight to see if it's even on time because if it's delayed I might not need to change anything after all, so can you help me with checking flight status for AA123?"
Expected Triage Activity: Parse long request, identify primary intent (status check), route to status agent

STRESS_002: Minimal Input
User Input: "Help"
Expected Triage Activity: Request clarification about what specific help is needed

STRESS_003: Mixed Languages/Typos
User Input: "I ned halp with mi flght bokking"
Expected Triage Activity: Understand despite typos, ask for clarification about booking help needed

STRESS_004: All Caps/Urgent
User Input: "URGENT! MY FLIGHT IS MISSING FROM THE SYSTEM!"
Expected Triage Activity: Acknowledge urgency, route to customer support for system issues

STRESS_005: Incomplete Information
User Input: "My flight..."
Expected Triage Activity: Ask user to complete their request

REALISTIC USER BEHAVIOR SCENARIOS
=================================

REALISTIC_001: Hesitant User
User_1: "Um, hi"
User_2: "I think I need help"
User_3: "Maybe with booking?"
User_4: "Actually checking status first"
Expected Triage Activity: Be patient with hesitation, guide to final clear intent (status check)

REALISTIC_002: Impatient User
User_1: "I need help NOW"
User_2: "Flight status immediately"
User_3: "Flight AA456"
Expected Triage Activity: Acknowledge urgency, quickly route to status agent

REALISTIC_003: Confused User
User_1: "I don't know what I need"
User_2: "Something about my flight"
User_3: "It's tomorrow and I'm worried"
Expected Triage Activity: Guide with gentle questions to identify specific concern (likely status check)

REALISTIC_004: Tech-Savvy User
User_1: "Route me to flight operations"
User_2: "I need real-time status data"
User_3: "For flight UA789"
Expected Triage Activity: Understand technical request, route to status agent

REALISTIC_005: Elderly/Less Tech-Savvy User
User_1: "Hello dear, I'm not good with computers"
User_2: "I just want to know about my airplane"
User_3: "It's supposed to leave tomorrow"
Expected Triage Activity: Use patient, simple language, guide to status check

BUSINESS LOGIC TEST SCENARIOS
=============================

BUSINESS_001: Peak Hours Simulation
User Input: "I need immediate help with my flight"
Expected Triage Activity: Acknowledge request, efficiently route based on specific need

BUSINESS_002: Off-Hours Request
User Input: "Is anyone available to help with booking?"
Expected Triage Activity: Confirm availability, route to booking agent

BUSINESS_003: Premium Customer
User Input: "This is a first-class passenger, I need priority assistance"
Expected Triage Activity: Acknowledge status, route appropriately while maintaining service level

BUSINESS_004: Group Booking
User Input: "I'm booking for a corporate group of 50 people"
Expected Triage Activity: Recognize complex booking need, route to specialized booking support

BUSINESS_005: International Flight Issues
User Input: "My international flight has visa documentation problems"
Expected Triage Activity: Identify complex international issue, route to specialized customer support

INTEGRATION TEST SCENARIOS
==========================

INTEGRATION_001: System Handoff
User Input: "Transfer me to a human agent"
Expected Triage Activity: Acknowledge request, explain routing to appropriate specialist

INTEGRATION_002: Callback Request
User Input: "Can someone call me back about my flight issue?"
Expected Triage Activity: Understand callback preference, route to appropriate agent with callback capability

INTEGRATION_003: Multi-Channel Continuation
User Input: "I started this conversation on your website"
Expected Triage Activity: Acknowledge channel switch, maintain context continuity

INTEGRATION_004: Escalation Path
User Input: "Your system isn't working, I need to speak to a manager"
Expected Triage Activity: Recognize escalation need, route to senior customer support

INTEGRATION_005: Follow-up Conversation
User Input: "I called earlier about flight AA123, any updates?"
Expected Triage Activity: Acknowledge follow-up, route to status agent for updates

UNKNOWN-INFO STRESS TESTS
========================

STRESS_UNKNOWN_001: Rapid Unknown Requests
User_1: "Weather forecast?"
User_2: "Hotel booking?"
User_3: "Car rental?"
User_4: "Restaurant recommendations?"
Expected Triage Activity: Handle rapid unknown requests gracefully, maintain consistent fallback responses

STRESS_UNKNOWN_002: Complex Out-of-Domain Request
User Input: "I need comprehensive travel planning including flights, hotels, car rentals, restaurant reservations, tourist attractions, local transportation, currency exchange, travel insurance, visa assistance, and medical advice for my 3-week European vacation with my family of 5 including elderly parents and young children with special dietary requirements"
Expected Triage Activity: Parse complex request, identify flight component, graceful fallback for other services

STRESS_UNKNOWN_003: Emotional Unknown Request
User Input: "I'm so stressed! I need help with everything - hotels, cars, restaurants, weather, everything except flights!"
Expected Triage Activity: Acknowledge stress empathetically, graceful fallback for non-flight services, offer flight assistance

STRESS_UNKNOWN_004: Technical Unknown Request
User Input: "Can you integrate with Booking.com API to help me find hotels?"
Expected Triage Activity: Understand technical request, explain limitation professionally, offer airline services

STRESS_UNKNOWN_005: Repeated Unknown After Explanation
User Input: "I know you said you can't help with hotels, but surely you have some hotel information?"
Expected Triage Activity: Patient re-explanation, maintain graceful tone, reinforce airline service offerings

REALISTIC UNKNOWN-INFO SCENARIOS
===============================

REALISTIC_UNKNOWN_001: Confused About Scope
User_1: "I thought airlines help with everything travel-related"
User_2: "Can't you book hotels too?"
Expected Triage Activity: Educate about scope politely, explain airline vs travel agency services

REALISTIC_UNKNOWN_002: Comparison Shopping
User Input: "Can you compare hotel prices like you do for flights?"
Expected Triage Activity: Explain service differences, graceful fallback, offer flight comparison services

REALISTIC_UNKNOWN_003: Assumption-Based Request
User Input: "Since you handle flights, you must handle ground transportation too"
Expected Triage Activity: Politely correct assumption, explain scope, offer relevant airline ground services if available

REALISTIC_UNKNOWN_004: Bundled Service Expectation
User Input: "I want to book a complete travel package - flights, hotels, and activities"
Expected Triage Activity: Handle flight component, graceful fallback for other services, suggest travel package providers

REALISTIC_UNKNOWN_005: Frustrated Repeat Customer
User Input: "Last time I called, someone helped me with hotel recommendations"
Expected Triage Activity: Acknowledge past experience, explain current scope, maintain helpful tone, offer flight services
