import requests
import json
import time
from uuid import uuid4

# Configuration Constants
# BASIC_URL = "https://api.dev.multi-agent.rozie.ai"
BASIC_URL = "http://0.0.0.0"
PATH_INITIATE = "conversation/initiate_chat"
PATH_SEND = "conversation/send_message"
PATH_RECEIVE = "conversation/get_message"
DISCONNECT_PATH = "conversation/end_chat"
CHAT_ID = f"TEST_LOCAL_{uuid4()}"
HEADERS = {
    "Content-Type": "application/json",
    "access_token": "Test@123",
    "application-id": "application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3",
    "rozie-correlation-id": CHAT_ID,
    "channel": "voice",
    "language": "en-US",
}

# Event Information
CHANNEL_INFO = {"channel_id": "voice", "channel_name": "voice"}
USER_INFO = {
    "UserName": "RozieAI",
}
USER_CONTEXT = {
    "UserName": "RozieAI",
    "PhoneNumber": "9422139024",
    "WelcomeMessageFlag": "default",
    "Location": "Bay Ridge",
    "BusinessId": "Wj6YQfGEsvSqkLskUdhu3g",
}
LLM_CONTEXT = {
    "Customer's Dialed Location": "Bay Ridge",
    "Customer's Dialed Number": "9422139024",
    "Customer's Name": "RozieAI",
}
ROSTER_ID = "salon718-Dev"

# For Dev
PRINT_COMPLETE_EVENT = True


def get_base_event():
    """Builds the base event payload."""
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": CHAT_ID, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": {**USER_INFO},
        },
        "channel": {**CHANNEL_INFO, "ui_info": {"should_consolidate_buttons": False}},
        "incoming_events": [
            {
                "event_id": "initiate_event",
                "event_user": {
                    "user_id": {
                        "id": CHAT_ID,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {**USER_INFO},
                },
                "event_template": {},
            }
        ],
    }


def get_initiate_event():
    """Builds the initiate chat event payload."""
    initiate_event = get_base_event()
    initiate_event["incoming_events"][0]["event_template"] = {
        "event_type": "initiate",
        "rosters_id": ROSTER_ID,
        "llm_context": LLM_CONTEXT,
        "user_context": USER_CONTEXT,
    }
    return initiate_event


def get_send_message_event(user_input):
    """Builds the send message event payload."""
    send_message_event = get_base_event()
    send_message_event["incoming_events"][0]["event_template"] = {
        "event_type": "text",
        "text": user_input,
    }
    return send_message_event


def get_receive_message_event():
    """Builds the receive message event payload."""
    receive_message_event = get_base_event()
    receive_message_event["incoming_events"][0]["event_template"] = {
        "event_type": "listen"
    }
    return receive_message_event


def get_disconnect_event():
    """Builds the disconnect event payload."""
    disconnect_event = get_base_event()
    disconnect_event["incoming_events"][0]["event_template"] = {
        "event_type": "disconnect"
    }
    return disconnect_event


def initiate_chat():
    """Sends the initiate chat event to the server."""
    url = f"{BASIC_URL}/{PATH_INITIATE}"
    response = requests.post(
        url, headers=HEADERS, json=get_initiate_event(), timeout=10
    )
    return response.json()


def send_message(user_input):
    """Sends a message event to the server."""
    url = f"{BASIC_URL}/{PATH_SEND}"
    response = requests.post(
        url, headers=HEADERS, json=get_send_message_event(user_input), timeout=10
    )
    return response.json()


def receive_message():
    """Fetches the latest agent message."""
    url = f"{BASIC_URL}/{PATH_RECEIVE}"
    response = requests.post(
        url, headers=HEADERS, json=get_receive_message_event(), timeout=10
    )
    return response.json()


def disconnect_chat():
    """Disconnects the chat."""
    url = f"{BASIC_URL}/{DISCONNECT_PATH}"
    response = requests.post(
        url, headers=HEADERS, json=get_disconnect_event(), timeout=10
    )
    return response.json()


def run_chat():
    """Main chat loop."""
    print("Starting chat session...")
    response = initiate_chat()
    last_action_time = time.time()
    user_message_count = 0
    while True:
        agent_response = receive_message()
        time_taken = round(time.time() - last_action_time, 2)
        # print(f"Time taken: {time_taken}s")
        next_action = agent_response.get("next_action")
        agent_message = json.dumps(agent_response, indent=4)

        if next_action == "wait":
            time.sleep(0.2)
            continue
        elif next_action in {"say", "gather"}:
            # Print Agent Message
            if PRINT_COMPLETE_EVENT:
                print(agent_message)
            else:
                responses = (
                    agent_response.get("response_map", {})
                    .get("responses", {})
                    .get("default", [])
                )
                for response in responses:
                    text = response.get("response_template", {}).get("text")
                    if text:
                        print(f"Agent: {text}")
            # Gather User Input
            if next_action == "gather":
                user_input = input("Customer: ")
                if user_input.lower() == "end":
                    break
                user_message_count += 1
                response = send_message(user_input)
            last_action_time = time.time()
        elif next_action == "disconnect":
            print("Disconnecting chat...")
            disconnect_chat()
            break
        time.sleep(0.2)
    print("Chat session ended.")


if __name__ == "__main__":
    run_chat()
