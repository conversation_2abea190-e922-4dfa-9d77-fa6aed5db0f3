[tool.poetry]
authors = []
description = "Flask API service for autogen navigator."
name = "scc-navigator-autogen"
readme = "README.md"
version = "0.1.0"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
fastapi = "0.115.3"
uvicorn = {extras = ["standard"], version = "^0.27.0.post1"}
boto3 = "^1.34.49"
autogen-core = "0.5.1"
autogen-ext = {version = "0.5.1", extras = ["openai", "anthropic"]}
requests= "^2.31.0"
RestrictedPython= "^8.0"
langfuse="2.54.1"
beautifulsoup4="4.12.2"
psutil="7.0.0"
loguru = "^0.7.2"
cachetools = "^5.5.2"
jinja2 = "^3.1.2"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"

[tool.poetry.group.codespell]
optional = true

[tool.poetry.group.codespell.dependencies]
codespell = "^2.2.6"

[tool.poetry.group.lint]
optional = true

[tool.poetry.group.lint.dependencies]
ruff = "^0.1.15"
pre-commit = "^3.6.0"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^7.3.0"
pytest-cov = "^4.0.0"

[tool.poetry.group.typing]
optional = true

[tool.poetry.group.typing.dependencies]
mypy = "^1.8.0"

[tool.codespell]
check-filenames = true
check-hidden = true
ignore-words-list = "astroid,gallary,momento,narl,ot,rouge"
# Feel free to un-skip examples, and experimental, you will just need to
# work through many typos (--write-changes and --interactive will help)
skip = "*.csv,*.html,*.json,*.jsonl,*.pdf,*.txt,*.ipynb"

[tool.coverage.run]
omit = [
  "tests/*"
]

[tool.mypy]
disallow_untyped_defs = true
# Remove venv skip when integrated with pre-commit
exclude = [".venv", "examples"]
follow_imports = "skip"
ignore_missing_imports = true
python_version = "3.11"
strict_optional = false

[tool.ruff]
target-version = "py311"

[tool.ruff.flake8-annotations]
mypy-init-return = true

[tool.ruff.pydocstyle]
convention = "google"

[build-system]
build-backend = "poetry.core.masonry.api"
requires = ["poetry-core"]