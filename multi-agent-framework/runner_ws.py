import json
import threading
import time
import websocket
from uuid import uuid4

# ------------------ Configuration ------------------

BASIC_URL = "ws://0.0.0.0"
PATH = "conversation/ws"
CHAT_ID = f"TEST_LOCAL_{uuid4()}"
HEADERS = {
    "Content-Type": "application/json",
    "access_token": "Test@123",
    "application-id": "application_6f56a813-bfcb-43b5-aa5d-81df8f24c2f3",
    "rozie-correlation-id": CHAT_ID,
    "channel": "voice",
    "language": "en-US",
}
CHANNEL_INFO = {"channel_id": "voice", "channel_name": "voice"}
USER_INFO = {"UserName": "RozieAI"}
USER_CONTEXT = {"WelcomeMessageFlag": "default"}
LLM_CONTEXT = {}
ROSTER_ID = "RozieAir_V2"

# ------------------ Event Builders ------------------


def get_base_event():
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": CHAT_ID, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": USER_INFO,
        },
        "channel": {**CHANNEL_INFO, "ui_info": {"should_consolidate_buttons": False}},
        "incoming_events": [
            {
                "event_id": "initiate_event",
                "event_user": {
                    "user_id": {
                        "id": CHAT_ID,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": USER_INFO,
                },
                "event_template": {},
            }
        ],
    }


def get_initiate_event():
    event = get_base_event()
    event["incoming_events"][0]["event_template"] = {
        "event_type": "initiate",
        "rosters_id": ROSTER_ID,
        "llm_context": LLM_CONTEXT,
        "user_context": USER_CONTEXT,
    }
    return event


def get_send_message_event(user_input):
    event = get_base_event()
    event["incoming_events"][0]["event_template"] = {
        "event_type": "text",
        "text": user_input,
    }
    return event


def get_disconnect_event():
    event = get_base_event()
    event["incoming_events"][0]["event_template"] = {"event_type": "disconnect"}
    return event


# ------------------ WebSocket Callbacks ------------------


def process_message(ws, message):
    try:
        data = json.loads(message)
        print("Agent:", json.dumps(data, indent=4))

        if data.get("next_action") == "gather":
            print("Waiting for customer input... (type 'end' to quit)")

        if data.get("should_end_interaction"):
            print("Ending interaction...")
            send_websocket_message(ws, get_disconnect_event())
            close_websocket_connection(ws)

    except Exception as e:
        print(f"Failed to process message: {e}")


def on_message(ws, message):
    if message.startswith("__ping__"):
        print("Received ping")
    else:
        process_message(ws, message)


def on_error(ws, error):
    print(f"WebSocket error: {error}")


def on_close(ws, code, reason):
    print(f"WebSocket closed (code={code}, reason={reason})")


def on_open(ws):
    print("WebSocket connection opened.")
    send_websocket_message(ws, get_initiate_event())


# ------------------ WebSocket Helpers ------------------


def send_websocket_message(ws, message):
    try:
        ws.send(json.dumps(message))
    except Exception as e:
        print(f"Error sending message: {e}")


def close_websocket_connection(ws):
    try:
        ws.close()
    except Exception as e:
        print(f"Error closing connection: {e}")


def on_ping(ws, message):
    print("Got protocol-level PING")


def create_websocket_connection(url):
    return websocket.WebSocketApp(
        url,
        header=HEADERS,
        on_open=on_open,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close,
        on_ping=on_ping,
    )


# ------------------ Main Runner ------------------


def run_chat():
    ws_url = f"{BASIC_URL}/{PATH}/{CHAT_ID}"
    print(f"Connecting to: {ws_url}")
    ws = create_websocket_connection(ws_url)

    def user_input_thread():
        while True:
            user_input = input("Customer: ").strip()
            if user_input.lower() == "end":
                send_websocket_message(ws, get_disconnect_event())
                close_websocket_connection(ws)
                break
            event = get_send_message_event(user_input)
            send_websocket_message(ws, event)

    threading.Thread(target=user_input_thread, daemon=True).start()

    ws.run_forever(ping_interval=30, ping_timeout=10)


# ------------------ Entry ------------------

if __name__ == "__main__":
    run_chat()
