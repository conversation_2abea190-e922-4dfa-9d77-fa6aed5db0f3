#!/usr/bin/env python3
"""
Quick Integration Test for Triage Agent V2
==========================================

This script tests the basic integration of the enhanced triage agent
to ensure it loads properly and can generate responses.
"""

import sys
import os
import json

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import (
    _get_triage_agent_v2_system_prompt
)


def test_prompt_generation():
    """Test that the V2 prompt generation works correctly."""
    print("🧪 Testing Triage Agent V2 Prompt Generation")
    print("=" * 50)
    
    # Load test configuration
    config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    try:
        # Generate the system prompt
        system_prompt = _get_triage_agent_v2_system_prompt(config)
        
        print("✅ System prompt generated successfully!")
        print(f"📏 Prompt length: {len(system_prompt)} characters")
        
        # Check for key components
        required_components = [
            "Nina",  # avatar_name
            "Rozie Airline",  # company_name
            "Airline",  # use_case_domain
            "STEP 1: CONVERSATIONAL RESPONSE",
            "STEP 2: DECISION TREE",
            "AVAILABLE WORKFLOWS"
        ]
        
        missing_components = []
        for component in required_components:
            if component not in system_prompt:
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ Missing components: {missing_components}")
            return False
        
        print("✅ All required components found in prompt!")
        
        # Show a preview of the prompt
        print("\n📋 Prompt Preview (first 500 characters):")
        print("-" * 50)
        print(system_prompt[:500] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating prompt: {str(e)}")
        return False


def test_workflow_formatting():
    """Test that workflows are properly formatted."""
    print("\n🔧 Testing Workflow Formatting")
    print("=" * 50)
    
    config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    workflows = config.get("workflows", {})
    
    print(f"📊 Found {len(workflows)} workflows:")
    
    for workflow_id, workflow_data in workflows.items():
        if isinstance(workflow_data, dict):
            name = workflow_data.get("name", "Unknown")
            description = workflow_data.get("description", "No description")
            print(f"  ✅ {workflow_id}: {name}")
            print(f"     📝 {description[:80]}...")
        else:
            print(f"  ⚠️  {workflow_id}: Legacy format (list)")
    
    return True


def test_configuration_validation():
    """Test that the V2 configuration has all required fields."""
    print("\n⚙️  Testing Configuration Validation")
    print("=" * 50)
    
    config_path = "llm_app/config/examples/rosters/RozieAir_V2.json"
    
    with open(config_path, 'r') as f:
        config = json.load(f)
    
    required_fields = [
        "avatar_name",
        "company_name", 
        "use_case_domain",
        "time_zone",
        "channel_guidelines",
        "use_triage_agent_v2",
        "workflows"
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in config:
            missing_fields.append(field)
        else:
            print(f"  ✅ {field}: {config[field]}")
    
    if missing_fields:
        print(f"❌ Missing required fields: {missing_fields}")
        return False
    
    # Check that V2 is enabled
    if not config.get("use_triage_agent_v2", False):
        print("❌ Triage Agent V2 is not enabled in configuration")
        return False
    
    print("✅ All required configuration fields present!")
    return True


def main():
    """Run all integration tests."""
    print("🚀 Triage Agent V2 Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Configuration Validation", test_configuration_validation),
        ("Workflow Formatting", test_workflow_formatting),
        ("Prompt Generation", test_prompt_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Triage Agent V2 integration looks good.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
