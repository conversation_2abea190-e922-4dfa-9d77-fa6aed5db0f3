# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: Build
    displayName: Build and push stage
    jobs:
      - job: Build
        displayName: Build image
        steps:
          - task: Docker@2
            displayName: Build and push image to container registry
            inputs:
              command: buildAndPush
              repository: $(agn_imageRepository)
              dockerfile: $(agn_dockerfilePath)
              containerRegistry: $(agn_dockerRegistryServiceConnection)
              tags: |
                $(Build.BuildId)
                $(agn_tag1)