from fastapi import APIRouter, HTTPException, Depends
from llm_app.adapters.auth_adapter import get_api_key
import psutil

status = APIRouter(prefix="/status")

@status.get("", dependencies=[Depends(get_api_key)])
def get_system_status():
    """  System Status """
    cpu_usage = psutil.cpu_percent(interval=1)
    memory_info = psutil.virtual_memory()
    
    sys_stat = {
        "cpu_usage": f"{cpu_usage}%",
        "memory_total": f"{memory_info.total / (1024 ** 3):.2f} GB",
        "memory_used": f"{memory_info.used / (1024 ** 3):.2f} GB",
        "memory_available": f"{memory_info.available / (1024 ** 3):.2f} GB",
        "memory_usage": f"{memory_info.percent}%"
    }

    return {"statusCode": 200, "status": sys_stat}
