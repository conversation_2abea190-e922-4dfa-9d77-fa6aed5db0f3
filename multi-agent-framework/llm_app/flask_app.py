import os
from contextlib import asynccontextmanager
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from loguru import logger

# --- Import Loguru Setup Function and Middleware ---
from llm_app.logging.loguru_config import setup_loguru_logging
from llm_app.logging.middleware import LoggingContextMiddleware


# --- Import Routers and Constants ---
from llm_app.routes import health_router, sys_status_router, conversation_router
from llm_app.common.attributes import CONSTANTS


# --- Lifespan Context Manager ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("--- [Lifespan] Initializing application startup ---")
    logger.info("--- [Lifespan] Configuring Loguru logging ---")

    # --- Setup Loguru Logging ---
    setup_loguru_logging()
    # Loguru setup function already logs confirmation

    logger.info("--- [Lifespan] Application startup complete ---")
    yield  # The application runs
    logger.info("--- [Lifespan] Application shutdown sequence started ---")
    logger.info("--- [Lifespan] Application shutdown complete ---")


# --- FastAPI App Initialization ---
app = FastAPI(
    title="Multi Agent Application",
    description="API for LLM interactions, health checks, and system status.",
    version=CONSTANTS.get("APP_VERSION", "0.1.0"),
    lifespan=lifespan,
)


# --- Middleware ---
logger.info("--- [Setup] Configuring CORS middleware ---")
allowed_origins = []
allowed_origins_str = CONSTANTS.get("ALLOWED_ORIGINS", "")  # Use CONSTANTS or os.getenv directly
if not allowed_origins_str:
    logger.warning(
        "ALLOWED_ORIGINS environment variable not set or empty. Defaulting to no origins. CORS may block requests."
    )
    # Decide on default behavior: empty list, ["*"], or error
    allowed_origins = []  # Example: restrict by default if not specified
else:
    allowed_origins = [origin.strip() for origin in allowed_origins_str.split(",")]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info(f"--- [Setup] CORS middleware configured for origins: {allowed_origins} ---")

# --- Add Logging Context Middleware ---
# Add this *after* CORS but *before* routes process requests
logger.info("--- [Setup] Adding Logging Context middleware ---")
app.add_middleware(LoggingContextMiddleware)
logger.info("--- [Setup] Logging Context middleware added ---")


# --- Include Routers ---
logger.info("--- [Setup] Including application routers ---")
app.include_router(health_router.health_router)
app.include_router(sys_status_router.status)
app.include_router(conversation_router.router)
logger.info("--- [Setup] Routers included successfully ---")


# --- Main Execution Block ---
if __name__ == "__main__":
    import uvicorn

    host = os.environ.get("HOST", "0.0.0.0")
    port = int(os.environ.get("PORT", 8000))
    log_level_uvicorn = os.environ.get("UVICORN_LOG_LEVEL", "info").lower()

    app_log_level = os.environ.get("LOG_LEVEL", "INFO").upper()
    # --------------------------------------------------------------------
    logger.info("--- Starting application via Uvicorn ---")
    logger.info(f"--- Uvicorn Config: host={host}, port={port}, uvicorn_log_level={log_level_uvicorn} ---")
    # --- Use the variable read above ---
    logger.info(f"--- Application Log Level (Loguru set via ENV): {app_log_level} ---")
    # -----------------------------------

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        log_level=log_level_uvicorn,
    )
