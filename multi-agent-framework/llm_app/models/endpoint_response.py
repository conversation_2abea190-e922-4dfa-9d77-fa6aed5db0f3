from pydantic import BaseModel, Field, TypeAdapter
from typing import Literal, List, Union, Dict, Any, Optional


class InitiateEventTemplate(BaseModel):
    event_type: Literal["initiate"]
    rosters_id: str = Field(..., description="Unique identifier for the agent fleet roster")
    llm_context: Dict = Field(..., description="Dictionary containing system prompt context key-value pairs")
    user_context: Dict = Field(..., description="Dictionary containing user context attributes")


class TextEventTemplate(BaseModel):
    event_type: Literal["text"]
    text: str = Field(..., description="User input message to be processed by the language model")


class ListenEventTemplate(BaseModel):
    event_type: Literal["listen"]


class DisconnectEventTemplate(BaseModel):
    event_type: Literal["disconnect"]


WebsocketClientEvents = Union[InitiateEventTemplate, TextEventTemplate, ListenEventTemplate, DisconnectEventTemplate]


def websocket_client_event_parser(data_obj: Any):
    adapter = TypeAdapter(WebsocketClientEvents)
    return adapter.validate_json(data_obj)


class UserID(BaseModel):
    id: str
    id_type: str
    id_resource: str


class UserInfo(BaseModel):
    # Replace `Any` with actual fields if known
    user_id: UserID
    user_info: Dict[str, Any]


class UIInfo(BaseModel):
    should_consolidate_buttons: bool


class Channel(BaseModel):
    # Add additional known fields here if CHANNEL_INFO has a defined schema
    ui_info: UIInfo
    other_fields: Optional[Dict[str, Any]] = None  # to accommodate **CHANNEL_INFO


class EventUser(BaseModel):
    user_id: UserID
    user_info: Dict[str, Any]


class EventTemplate(BaseModel):
    event_type: str
    rosters_id: str
    llm_context: Dict[str, Any]
    user_context: Dict[str, Any]


class IncomingEvent(BaseModel):
    event_id: str
    event_user: EventUser
    event_template: Union[InitiateEventTemplate, TextEventTemplate]


class APIRequest(BaseModel):
    version: str
    user_info: UserInfo
    channel: Channel
    incoming_events: List[IncomingEvent]


def api_client_event_parser(data_obj: Any):
    adapter = TypeAdapter(APIRequest)
    return adapter.validate_json(data_obj)
