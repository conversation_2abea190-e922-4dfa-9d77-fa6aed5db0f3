import uuid
import json


class TaskManager:
    def __init__(self):
        self.tasks = {}

    def start_task(self):
        """Start a new task and return its unique ID."""
        task_id = str(uuid.uuid4())
        self.tasks[task_id] = {"results": [], "done": False}
        return task_id

    @staticmethod
    def get_combined_result(results):
        """Combine results by appending default responses."""
        if not results:
            return None
        if len(results) == 1:
            return results[0]

        # Deep copy to avoid mutating original
        final_result = json.loads(json.dumps(results[0]))
        for result in results[1:]:
            final_result["response_map"]["responses"]["default"].append(
                result["response_map"]["responses"]["default"][0]
            )

        return final_result

    def get_status(self, task_id):
        """Return task status and the next available result."""
        if task_id not in self.tasks:
            return {"error": "Invalid task ID"}

        task = self.tasks[task_id]

        if not task["done"]:
            return {"done": False, "result": None}

        result = self.get_combined_result(task["results"])
        if not result:
            return {"done": False, "result": None}
        task["results"].clear()
        task["done"] = False
        return {"done": True, "result": result}

    def add_result(self, task_id, result):
        """Add a new result to a task."""
        if task_id not in self.tasks:
            return {"error": "Invalid task ID"}
        result = json.loads(result)
        task = self.tasks[task_id]
        task["results"].append(result)
        task["done"] = result.get("next_action") == "gather"
        return {"success": True}


task_manager = TaskManager()
