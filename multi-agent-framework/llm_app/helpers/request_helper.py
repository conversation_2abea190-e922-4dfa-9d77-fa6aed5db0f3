"""
This helper function assist in sending http request.
"""

import requests
import json
from loguru import logger


def get_request(url, query_params=None, headers=None, username=None, password=None):
    """
    This function used for invoking get method and wait for result.
    """
    param = {"url": url, "timeout": 30}
    if query_params:
        param["params"] = query_params
    if username and password:
        param["auth"] = (username, password)
    if headers:
        param["headers"] = headers
    try:
        response = requests.get(**param)
        if response.status_code not in [200, 201, 202]:
            try:
                return "Failed", response.json()
            except ValueError:
                return "Failed", {"error": response.text or "Unknown error occurred."}

        return "Success", response.json()

    except Exception as e:
        logger.exception(
            f"Unhandled error processing /helper/get_request: {e}",  # Contextual message
            data={"error_location": "/helper/get_request"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        return "Failed", str(e)


def post_request(url, body=None, headers=None, username=None, password=None):
    """
    This function invokes a POST method and waits for the result.
    """
    param = {"url": url, "timeout": 30}
    if body:
        param["data"] = json.dumps(body)
    if username and password:
        param["auth"] = (username, password)
    if headers:
        param["headers"] = headers
    try:
        response = requests.post(**param)
        if response.status_code not in [200, 201, 202]:
            try:
                return "Failed", response.json()
            except ValueError:
                return "Failed", {"error": response.text or "Unknown error occurred."}
        return "Success", response.json()
    except Exception as e:
        logger.exception(
            f"Unhandled error processing /helper/post_request: {e}",  # Contextual message
            data={"error_location": "/helper/post_request"},
            status="FAILURE",
            # No need to manually add error string, formatter handles it
        )
        return "Failed", {"error": str(e)}