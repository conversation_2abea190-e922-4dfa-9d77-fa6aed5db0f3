# --- Imports ---
# Standard library imports
import json
import time
import random
import uuid
import traceback
import zoneinfo
from collections import defaultdict
from datetime import datetime, timedelta
import re
from typing import Literal, Annotated, Dict, Any, Callable, List

# Third-party imports
from RestrictedPython.Guards import safe_builtins
import requests
from bs4 import BeautifulSoup
from loguru import logger

# --- Constants ---
# Modules explicitly allowed for import within the safe execution environment
ALLOWED_MODULES: List[str] = ["datetime", "requests", "json", "time", "_strptime" , "uuid"]


def _custom_import(name, *args, **kwargs):
    if name in ALLOWED_MODULES:
        return __import__(name, *args, **kwargs)
    raise ImportError(f"Importing '{name}' is restricted in this environment.")


def _build_safe_builtins() -> Dict[str, Any]:
    builtins = safe_builtins.copy()  # Start with RestrictedPython's defaults

    # Explicitly add commonly needed built-in functions and types
    builtins["list"] = list
    builtins["set"] = set
    builtins["dict"] = dict
    builtins["any"] = any
    builtins["all"] = all
    builtins["enumerate"] = enumerate
    builtins["max"] = max
    builtins["min"] = min
    builtins["print"] = print  # Allow standard print

    # Add typing utilities if needed within the executed code
    builtins["Annotated"] = Annotated
    builtins["Literal"] = Literal

    # Crucially, override the default __import__ with our custom gatekeeper
    builtins["__import__"] = _custom_import

    return builtins


def _build_safe_globals() -> Dict[str, Any]:
    safe_globals_namespace = {
        "__builtins__": _build_safe_builtins(),
        "__import__": None,
        "__file__": None,
        "__name__": None,
        # Expose specific safe modules directly
        "re": re,
        "time": time,
        "json": json,
        "random": random,
        "requests": requests,
        "zoneinfo": zoneinfo,
        "uuid": uuid,
        "datetime": datetime,
        "timedelta": timedelta,
        "traceback": traceback,
        # Expose specific safe classes/functions from modules
        "defaultdict": defaultdict,
        "BeautifulSoup": BeautifulSoup,
    }
    return safe_globals_namespace


# --- Global State ---
# Initialize the safe global environment immediately.
# This dictionary will be used by `execute_and_retrieve_function`.
SAFE_GLOBALS: Dict[str, Any] = _build_safe_globals()


def return_function(function_definition: Dict[str, str]) -> Callable:
    code_to_execute = function_definition["code"]
    expected_function_name = function_definition["function_name"]
    execution_globals = SAFE_GLOBALS.copy()
    try:
        exec(code_to_execute, execution_globals)

        created_function = execution_globals[expected_function_name]

        if not callable(created_function):
            raise TypeError(f"Object '{expected_function_name}' created by code is not callable.")

        return created_function

    except KeyError as exception:
        logger.exception(f"Function '{expected_function_name}' not found after executing code. {exception}")
        raise
    except Exception as exception:
        logger.exception(
            f"Error while executing dynamic code '{expected_function_name}' {exception}",
        )
        raise
