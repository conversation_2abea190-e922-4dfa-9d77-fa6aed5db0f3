import uuid
from datetime import datetime
from enum import Enum
from random import randint
from typing import List

from .BaseEntity import BaseEntity


class AnalyticsBaseEvent(BaseEntity):
    def __init__(self, internal_targets: List[str], integration_hub_targets: List[str], rozie_application_type: str,
                 application_id: str, event_name: str, rozie_correlation_id: str, is_test_request: bool,
                 timestamp: datetime):
        super().__init__()
        self.id = f"id_{uuid.uuid4()}_{randint(1, 100000)}"
        self.internal_targets = internal_targets
        self.integration_hub_targets = integration_hub_targets
        self.rozie_application_type = rozie_application_type
        self.application_id = application_id
        self.event_name = event_name
        self.rozie_correlation_id = rozie_correlation_id
        self.timestamp = timestamp if timestamp is not None else datetime.utcnow()
        self.is_test_request = is_test_request
        self.custom_fields = dict()
        self.pii_fields = dict()
        self.concept_fields = dict()

    def add_global_property(self, key, value):
        self.custom_fields[key] = value

    @staticmethod
    def convert_enum_value(value):
        if isinstance(value, Enum):
            return value.value
        return value

    def convert_list_of_enums(self, value):
        return [self.convert_enum_value(item) for item in value] if isinstance(value, list) \
            else self.convert_enum_value(value)

    def format_attribute(self, value):
        return value.isoformat() if isinstance(value, datetime) else self.convert_list_of_enums(value)

    def to_dict(self):
        return {
            attr: self.format_attribute(value)
            for attr, value in self.__dict__.items()
        }