class AnalyticsConfig:
    def __init__(self):
        self.internal_targets = ["review_tool", "all_reports"]
        self.integration_hub_targets = []
        self.rozie_application_type = "concierge"
        self.send_url = "/api/v1/internal-events"

    def to_dict(self):
        return {
            "internal_targets": self.internal_targets,
            "integration_hub_targets": self.integration_hub_targets,
            "rozie_application_type": self.rozie_application_type,
            "send_url": self.send_url
        }
