from datetime import datetime
from typing import List, Any, Dict
from cachetools import TTLCache
from starlette.datastructures import Headers

from llm_app.analytics.LOCAL.models import AnalyticsBaseEvent
from llm_app.analytics import analytics_constants
from llm_app.analytics import header_constants
from llm_app.analytics.LOCAL.analytics_config import AnalyticsConfig
from llm_app.analytics.event_name import EventName
from llm_app.logging.logging_context import (
    conversation_id,
    channel_user_id_var,
    user_id_var,
    channel_var,
    language_var,
    rozie_correlation_id_var,
)
import uuid
import requests
import os
import json


class CacheItem:
    def __init__(self, channel_user_id: str):
        self.channel_user_id = channel_user_id
        self.rozie_user_id = f'rozie_user_{uuid.uuid4()}'
        self.conversation_id = f'conversation_{uuid.uuid4()}'

    def to_dict(self) -> Dict[str, Any]:
        return {
            'channel_user_id': self.channel_user_id,
            'rozie_user_id': self.rozie_user_id,
            'conversation_id': self.conversation_id
        }

    def to_json(self) -> str:
        return json.dumps(self.to_dict())

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheItem':
        item = cls(data['channel_user_id'])
        item.rozie_user_id = data.get('rozie_user_id', item.rozie_user_id)
        item.conversation_id = data.get('conversation_id', item.conversation_id)
        return item

    @classmethod
    def from_json(cls, json_str: str) -> 'CacheItem':
        return cls.from_dict(json.loads(json_str))


class AnalyticsService:
    def __init__(self):
        self.analytics_config = AnalyticsConfig()
        self.analytics_url = os.getenv("ANALYTICS_URL")
        self.cache = TTLCache(maxsize=100, ttl=3600)

        if not self.analytics_url:
            raise EnvironmentError("Missing required environment variable: ANALYTICS_URL")

    def _create_new_event(self, event_name: str, headers: Headers, timestamp: datetime = None) -> AnalyticsBaseEvent:

        application_id = headers.get(header_constants.APPLICATION_ID)
        rozie_correlation_id = f'rozie_correlation_{str(uuid.uuid4())}'
        if not application_id.strip() or not event_name.strip() or not rozie_correlation_id.strip():
            raise Exception("Application Id, Event Name & Rozie Correlation Id can't be null or empty")
        if not (self.analytics_config.internal_targets or self.analytics_config.integration_hub_targets):
            raise Exception("Internal Targets or Integration Hub Targets should be configured")

        analytics_event = AnalyticsBaseEvent(internal_targets=self.analytics_config.internal_targets,
                          integration_hub_targets=self.analytics_config.integration_hub_targets,
                          rozie_application_type=self.analytics_config.rozie_application_type,
                          application_id=application_id,
                          event_name=event_name,
                          rozie_correlation_id=rozie_correlation_id,
                          is_test_request=False,
                          timestamp=timestamp)

        analytics_event.add_global_property(analytics_constants.CHANNEL_USER_ID, channel_user_id_var.get())
        analytics_event.add_global_property(analytics_constants.ROZIE_USER_ID, user_id_var.get())
        analytics_event.add_global_property(analytics_constants.CHANNEL_NAME, channel_var.get() if channel_var.get() is not None else "default")
        analytics_event.add_global_property(analytics_constants.CHANNEL_ID, headers.get(analytics_constants.CHANNEL_ID, ""))
        analytics_event.add_global_property(analytics_constants.CONVERSATION_ID, conversation_id.get())
        analytics_event.add_global_property(analytics_constants.LANGUAGE, language_var.get() if language_var.get() is not None else "en_us")

        return analytics_event

    async def _add_and_flush_event(self, application_id: str, analytics_events: List[AnalyticsBaseEvent]):
        url = self.analytics_url + self.analytics_config.send_url
        headers = {
            header_constants.APPLICATION_ID: application_id,
            "Content-Type": "application/json"
        }
        response = requests.post(url, json=[analytics_event.to_dict() for analytics_event in analytics_events],
                                 headers=headers)
        response.raise_for_status()

    # Analytic Service Cache Public Methods

    async def get_cached_chat(self, application_id: str, chatId: str) -> CacheItem | None:

        key = f"{application_id}_{chatId}"
        if key not in self.cache:
            return None
        else:
            try:
                data = self.cache[key]
                if data:
                    if isinstance(data, str):
                        return CacheItem.from_json(data)
                    else:
                        return CacheItem.from_dict(data)
                else:
                    return None
            except json.JSONDecodeError:
                return None

    async def _is_chat_exists(self, application_id: str, chatId: str) -> bool:
        key = f"{application_id}_{chatId}"
        return True if key in self.cache else False

    async def _cache_new_chat(self, application_id: str, chatId: str):
        key = f"{application_id}_{chatId}"
        cached_chat = CacheItem(chatId)
        self.cache[key] = cached_chat.to_json()

    # Analytic Service Events Public Methods

    async def add_new_user_created_event(self, chat_id: str, request: dict, headers: Headers):
        application_id = headers.get(header_constants.APPLICATION_ID)

        chat_exists = await self._is_chat_exists(application_id, chat_id)
        if not chat_exists:
            try:
                # Create analytics new user created event
                new_user_created_event = self._create_new_event(EventName.NEW_USER_CREATED.value, headers)

                user_name = request.get("user_info").get("user_info").get("user_name")
                new_user_created_event.pii_fields[
                    analytics_constants.CUSTOMER_NAME] = user_name if user_name is not None else "Annonymous User"

                # Add and flush event
                await self._add_and_flush_event(application_id, [new_user_created_event])
                print(f"Added New User Created analytics event for {application_id}")

            except Exception as e:
                error_log = f"Failed to add New User Created analytics event for {application_id} - Exception: {str(e)}"
                print(error_log)

    async def add_conversation_started_event(self, chat_id: str, request: dict, headers: Headers):
        application_id = headers.get(header_constants.APPLICATION_ID)

        chat_exists = await self._is_chat_exists(application_id, chat_id)
        if not chat_exists:
            await self._cache_new_chat(application_id, chat_id)
            try:
                # Create analytics conversation started event
                conversation_started_event = self._create_new_event(EventName.CONVERSATION_STARTED.value, headers)

                # Add and flush event
                await self._add_and_flush_event(application_id, [conversation_started_event])
                print(f"Added Conversation Started analytics event for {application_id}")

            except Exception as e:
                error_log = f"Failed to add Conversation Started analytics event for {application_id} - Exception: {str(e)}"
                print(error_log)

    async def add_request_received_event(self, chat_id: str, request: dict, headers: Headers):
        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            request_received_events = []
            for incoming_event in request.get("incoming_events", []):
                # Create analytics request received event
                request_received_event = self._create_new_event(EventName.REQUEST_RECEIVED.value, headers)

                # Add event specific properties
                event_type = incoming_event.get("event_template").get("event_type")
                request_received_event.custom_fields[analytics_constants.RequestType] = event_type

                if event_type == "text":
                    request_received_event.pii_fields[analytics_constants.RequestText] = incoming_event.get(
                        "event_template").get("text")
                elif event_type == "contextkey":
                    request_received_event.custom_fields[analytics_constants.ContextLabel] = incoming_event.get(
                        "event_template").get("context_label")

                request_received_events.append(request_received_event)

            if request_received_events:
                # Add and flush event
                await self._add_and_flush_event(application_id, request_received_events)
                print(f"Added Request Received analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Request Received analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_response_sent_event(self, chat_id: str, response: dict, headers: Headers):
        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            response_sent_events = []
            response_templates = response.get("response_map", {}).get("responses", {}).get("default", [])
            for response_template in response_templates:
                template = response_template.get("response_template", {})
                response_type = template.get("response_type")

                # Create analytics response sent event
                response_sent_event = self._create_new_event(EventName.RESPONSE_SENT.value, headers)

                response_sent_event.custom_fields[analytics_constants.ResponseType] = response_type
                if response_type == "text":
                    response_sent_event.pii_fields[analytics_constants.ResponseText] = template.get("text")
                elif response_type == "quick_reply":
                    response_sent_event.custom_fields[analytics_constants.QuickReplyLabel] = [item.get("label") for item in
                                                                                           template.get("items", [])]
                    response_sent_event.custom_fields[analytics_constants.StatementType] = "quick_reply"

                response_sent_events.append(response_sent_event)

            if response_sent_events:
                # Add and flush event
                await self._add_and_flush_event(application_id, response_sent_events)
                print(f"Added Response Sent analytics events for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Response Sent analytics events for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_conversation_ended_event(self, chat_id: str, response: dict, headers: Headers):
        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics conversation ended event
            conversation_ended_event = self._create_new_event(EventName.CONVERSATION_ENDED.value, headers)

            # Add and flush event
            await self._add_and_flush_event(application_id, [conversation_ended_event])
            print(f"Added Conversation Ended analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Conversation Ended analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)
