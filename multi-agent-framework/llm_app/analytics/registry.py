from llm_app.analytics.AWS.analytics_service import AnalyticsService as AWSAnalyticsService
from llm_app.analytics.LOCAL.analytics_service import AnalyticsService as LOCALAnalyticsService
import os

def init_analytics_service() -> AWSAnalyticsService | LOCALAnalyticsService | None:
    if os.getenv("ENABLE_ANALYTICS", "true").lower() == "true":
        deployment_type = os.environ.get("DEPLOYMENT_TYPE", "LOCAL")
        if deployment_type == "AWS":
            return AWSAnalyticsService()
        else:
            return LOCALAnalyticsService()
    else:
        print("Analytics service is disabled.")
        return None


def get_analytics_service() -> AWSAnalyticsService | LOCALAnalyticsService | None:
    return analytics_service


analytics_service = init_analytics_service()
