from datetime import datetime
from typing import List

from starlette.datastructures import Headers

from llm_app.analytics.AWS.models import AnalyticsBaseEvent
from llm_app.analytics import analytics_constants
from llm_app.analytics import header_constants
from llm_app.analytics.AWS.analytics_config import AnalyticsConfig
from llm_app.analytics.event_name import EventName
from llm_app.logging.logging_context import (
    conversation_id,
    channel_user_id_var,
    user_id_var,
    channel_var,
    language_var,
    rozie_correlation_id_var,
)
import requests
import os


class AnalyticsService:
    def __init__(self):
        self.analytics_config = AnalyticsConfig()
        self.analytics_url = os.getenv("ANALYTICS_URL")

        if not self.analytics_url:
            raise EnvironmentError("Missing required environment variable: ANALYTICS_URL")

    def _create_new_event(
        self, event_name: str, headers: Headers, timestamp: datetime = None
    ) -> AnalyticsBaseEvent:

        application_id = headers.get(header_constants.APPLICATION_ID)
        if (
            not application_id.strip()
            or not event_name.strip()
            or not rozie_correlation_id_var.get().strip()
        ):
            raise Exception(
                "Application Id, Event Name & Rozie Correlation Id can't be null or empty"
            )

        analytics_event = AnalyticsBaseEvent(
            rozie_application_type=self.analytics_config.rozie_application_type,
            conversation_id=conversation_id.get(),
            application_id=application_id,
            event_name=event_name,
            rozie_correlation_id=rozie_correlation_id_var.get(),
            timestamp=timestamp,
        )

        analytics_event.add_global_property(analytics_constants.CHANNEL_USER_ID, channel_user_id_var.get())
        analytics_event.add_global_property(analytics_constants.ROZIE_USER_ID, user_id_var.get())
        analytics_event.add_global_property(analytics_constants.CHANNEL_NAME, channel_var.get())
        analytics_event.add_global_property(analytics_constants.CHANNEL_ID, headers.get(analytics_constants.CHANNEL_ID, ""))
        analytics_event.add_global_property(analytics_constants.LANGUAGE, language_var.get())

        return analytics_event

    async def _add_and_flush_event(
        self, application_id: str, analytics_events: List[AnalyticsBaseEvent]
    ):
        url = self.analytics_url + self.analytics_config.send_url
        headers = {
            header_constants.APPLICATION_ID: application_id,
            "Content-Type": "application/json",
        }
        response = requests.post(
            url,
            json=[analytics_event.to_dict() for analytics_event in analytics_events],
            headers=headers,
            timeout=10
        )
        # response.raise_for_status()

    async def add_new_user_created_event(
        self, chat_id: str, request: dict, headers: Headers
    ):
        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics new user created event
            new_user_created_event = self._create_new_event(
                EventName.NEW_USER_CREATED.value, headers
            )
            # Add event specific properties
            for event in request.get("incoming_events", []):
                event = event.get("event_template", {})
                event.update(
                    {analytics_constants.EVENT_SOURCE: analytics_constants.CUSTOMER}
                )
                new_user_created_event.add_event(event)

            # Add and flush event
            await self._add_and_flush_event(application_id, [new_user_created_event])
            print(f"Added New User Created analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add New User Created analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_conversation_started_event(
        self, chat_id: str, request: dict, headers: Headers
    ):

        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics conversation started event
            conversation_started_event = self._create_new_event(
                EventName.CONVERSATION_STARTED.value, headers
            )
            # Add event specific properties
            for event in request.get("incoming_events", []):
                event = event.get("event_template", {})
                event.update(
                    {analytics_constants.EVENT_SOURCE: analytics_constants.CUSTOMER}
                )
                conversation_started_event.add_event(event)
            # Add and flush event
            await self._add_and_flush_event(
                application_id, [conversation_started_event]
            )
            print(f"Added Conversation Started analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Conversation Started analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_request_received_event(
        self, chat_id: str, request: dict, headers: Headers
    ):

        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics request received event
            request_received_event = self._create_new_event(
                EventName.REQUEST_RECEIVED.value, headers
            )
            # Add event specific properties
            for event in request.get("incoming_events", []):
                event = event.get("event_template", {})
                event.update(
                    {analytics_constants.EVENT_SOURCE: analytics_constants.CUSTOMER}
                )
                request_received_event.add_event(event)
            # Add and flush event
            await self._add_and_flush_event(application_id, [request_received_event])
            print(f"Added Request Received analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Request Received analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_response_sent_event(
        self, chat_id: str, response: dict, headers: Headers
    ):

        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics response sent event
            response_sent_event = self._create_new_event(
                EventName.RESPONSE_SENT.value,
                headers,
            )
            response_templates = (
                response.get("response_map", {}).get("responses", {}).get("default", [])
            )
            # Add event specific properties
            for response_template in response_templates:
                response_template = response_template.get("response_template", {})
                response_template.update(
                    {analytics_constants.EVENT_SOURCE: analytics_constants.AGENT}
                )
                response_sent_event.add_event(response_template)
            # Add and flush event
            await self._add_and_flush_event(application_id, [response_sent_event])
            print(f"Added Response Sent analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Response Sent analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)

    async def add_conversation_ended_event(
        self, chat_id: str, response: dict, headers: Headers
    ):

        application_id = headers.get(header_constants.APPLICATION_ID)
        try:
            # Create analytics conversation started event
            conversation_ended_event = self._create_new_event(
                EventName.CONVERSATION_ENDED.value,
                headers,
            )
            response_templates = (
                response.get("response_map", {}).get("responses", {}).get("default", [])
            )
            # Add event specific properties
            for response_template in response_templates:
                response_template = response_template.get("response_template", {})
                response_template.update(
                    {analytics_constants.EVENT_SOURCE: analytics_constants.AGENT}
                )
                conversation_ended_event.add_event(response_template)
            # Add and flush event
            await self._add_and_flush_event(application_id, [conversation_ended_event])
            print(f"Added Conversation Ended analytics event for {application_id}")

        except Exception as e:
            error_log = f"Failed to add Conversation Ended analytics event for {application_id} - Exception: {str(e)}"
            print(error_log)
