import jinja2
import os
import glob
from typing import Dict, <PERSON>, <PERSON><PERSON>
from loguru import logger

# Set up logging

def discover_templates(template_dir: str) -> Dict[str, str]:
    """
    Automatically discover all templates in the templates directory.

    Args:
        template_dir (str): Directory containing templates

    Returns:
        Dict[str, str]: Mapping of template names to filenames
    """
    template_registry = {}

    if not os.path.exists(template_dir):
        logger.warning(f"Templates directory not found: {template_dir}")
        return template_registry

    # Find all template files (.jinja, .html)
    template_patterns = [
        os.path.join(template_dir, "*.jinja"),
        os.path.join(template_dir, "*.html")
    ]

    for pattern in template_patterns:
        for file_path in glob.glob(pattern):
            # Extract template name from filename (without extension)
            template_name = os.path.splitext(os.path.basename(file_path))[0]
            filename = os.path.basename(file_path)
            template_registry[template_name] = filename
            logger.info(f"Discovered template: '{template_name}' -> {filename}")

    logger.info(f"Total templates discovered: {len(template_registry)}")
    return template_registry

# Initialize Jinja2 environment
template_dir = "llm_app/pipeline/steps/response_builder/templates"
try:
    jinja_env = jinja2.Environment(
        loader=jinja2.FileSystemLoader(template_dir),
        autoescape=jinja2.select_autoescape(["html", "xml"]),
    )

    # Auto-discover templates 
    template_registry = discover_templates(template_dir)

    logger.info("Jinja2 environment initialized successfully")
except Exception as e:
    logger.error(f"Error initializing Jinja2 environment: {str(e)}")
    jinja_env = None
    template_registry = {}


def render_template(template_name: str, template_values: Dict[str, Any]) -> Tuple[str, str]:
    """
    Render a template with the provided values.

    Args:
        template_name: The name of the template to render
        template_values: The values to use for rendering

    Returns:
        A tuple of (html_content, css_content)
    """
    import re

    if not jinja_env or template_name not in template_registry:
        logger.error(f"Cannot render template: {template_name}")
        return "", ""  # Return empty strings to trigger fallback

    try:
        template_file = template_registry[template_name]
        template = jinja_env.get_template(template_file)
        rendered_html = template.render(**template_values)

        # Extract CSS from the rendered HTML
        css_content = ""
        html_content = rendered_html

        # Look for <style> tags
        style_pattern = re.compile(r"<style>(.*?)</style>", re.DOTALL)
        style_matches = style_pattern.findall(rendered_html)

        if style_matches:
            # Extract CSS content
            css_content = "\n".join(style_matches)

            # Remove style tags from HTML
            html_content = style_pattern.sub("", rendered_html)

        return html_content, css_content
    except Exception as e:
        logger.error(f"Error rendering template {template_name}: {str(e)}")
        return "", ""  # Return empty strings to trigger fallback
