from loguru import logger


def load_system_message_template(PROMPT_PATH):
    try:
        with open(PROMPT_PATH, encoding="utf-8") as file_object:
            return file_object.read()
    except FileNotFoundError as e:
        logger.exception("System message file not found", data={"filename": e.filename}, status="FAILURE")
        raise
    except IOError as e:
        logger.exception("Error reading system message file", status="FAILURE")
        raise


def format_system_message(template: str, agent_id: str, params: dict):
    try:
        system_message = template.format(**params)
        logger.debug(f"{agent_id}_prompt", data={"prompt": system_message})
        return system_message
    except KeyError as e:
        logger.exception(f"Missing placeholder '{e.args[0]}' in system message template for {agent_id}.")
        raise
