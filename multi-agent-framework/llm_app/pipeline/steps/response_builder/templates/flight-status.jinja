{# Flight Status Template #}
<div class="flight-status-card">
  <div class="flight-header">
    <div class="flight-id">{{ flight_id }}</div>
    <div class="flight-status" style="background-color:
      {% if status == 'ON TIME' %}#01bc47
      {% elif status == 'DELAYED' %}#FA9620
      {% elif status == 'IN AIR' %}#4b5563
      {% elif status == 'ARRIVED' %}#9ca3af
      {% else %}#9ca3af{% endif %};">
      {{ status }}
    </div>
  </div>
  <div class="flight-route">
    <div class="departure">
      <div class="airport-code">{{ departure_airport_code }}</div>
      <div class="city">{{ departure_city }}</div>
      <div class="date">
        {% if departure_date %}
          {{ departure_date }}
        {% else %}
          Unknown
        {% endif %}
      </div>
      <div class="time">
        {% if departure_time_display %}
          {{ departure_time_display }}
        {% else %}
          Unknown
        {% endif %}
      </div>
    </div>
    <div class="flight-info">
      <div class="duration">{{ duration }}</div>
      <div class="arrow">→</div>
    </div>
    <div class="arrival">
      <div class="airport-code">{{ arrival_airport_code }}</div>
      <div class="city">{{ arrival_city }}</div>
      <div class="date">
        {% if arrival_date %}
          {{ arrival_date }}
        {% else %}
          Unknown
        {% endif %}
      </div>
      <div class="time">
        {% if arrival_time_display %}
          {{ arrival_time_display }}
        {% else %}
          Unknown
        {% endif %}
      </div>
    </div>
  </div>

  {% if connections and connections|length > 0 %}
  <div class="connections">
    <h3>Connections</h3>
    {% for connection in connections %}
    <div class="connection-item">
      <div class="connection-airport">{{ connection.airport_code }}</div>
      <div class="connection-time">{{ connection.time }}</div>
    </div>
    {% endfor %}
  </div>
  {% endif %}
</div>

<style>
.flight-status-card {
  font-family: 'Inter', sans-serif;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  max-width: 400px;
  margin: 0 auto;
}

.flight-header {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background-color: #f3f4f6;
}

.flight-id {
  font-weight: 700;
  font-size: 18px;
}

.flight-status {
  font-size: 12px;
  font-weight: 700;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
}

.flight-route {
  display: flex;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
}

.departure, .arrival {
  flex: 1;
  text-align: center;
}

.flight-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 12px;
}

.airport-code {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.city {
  font-size: 14px;
  color: #4b5563;
  margin-bottom: 8px;
}

.date {
  font-size: 12px;
  color: #6b7280;
}

.time {
  font-size: 18px;
  font-weight: 600;
  margin-top: 4px;
}

.duration {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.arrow {
  font-size: 20px;
  color: #9ca3af;
}

.connections {
  border-top: 1px solid #e5e7eb;
  padding: 16px;
}

.connections h3 {
  font-size: 16px;
  margin-bottom: 12px;
  text-align: center;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f9fafb;
  border-radius: 4px;
}

.connection-airport {
  font-weight: 600;
}

.connection-time {
  color: #6b7280;
}
</style>