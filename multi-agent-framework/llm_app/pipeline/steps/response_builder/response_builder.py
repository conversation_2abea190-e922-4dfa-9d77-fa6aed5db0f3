from typing import Async<PERSON>enerator


from llm_app.pipeline.models.step import PipelineStep
from llm_app.pipeline.models.data import PipelineData

from llm_app.pipeline.steps.response_builder.agent.response_builder_agent import ResponseBuilderAgent
from llm_app.pipeline.steps.response_builder.helpers.rozie_response_helper import (
    format_llm_response_message,
    format_template_response_message,
    format_knowledge_base_response_message,
)
from llm_app.pipeline.models.state import (
    ResponseBuilderStatePartial,
    ResponseBuilderStateFinal,
)
from llm_app.pipeline.steps.response_builder.agent.models.SendMessageResponse import AgentResponse, TextResponseTemplate


class ResponseBuilder(PipelineStep):
    def __init__(self):
        self.llm_response_builder_object = ResponseBuilderAgent()

    async def process(self, data: PipelineData) -> AsyncGenerator[PipelineData, None]:
        if data.state.response_type == "text":
            # Handle text response using the LLM-based response builder
            llm_response_content = data.state.text
            if not llm_response_content:
                raise ValueError("Missing 'llm_response_content' in pipeline data.")
            if data.state.is_forced_text_template:
                pass
                response_builder_response = AgentResponse(send_message=TextResponseTemplate(type="text"))
            else:
                response_builder_response = await self.llm_response_builder_object.handle_user_message(
                    llm_response_content, data.event.conversation_id
                )

            parsed_chunks = [format_llm_response_message(llm_response_content, response_builder_response, data)]

        elif data.state.response_type == "template":
            # Handle template response (function-driven, not LLM-driven)
            template_data = data.state.template
            if not template_data:
                raise ValueError("Missing 'template' data in pipeline data.")

            # Extract template name and values
            # Support both formats:
            # 1. {"template_name": "name", "template_values": {...}}
            # 2. {"template_name": "name", "output_keys": {...}}
            template_name = template_data.get("template_name")
            template_values = template_data.get("template_values") or template_data.get("output_keys", {})

            if not template_name:
                raise ValueError("Missing 'template_name' in template data.")

            # Process the template using the helper function
            parsed_chunks = [format_template_response_message(template_name, template_values, data)]
        elif data.state.response_type == "knowledge_base_text":
            llm_response_content = data.state.text
            if not llm_response_content:
                raise ValueError("Missing 'llm_response_content' in pipeline data.")
            response_builder_response = AgentResponse(send_message=TextResponseTemplate(type="text"))
            parsed_chunks = [
                format_llm_response_message(
                    llm_response_content, response_builder_response, data, "say" if data.state.references else None
                ),
            ]
            if data.state.references:
                parsed_chunks.append(
                    format_knowledge_base_response_message(data.state.references, data.state.knowledge_base_query, data)
                )
        else:
            raise ValueError(f"Unsupported response_type: {data.state.response_type}")

        # Yield partial states for all but the last chunk
        for chunk in parsed_chunks[:-1]:
            yield PipelineData(
                **data.model_dump(exclude={"state"}), state=ResponseBuilderStatePartial(channel_response=chunk)
            )

        # Yield final state
        if parsed_chunks:
            yield PipelineData(
                **data.model_dump(exclude={"state"}),
                state=ResponseBuilderStateFinal(channel_response=parsed_chunks[-1]),
            )
