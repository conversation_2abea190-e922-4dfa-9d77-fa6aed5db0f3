from pydantic import BaseModel, Field
from typing import Literal, List, Union


class TextResponseTemplate(BaseModel):
    type: Literal["text"]


class OptionsResponseTemplate(BaseModel):
    type: Literal["options"]
    message: str = Field(..., description="TRephrased assistant message without options.")
    options: List[str] = Field(..., description="Distinct labels for each option.")


class AgentResponse(BaseModel):
    send_message: Union[TextResponseTemplate, OptionsResponseTemplate]
