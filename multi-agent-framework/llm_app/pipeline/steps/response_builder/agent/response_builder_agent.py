from autogen_core.models import ModelFamily
from autogen_core.models import SystemMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient

from langfuse.decorators import observe, langfuse_context


from llm_app.pipeline.steps.response_builder.helpers.llm_agent_helper import (
    load_system_message_template,
    format_system_message,
)
from llm_app.pipeline.steps.response_builder.agent.models.SendMessageResponse import AgentR<PERSON>ponse

RESPONSE_PROMPT_PATH = "llm_app/pipeline/steps/response_builder/agent/resources/prompt_templates/response_generation.txt"


class ResponseBuilderAgent:
    def __init__(
        self,
    ) -> None:
        self.agent_id = "ResponseBuilderAgent"
        self._model_name = "gpt-4.1"
        self._model_client = OpenAIChatCompletionClient(
            model="gpt-4.1-2025-04-14",
            model_info={
                "vision": True,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.GPT_4O,
                "structured_output": True,
            },
            response_format=AgentResponse,
        )
        self.response_builder_system_template = load_system_message_template(RESPONSE_PROMPT_PATH)

    @observe()
    async def handle_user_message(self, last_assistant_message, conversation_id) -> AgentResponse:
        langfuse_context.update_current_observation(
            input={"text": last_assistant_message},
            session_id=conversation_id,
            model=self._model_name,
            tags=[self.agent_id],
        )
        return await self._invoke_llm_loop(last_assistant_message)

    @observe(as_type="generation")
    async def _invoke_llm_loop(self, last_assistant_message):
        _system_message = format_system_message(
            self.response_builder_system_template, self.agent_id, {"last_assistant_message": last_assistant_message}
        )
        response = await self._model_client.create(messages=[SystemMessage(content=_system_message)])
        if response.usage:
            usage = response.usage
            langfuse_context.update_current_observation(
                model=self._model_name, usage={"input": usage.prompt_tokens, "output": usage.completion_tokens}
            )
        return AgentResponse.model_validate_json(response.content)
