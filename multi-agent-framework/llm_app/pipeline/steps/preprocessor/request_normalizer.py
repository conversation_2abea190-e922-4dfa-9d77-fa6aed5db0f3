from llm_app.pipeline.models.step import PipelineStep
from llm_app.pipeline.models.data import PipelineData
from llm_app.pipeline.steps.agentic_framework.autogen.processor import AutoGenProcessor


class RequestNormalizer(PipelineStep):
    def __init__(self, processor: AutoGenProcessor):
        self.processor = processor

    async def process(self, data: PipelineData) -> PipelineData:
        conversation_id = data.event.conversation_id

        if not conversation_id:
            raise ValueError("Missing 'conversation_id' in event data.")
        return data
