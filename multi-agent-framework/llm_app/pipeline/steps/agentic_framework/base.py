from llm_app.pipeline.models.step import PipelineStep
from abc import ABC, abstractmethod


class BaseAgenticProcessor(PipelineStep, ABC):
    def __init__(self, config):
        """Initialize with a configuration dict or object"""
        self.config = config
        self._init_processor()

    @abstractmethod
    def _init_processor(self):
        """Set up agentic logic and supporting components (agents, tools, memory, etc)."""
        pass

    @abstractmethod
    async def process(self, data):
        """Run agentic logic and return processed result"""
        pass

    @abstractmethod
    def has_session(self, session_id: str) -> bool:
        """Return True if the session exists"""
        pass

    def shutdown(self):
        """Clean up resources if necessary (threads, memory, models)."""
        pass
