import json
import time
from typing import AsyncGenerator
from llm_app.pipeline.models.data import PipelineData
from llm_app.pipeline.models.state import LLMProcessorStatePartial, LLMProcessorStateFinal
from llm_app.pipeline.steps.agentic_framework.base import BaseAgenticProcessor
from llm_app.pipeline.steps.agentic_framework.autogen.conversation_manager import ConversationManager


class AutoGenProcessor(BaseAgenticProcessor):
    def _init_processor(self):
        self.conversation_manager = ConversationManager()

    def _create_pipeline_data(self, data: PipelineData, item: dict) -> PipelineData:
        next_action = "say" if item["partial_response"] else "gather"
        item["next_action"] = next_action
        is_partial = item["partial_response"]

        state_cls = LLMProcessorStateFinal if not is_partial else LLMProcessorStatePartial
        return PipelineData(**data.model_dump(exclude={"state"}), state=state_cls(**item))

    async def process(self, data: PipelineData) -> AsyncGenerator[PipelineData, None]:
        async for item in self.conversation_manager.run(
            data.event.conversation_id, data.event.event_template, data.event.channel
        ):
            if item["response_type"] == "error":
                raise Exception(f'source: {item["source"]}, error:{item["error"]}')
            yield self._create_pipeline_data(data, item)

    def has_session(self, session_id: str) -> bool:
        return session_id in self.conversation_manager.sessions

    def shutdown(self):
        if hasattr(self.agent, "sessions"):
            self.agent.sessions.clear()
