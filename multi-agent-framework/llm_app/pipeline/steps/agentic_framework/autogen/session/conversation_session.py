from loguru import logger
from typing import List, Any
from autogen_core.model_context import Buffered<PERSON>hatCompletionContext
from autogen_core.models import LLMMessage, AssistantMessage


class ConversationSession:
    """A class to manage conversation sessions with message history."""

    def __init__(self):
        self._session_context = BufferedChatCompletionContext(buffer_size=500)

    async def add_message(self, message: LLMMessage):
        """Add an message to the conversation history.

        Args:
            message (LLMMessage): llm message to add

        """
        await self._session_context.add_message(message)

    async def clear_messages(self):
        """Clear all messages from the conversation history."""
        await self._session_context.clear()

    async def get_conversation_messages(self):
        """Retrieve all messages from the conversation history.

        Returns:
            list: List of conversation messages
        """
        messages = await self._session_context.get_messages()
        return messages

    async def get_route_agent(self, route_agent):
        """
            Retrieves route agent to send message to
        Returns:
            str: route agent
        """
        messages = await self._session_context.get_messages()
        if len(messages) > 0 and isinstance(messages[-1], AssistantMessage) and isinstance(messages[-1].source, str):
            return messages[-1].source
        return route_agent
