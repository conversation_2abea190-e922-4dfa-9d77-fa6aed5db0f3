from autogen_core import TypeSubscription
from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.agent_user import UserAgent


class UserAgentWrapper:
    def __init__(
        self,
        *,
        runtime,
        response_queue,
        description: str,
        stream_done=None,
    ):
        self.runtime = runtime
        self.response_queue = response_queue
        self.agent_topic_type = "User"
        self.description = description
        self.stream_done = stream_done

    async def create(self):
        agent_type = await UserAgent.register(
            self.runtime,
            type=self.agent_topic_type,
            factory=lambda: UserAgent(
                description=self.description,
                response_queue=self.response_queue,
                stream_done=self.stream_done,
            ),
        )
        await self.runtime.add_subscription(
            TypeSubscription(topic_type=self.agent_topic_type, agent_type=agent_type.type)
        )
        return agent_type
