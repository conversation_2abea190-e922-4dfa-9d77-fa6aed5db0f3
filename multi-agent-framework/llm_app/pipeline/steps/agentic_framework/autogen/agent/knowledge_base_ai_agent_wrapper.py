import types
from autogen_core import TypeSubscription
from autogen_core.models import SystemMessage
from autogen_core.tools import FunctionTool

from llm_app.pipeline.steps.agentic_framework.autogen.interfaces.knowledge_agent_base import KnowledgeBaseAIAgent
from llm_app.pipeline.steps.agentic_framework.autogen.agent.default_tools.get_information_tools import return_tool

from llm_app.pipeline.steps.agentic_framework.autogen.utils.prompt_generation import _get_kb_agent_system_prompt


class KnowledgeBaseAIAgentWrapper:
    def __init__(
        self,
        *,
        config: dict,
        runtime,
        model_client,
        response_queue,
        user_topic_type,
        toolset: list = None,
        delegate_tools: list = None,
    ):
        self.config = config
        self.runtime = runtime
        self.model_client = model_client
        self.response_queue = response_queue
        self.user_topic_type = user_topic_type
        self.delegate_tools = delegate_tools or []
        self.toolset = toolset or []
        # Dynamically created from config
        self.agent_topic_type = "Knowledge_Base_Agent"
        self.agent_name = "Knowledge_Base_Agent"
        self.system_message = SystemMessage(content=_get_kb_agent_system_prompt(config.copy()))
        self.description = "Assisting customers by providing information and resolving queries using the knowledge base, including FAQs, policies, and troubleshooting guides. using available functions. Your job is to accurately and efficiently resolve customer inquiries, asking clarifying questions whenever essential to provide a precise and helpful answer."

    async def create(self):
        # add default get information tool
        self.toolset.append(return_tool())
        agent_type = await KnowledgeBaseAIAgent.register(
            self.runtime,
            type=self.agent_topic_type,
            factory=lambda: KnowledgeBaseAIAgent(
                description=self.description,
                system_message=self.system_message,
                model_client=self.model_client,
                tools=self.toolset,
                delegate_tools=self.delegate_tools,
                agent_topic_type=self.agent_topic_type,
                user_topic_type=self.user_topic_type,
                response_queue=self.response_queue,
            ),
        )

        await self.runtime.add_subscription(
            TypeSubscription(topic_type=self.agent_topic_type, agent_type=agent_type.type)
        )
        return agent_type

    def get_delegate_tool(self):
        agent_role = self.description
        agent_name = self.agent_name
        agent_topic = self.agent_topic_type

        # Generate a safe function name from agent_name
        func_name = f"route_to_{agent_name.lower().replace(' ', '_')}"

        # Define a closure-compatible function
        def _route_fn() -> str:
            return agent_topic

        # Create a dynamic function with the proper closure
        code = _route_fn.__code__
        globals_dict = globals()
        name = func_name
        arg_defs = _route_fn.__defaults__
        closure = _route_fn.__closure__  # This is what was missing

        # Construct the new function
        route_fn = types.FunctionType(code, globals_dict, name, arg_defs, closure)

        return FunctionTool(
            route_fn,
            description=(
                f"Use this tool if the customer is discussing anything related to: {agent_role}.\n"
                f"This will route the conversation to **{agent_name}**."
            ),
        )
