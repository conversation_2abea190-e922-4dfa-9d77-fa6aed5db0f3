import json
import asyncio
from typing import List
from loguru import logger
from autogen_core import (
    <PERSON><PERSON>Call,
    MessageContext,
    RoutedAgent,
    TopicId,
    message_handler,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    SystemMessage,
)
from autogen_core.tools import Tool
from llm_app.pipeline.steps.agentic_framework.autogen.models.models import UserTask, AgentResponse
from llm_app.pipeline.steps.agentic_framework.autogen.session.conversation_session import ConversationSession


class AIAgent(RoutedAgent):
    def __init__(
        self,
        description: str,
        system_message: SystemMessage,
        model_client: ChatCompletionClient,
        tools: List[Tool],
        delegate_tools: List[Tool],
        agent_topic_type: str,
        user_topic_type: str,
        response_queue: asyncio.Queue[str | object],
    ) -> None:
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        self._tools = dict([(tool["tool_name"], tool) for tool in tools])
        self._tool_schema = [tool["schema"] for tool in tools]
        self._delegate_tools = dict([(tool.name, tool) for tool in delegate_tools])
        self._delegate_tool_schema = [tool.schema for tool in delegate_tools]
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self._response_queue = response_queue

    @message_handler
    async def handle_task(self, message: UserTask, ctx: MessageContext) -> None:
        try:
            # Enhanced logging for Triage Agent V2
            if "Triage_Agent_V2" in self._agent_topic_type:
                print(f"🎯 [TRIAGE_V2_HANDLER] Processing message for {self._agent_topic_type}")
                conversation_messages = await message.conversation_session_context.get_conversation_messages()
                print(f"💬 [TRIAGE_V2_HANDLER] Conversation history: {len(conversation_messages)} messages")

                if conversation_messages:
                    latest_message = conversation_messages[-1]
                    print(f"📝 [TRIAGE_V2_HANDLER] Latest message: {latest_message.content[:100]}...")

                print(f"🔧 [TRIAGE_V2_HANDLER] Available tools: {len(self._tool_schema)} regular + {len(self._delegate_tool_schema)} delegate")
                print(f"🔀 [TRIAGE_V2_HANDLER] Delegate tools: {list(self._delegate_tools.keys())}")

            # Start streaming LLM responses
            final_response = await self._model_client.create(
                messages=[self._system_message]
                + await message.conversation_session_context.get_conversation_messages(),
                tools=self._tool_schema + self._delegate_tool_schema,
                cancellation_token=ctx.cancellation_token,
            )

            # Log response for Triage Agent V2
            if "Triage_Agent_V2" in self._agent_topic_type:
                print(f"🤖 [TRIAGE_V2_HANDLER] LLM response received")
                print(f"💭 [TRIAGE_V2_HANDLER] Thought: {final_response.thought[:100] if final_response.thought else 'None'}...")
                print(f"📄 [TRIAGE_V2_HANDLER] Content type: {type(final_response.content)}")
                if isinstance(final_response.content, str):
                    print(f"💬 [TRIAGE_V2_HANDLER] Response text: {final_response.content[:150]}...")
                elif isinstance(final_response.content, list):
                    print(f"🔧 [TRIAGE_V2_HANDLER] Function calls: {len(final_response.content)}")
                    for call in final_response.content:
                        if hasattr(call, 'name'):
                            print(f"   🎯 [TRIAGE_V2_HANDLER] Calling: {call.name}")

            # Process function/tool calls if present
            if final_response.thought:
                await self._response_queue.put(
                    {
                        "response_type": "text",
                        "text": final_response.thought,
                        "template": None,
                        "partial_response": True,
                        "should_end_interaction": False,
                        "is_forced_text_template": False,
                        "source": self._agent_topic_type,
                    }
                )
            while isinstance(final_response.content, list) and all(
                isinstance(m, FunctionCall) for m in final_response.content
            ):
                tool_call_results = []
                delegate_targets = []

                for call in final_response.content:
                    arguments = json.loads(call.arguments)
                    arguments["context_arguments"] = message.context_arguments
                    if call.name in self._tools:
                        result = await self._tools[call.name]["tool"].run_json(arguments, ctx.cancellation_token)
                        result_str = json.dumps(result)

                        tool_call_results.append(
                            FunctionExecutionResult(call_id=call.id, content=result_str, is_error=False, name=call.name)
                        )
                    elif call.name in self._delegate_tools:
                        result = await self._delegate_tools[call.name].run_json(arguments, ctx.cancellation_token)
                        topic_type = self._delegate_tools[call.name].return_value_as_string(result)

                        await message.conversation_session_context.add_message(
                            AssistantMessage(content=[call], source=self.id.type)
                        )
                        await message.conversation_session_context.add_message(
                            FunctionExecutionResultMessage(
                                content=[
                                    FunctionExecutionResult(
                                        call_id=call.id,
                                        content=f"Transferred to {topic_type}. Adopt persona immediately.",
                                        is_error=False,
                                        name=call.name,
                                    )
                                ]
                            )
                        )
                        delegate_targets.append(
                            (
                                topic_type,
                                UserTask(
                                    conversation_session_context=message.conversation_session_context,
                                    context_arguments=message.context_arguments,
                                ),
                            )
                        )
                    else:
                        raise Exception(f"Unknown tool: {call.name}")

                # Handle delegation
                if delegate_targets:
                    if "Triage_Agent_V2" in self._agent_topic_type:
                        print(f"🔀 [TRIAGE_V2_HANDLER] Delegating to {len(delegate_targets)} target(s)")
                        for topic_type, task in delegate_targets:
                            print(f"   🎯 [TRIAGE_V2_HANDLER] Routing to: {topic_type}")

                    for topic_type, task in delegate_targets:
                        await self.publish_message(task, topic_id=TopicId(topic_type, source=self.id.key))
                    return  # Done after delegation

                # Handle follow-up call after tool execution
                if tool_call_results:
                    await message.conversation_session_context.add_message(
                        AssistantMessage(content=final_response.content, source=self.id.type)
                    )
                    await message.conversation_session_context.add_message(
                        FunctionExecutionResultMessage(content=tool_call_results)
                    )
                    for tool_call_result in tool_call_results:
                        if self._tools[tool_call_result.name].get("template"):
                            await self._response_queue.put(
                                {
                                    "response_type": "template",
                                    "text": None,
                                    "template": {
                                        "template_name": self._tools[tool_call_result.name].get("template"),
                                        "template_values": json.loads(tool_call_result.content),
                                    },
                                    "partial_response": False,
                                    "should_end_interaction": False,
                                    "is_forced_text_template": False,
                                    "source": self._agent_topic_type,
                                }
                            )

                    final_response = await self._model_client.create(
                        messages=[self._system_message]
                        + await message.conversation_session_context.get_conversation_messages(),
                        tools=self._tool_schema + self._delegate_tool_schema,
                        cancellation_token=ctx.cancellation_token,
                    )
                else:
                    return

            # Final message
            assert isinstance(final_response.content, str)

            if "Triage_Agent_V2" in self._agent_topic_type:
                print(f"✅ [TRIAGE_V2_HANDLER] Sending final response")
                print(f"💬 [TRIAGE_V2_HANDLER] Final message: {final_response.content[:200]}...")
                print(f"📤 [TRIAGE_V2_HANDLER] Publishing to user topic: {self._user_topic_type}")

            await message.conversation_session_context.add_message(
                AssistantMessage(content=final_response.content, source=self.id.type)
            )
            await self.publish_message(
                AgentResponse(content=final_response.content, reply_to_topic_type=self._agent_topic_type),
                topic_id=TopicId(self._user_topic_type, source=self.id.key),
            )

            await self._response_queue.put(
                {
                    "response_type": "text",
                    "text": final_response.content,
                    "template": None,
                    "partial_response": False,
                    "should_end_interaction": False,
                    "is_forced_text_template": False,
                    "source": self._agent_topic_type,
                }
            )

            if "Triage_Agent_V2" in self._agent_topic_type:
                print(f"🎉 [TRIAGE_V2_HANDLER] Message processing complete!")
        except Exception as exception:
            logger.exception(
                f"unhandled exception in agent {self._agent_topic_type}: {exception}",
                status="FAILURE",
            )
            await self._response_queue.put(
                {
                    "response_type": "error",
                    "error": str(exception),
                    "source": self._agent_topic_type,
                }
            )
