import json
import asyncio
from autogen_core import (
    MessageContext,
    RoutedAgent,
    message_handler,
)

from llm_app.pipeline.steps.agentic_framework.autogen.models.models import AgentResponse


class UserAgent(RoutedAgent):
    def __init__(
        self,
        description: str,
        response_queue: asyncio.Queue[str | object],
        stream_done: object,
    ) -> None:
        super().__init__(description)
        self._response_queue = response_queue
        self._STREAM_DONE = stream_done

    @message_handler
    async def handle_task_result(self, message: AgentResponse, ctx: MessageContext) -> None:
        await self._response_queue.put(self._STREAM_DONE)
