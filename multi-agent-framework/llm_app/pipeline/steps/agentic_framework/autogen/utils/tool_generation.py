from typing import List
from autogen_core.tools import FunctionTool

from llm_app.config.config_store import get_function_config
from llm_app.helpers.restricted_python_helper import return_function


def resolve_tools(function_ids: List[str]):
    tools = []
    for func_name in function_ids:
        func_desc = get_function_config(func_name)
        function = return_function(func_desc)
        function_tool = FunctionTool(function, description=func_desc.get("description"))
        schema = None
        if func_desc.get("custom_schema"):
            schema = func_desc.get("custom_schema")
        else:
            schema = function_tool.schema
        tool_item = {
            "tool_name": function_tool.name,
            "description": func_desc.get("description"),
            "tool": function_tool,
            "schema": schema,
            "reflect_tool_result": func_desc.get("reflect_tool_result", True),
            "template": func_desc.get("template", ""),
        }
        tools.append(tool_item)
    return tools
