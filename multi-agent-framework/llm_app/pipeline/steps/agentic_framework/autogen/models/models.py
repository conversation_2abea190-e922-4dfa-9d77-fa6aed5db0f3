from typing import Dict, Any, Optional
from pydantic import BaseModel, ConfigDict

from llm_app.pipeline.steps.agentic_framework.autogen.session.conversation_session import ConversationSession


class UserTask(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    conversation_session_context: ConversationSession
    context_arguments: Optional[Dict[str, Any]]


class AgentResponse(BaseModel):
    reply_to_topic_type: str
    content: str
