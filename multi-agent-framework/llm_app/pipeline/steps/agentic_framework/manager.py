import asyncio


class ConversationNotFoundError(Exception):
    pass


class LLMConversationManager:
    def __init__(self):
        self.conversations = {}
        self.lock = asyncio.Lock()

    async def get(self, conversation_id):
        async with self.lock:
            if conversation_id not in self.conversations:
                raise ConversationNotFoundError(f"Conversation '{conversation_id}' not found.")
            return self.conversations[conversation_id]

    async def create(self, conversation_id, config, conversation_cls):
        async with self.lock:
            conversation = conversation_cls(config)
            self.conversations[conversation_id] = conversation
            return conversation

    async def remove(self, conversation_id):
        async with self.lock:
            conversation = self.conversations.pop(conversation_id, None)
            if conversation:
                conversation.shutdown()

    async def clear(self):
        async with self.lock:
            for conversation in self.conversations.values():
                conversation.shutdown()
            self.conversations.clear()
