from pydantic import BaseModel, Field
from typing import Literal, Dict, Any, Union, Optional, Annotated


# ---------- Event Templates ----------
class BaseEventTemplate(BaseModel):
    event_type: str


class TextEventTemplate(BaseEventTemplate):
    event_type: Literal["text"]
    text: str

class ContextOptionEventTemplate(BaseEventTemplate):
    event_type: Literal["contextkey"]
    context_label: str


class InitiateEventTemplate(BaseEventTemplate):
    event_type: Literal["initiate"]
    rosters_id: str
    llm_context: Dict[str, Any]
    user_context: Dict[str, Any]


EventTemplate = Annotated[
    Union[TextEventTemplate, InitiateEventTemplate, ContextOptionEventTemplate],
    Field(discriminator="event_type"),
]


# ---------- PipelineEvent ----------
class PipelineEvent(BaseModel):
    conversation_id: str
    event_template: EventTemplate
    raw_event: Optional[Dict[str, Any]] = None
    channel: str
