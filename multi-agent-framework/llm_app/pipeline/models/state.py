from pydantic import BaseModel, Field
from typing import Dict, Any, Literal, Union, Annotated, ClassVar, Optional, List


# ---------- Base ----------
class BasePipelineState(BaseModel):
    state_type: str


# ---------- Step-specific States ----------
class RequestNormalizerState(BasePipelineState):
    state_type: Literal["request_normalizer"] = Field(default="request_normalizer")
    normalized_input: Dict[str, Any]


class LLMProcessorStatePartial(BasePipelineState):
    state_type: Literal["llm_processor_partial"] = Field(default="llm_processor_partial")
    response_type: Literal["text", "template", "knowledge_base_text"]
    text: Optional[str] = None
    template: Optional[Dict] = None
    partial_response: ClassVar[Literal[True]] = True
    should_end_interaction: bool = False
    is_forced_text_template: bool = False
    references: Optional[List[Dict[str, Any]]] = None
    knowledge_base_query: Optional[str] = None


class LLMProcessorStateFinal(BasePipelineState):
    state_type: Literal["llm_processor_final"] = Field(default="llm_processor_final")
    response_type: Literal["text", "template", "knowledge_base_text"]
    text: Optional[str] = None
    template: Optional[Dict] = None
    partial_response: ClassVar[Literal[True]] = False
    should_end_interaction: bool = False
    is_forced_text_template: bool = False
    references: Optional[List[Dict[str, Any]]] = None
    knowledge_base_query: Optional[str] = None


class ResponseBuilderStatePartial(BasePipelineState):
    state_type: Literal["response_builder_partial"] = Field(default="response_builder_partial")
    channel_response: Any
    partial_response: ClassVar[Literal[True]] = True


class ResponseBuilderStateFinal(BasePipelineState):
    state_type: Literal["response_builder_final"] = Field(default="response_builder_final")
    channel_response: Any
    partial_response: ClassVar[Literal[True]] = False


# ---------- Union of all ----------
PipelineState = Annotated[
    Union[
        RequestNormalizerState,
        LLMProcessorStatePartial,
        LLMProcessorStateFinal,
        ResponseBuilderStatePartial,
        ResponseBuilderStateFinal,
    ],
    Field(discriminator="state_type"),
]
