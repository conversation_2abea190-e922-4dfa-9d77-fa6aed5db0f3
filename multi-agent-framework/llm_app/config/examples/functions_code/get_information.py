import json
import requests
from typing_extensions import Annotated
from bs4 import BeautifulSoup
import re

def get_information(
    question: Annotated[str, "question to retrieve answer from knowledge base."], context_arguments: dict = {}
) -> str:

    base_url = "https://api-manager-sandbox.rozie.ai/event-adapter"
    search_url = "/v1/adapters-illuminar"
    enable_enhancement = False

    def return_result(result, flag=True):
        if flag:
            return [clean_text(doc["description"]) for doc in result["data"]["attributes"]["search_results"]]
        else:
            return clean_text(result["data"]["attributes"].get("summary", ""))

    def clean_text(text):
        if not text:
            return ""

        # Convert HTML to plain text
        soup = BeautifulSoup(text, "html.parser")

        # Remove all anchor tags (<a>) to exclude links and their content
        for a in soup.find_all("a"):
            a.decompose()

        text = soup.get_text()
        # Replace multiple newlines with a single newline
        text = re.sub(r"\n+", "\n", text)

        # Strip leading and trailing spaces and newlines
        return text.strip()

    headers = {
        "Content-Type": "application/json",
        "application-id": "application_f7421e21-83c9-4f99-ba9c-f308abd129e1",
        "api-key": "5b5fadd08ddc4295abfa854244cbfbb2"
    }

    params = {"num_of_results": 2}
    params["query"] = question

    response = requests.get(f"{base_url}{search_url}", params=params, headers=headers)
    if response.status_code == 200:
        search_result = response.json()

        if enable_enhancement:
            print(search_result)
            enhancement_id = search_result["data"]["attributes"].get("enhancement_req_id")
            print(enhancement_id)
            if enhancement_id:
                # Fetch enhancements if an enhancement ID is present
                enhancement_url = f"{base_url}{search_url}/get-enhancements-by-id"
                enhancement_params = {"req_id": enhancement_id}
                enhancement_response = requests.get(
                    enhancement_url, headers=headers, params=enhancement_params, timeout=30
                )
                if enhancement_response.status_code == 200:
                    print(enhancement_response.json())
                    return return_result(enhancement_response.json(), False)
                else:
                    return return_result(search_result)
            else:
                return return_result(search_result)
        else:
            return return_result(search_result)
    return return_result(search_result)


print(get_information(question="what are the working hours of Prospect heights"))