import json
import requests
from typing_extensions import Annotated


def get_available_meal_services(
    flight_id: Annotated[str, "Alphanumeric five character long id with no spaces."],
    seat_class: Annotated[str, "Complete class name of the seat for which meal options should be looked up. Class name should be same as fetched from pnr details."],
    context_arguments: dict = {},
) -> str:
    query_param = {
        "flight_id": flight_id,
        "seat_class": seat_class
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/flight/get-meal-options"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code not in [200, 500]:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

print(get_available_meal_services("FL124", "Economy"))