import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def rozieair_create_flight_order(
    selected_offered_flight_number: Annotated[int, "The index of selected flight from offered flight option. valid options 1, 2 or 3."],
    context_arguments: dict = {}
):
    """Create a flight order using a selected flight offer and traveler details.

    This function combines the selected flight offer data and traveler details into a payload
    that is sent to the flight order API endpoint. The payload must include:
    - flightOffers: A list containing the selected flight offer data.
    - travelers: A list of traveler details, each including id, dateOfBirth, gender, name, and contact information.
    The flight offer data and traveler details are retrieved from the context arguments.

    Parameters:
        selected_offered_flight_number (int): An integer index representing the flight option selected by customer

    Returns:
        The JSON response from the API, or an error message if the request fails.
    """
    try:
        flight_offer_data = context_arguments.get("flight_offers")[selected_offered_flight_number-1]
        traveler_data = list(context_arguments.get("traveler_details").values())
    except json.JSONDecodeError as e:
        return {"error": f"Invalid JSON input: {str(e)}"}

    formatted_travelers = []
    for t in traveler_data:
        if not all(k in t for k in ("id", "first_name", "last_name", "date_of_birth", "gender", "email", "phone_number")):
            return {"error": "Invalid traveler data: missing required fields in traveler unknown"}

        formatted = {
            "id": str(t["id"]),
            "dateOfBirth": t["date_of_birth"],
            "gender": t["gender"],
            "name": {
                "firstName": t["first_name"].upper(),
                "lastName": t["last_name"].upper()
            },
            "contact": {
                "emailAddress": t["email"],
                "phones": [
                    {
                        "deviceType": "MOBILE",
                        "countryCallingCode": t["country_calling_code"].split(" ")[0].replace("+", ""),
                        "number": t["phone_number"][:10]
                    }
                ]
            },
            "documents": [
                {
                    "documentType": "PASSPORT",
                    "number": t["passport_number"],
                    "expiryDate": t["passport_expiry_date"],
                    "issuanceCountry": t.get("nationality", "IN"),
                    "nationality": t.get("nationality", "IN"),
                    "holder": True
                }
            ]
        }
        formatted_travelers.append(formatted)

    payload = {
        "data": {
            "type": "flight-order",
            "flightOffers": [flight_offer_data],
            "travelers": formatted_travelers
        }
    }
    url = "https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-order"
    headers = {"accept": "application/json", "Content-Type": "application/json"}
    try:
        response = requests.post(url, headers=headers, data=json.dumps(payload))
        status_code = response.status_code
        api_result = response.json()
    except Exception:
        return {"error": "Failed to parse response", "status_code": response.status_code, "text": response.text}

    # If status code is not 200, return error and skip email, HTML, and custom response logic
    if status_code != 200:
        return {
            "error": "Flight booking API failed",
            "status_code": status_code,
            "response": api_result
        }

    # The following logic is only executed if status_code == 200
    # Check for booking reference and send mail only if status code is 200
    data = api_result.get("data", {})
    associated_records = data.get("associatedRecords", [])
    booking_ref = associated_records[0].get("reference") if associated_records else None
    travelers = data.get("travelers", [])
    offers = data.get("flightOffers", [])
    if status_code == 200 and booking_ref:
        # Inline email logic (placeholder)
        print(f"Sending booking confirmation email for reference: {booking_ref}")
        # Implement your email sending logic here

    # Post-process the API response to generate UI card and summary
    data = api_result.get("data", {})
    booking_ref = data.get("associatedRecords", [{}])[0].get("reference", "N/A")
    booking_date = data.get("associatedRecords", [{}])[0].get("creationDate", "N/A")
    queuing_office = data.get("queuingOfficeId", "N/A")
    travelers = data.get("travelers", [])
    offers = data.get("flightOffers", [])
    pricing = offers[0].get("travelerPricings", []) if offers else []
    html = [
        '<div class="container">',
        '<details open>',
        '<summary class="accordion-header">Booking Information</summary>',
        '<div class="accordion-content booking-info">',
        f'<p><span class="label">Booking Reference:</span> {booking_ref}</p>',
        f'<p><span class="label">Booking Date:</span> {booking_date[:10] if booking_date != "N/A" else "N/A"}</p>',
        f'<p><span class="label">Queuing Office ID:</span> {queuing_office}</p>',
        '</div></details>'
    ]
    html.append('<details>')
    html.append('<summary class="accordion-header">Traveler Details</summary>')
    html.append('<div class="accordion-content travelers">')
    for t in travelers:
        name = t.get("name", {})
        contact = t.get("contact", {})
        docs = t.get("documents", [{}])[0]
        html.append('<div class="traveler">')
        html.append(f'<p><span class="label">Name:</span> {name.get("firstName", "N/A")} {name.get("lastName", "N/A")}</p>')
        html.append(f'<p><span class="label">Date of Birth:</span> {t.get("dateOfBirth", "N/A")}</p>')
        html.append(f'<p><span class="label">Gender:</span> {t.get("gender", "N/A").capitalize()}</p>')
        html.append(f'<p><span class="label">Email:</span> {contact.get("emailAddress", "N/A")}</p>')
        phone = contact.get("phones", [{}])[0]
        phone_str = f'+{phone.get("countryCallingCode", "")} {phone.get("number", "")}' if phone else "N/A"
        html.append(f'<p><span class="label">Phone:</span> {phone_str}</p>')
        html.append(f'<p><span class="label">Passport Number:</span> {docs.get("number", "N/A")}</p>')
        html.append(f'<p><span class="label">Passport Expiry:</span> {docs.get("expiryDate", "N/A")}</p>')
        html.append(f'<p><span class="label">Nationality:</span> {docs.get("nationality", "N/A")}</p>')
        html.append('</div><hr>')
    html.append('</div></details>')
    html.append('<details>')
    html.append('<summary class="accordion-header">Flight Itinerary</summary>')
    html.append('<div class="accordion-content itinerary">')
    for offer in offers:
        for itin in offer.get("itineraries", []):
            for seg in itin.get("segments", []):
                dep = seg.get("departure", {})
                arr = seg.get("arrival", {})
                dep_time = dep.get("at", "N/A")
                arr_time = arr.get("at", "N/A")
                try:
                    dep_time_fmt = datetime.fromisoformat(dep_time).strftime("%b %d, %Y at %H:%M")
                except Exception:
                    dep_time_fmt = dep_time
                try:
                    arr_time_fmt = datetime.fromisoformat(arr_time).strftime("%b %d, %Y at %H:%M")
                except Exception:
                    arr_time_fmt = arr_time
                html.append('<div class="flight-segment">')
                html.append(f'<h3>{"Departure" if itin == offer.get("itineraries", [])[0] else "Return"} Flight</h3>')
                html.append(f'<p><span class="label">Flight:</span> {seg.get("carrierCode", "N/A")} {seg.get("number", "N/A")}</p>')
                html.append(f'<p><span class="label">Departure:</span> {dep.get("iataCode", "N/A")} - {dep_time_fmt}</p>')
                html.append(f'<p><span class="label">Arrival:</span> {arr.get("iataCode", "N/A")} - {arr_time_fmt}</p>')
                html.append(f'<p><span class="label">Duration:</span> {seg.get("duration", "N/A").replace("PT", "").replace("H", "h ").replace("M", "m")}</p>')
                html.append(f'<p><span class="label">Aircraft:</span> {seg.get("aircraft", {}).get("code", "N/A")}</p>')
                html.append(f'<p><span class="label">Operating Carrier:</span> {seg.get("operating", {}).get("carrierCode", "N/A")}</p>')
                html.append('</div>')
    html.append('</div></details>')
    html.append('<details>')
    html.append('<summary class="accordion-header">Pricing Summary</summary>')
    html.append('<div class="accordion-content pricing">')
    html.append('<table class="pricing-table"><thead><tr><th>Traveler</th><th>Base Fare</th><th>Taxes</th><th>Total</th></tr></thead><tbody>')
    for p in pricing:
        traveler_id = p.get("travelerId", "N/A")
        traveler = {}
        for t in travelers:
            if t.get("id") == traveler_id:
                traveler = t
                break
        name = traveler.get("name", {})
        base = p.get("price", {}).get("base", "N/A")
        taxes = p.get("price", {}).get("refundableTaxes", "N/A")
        total = p.get("price", {}).get("total", "N/A")
        html.append(f'<tr><td>{name.get("firstName", "N/A")} {name.get("lastName", "N/A")}</td><td>€{base}</td><td>€{taxes}</td><td>€{total}</td></tr>')
    grand_total = offers[0].get("price", {}).get("grandTotal", "N/A") if offers else "N/A"
    html.append(f'</tbody><tfoot><tr><th colspan="3">Grand Total</th><th>€{grand_total}</th></tr></tfoot></table>')
    html.append('</div></details></div>')
    css = """
    <style>
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f6f8; margin: 0; padding: 20px; color: #333; }
    .container { max-width: 800px; margin: auto; background: #fff; border-radius: 8px; padding: 30px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); }
    h1 { text-align: center; color: #854E9F; }
    .accordion-section { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 6px; overflow: hidden; }
    .accordion-header { background-color: #f2f2f2; padding: 12px 20px; cursor: pointer; font-weight: bold; font-size: 1.1em; color: #854E9F; border-bottom: 1px solid #ddd; }
    .accordion-content { padding: 20px; display: block; }
    .booking-info, .traveler, .flight-segment, .pricing { margin-bottom: 15px; }
    .label { font-weight: bold; display: inline-block; width: 150px; color: #555; }
    .flight-segment { border: 1px solid #e1e1e1; border-radius: 5px; padding: 15px; margin-bottom: 15px; background-color: #fafafa; }
    .flight-segment h3 { margin-top: 0; color: #2980b9; }
    .pricing-table { width: 100%; border-collapse: collapse; }
    .pricing-table th, .pricing-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
    .pricing-table th { background-color: #f0f0f0; }
    @media (max-width: 600px) { .label { width: 100%; display: block; margin-bottom: 5px; } }
    </style>
    """
    # Safely extract first_email from formatted_travelers
    first_email = None
    for t in formatted_travelers:
        if t.get("contact", {}).get("emailAddress"):
            first_email = t["contact"]["emailAddress"]
            break
    if not first_email:
        return {"error": "Could not find valid email address in traveler data."}
    text_summary = (
        f"Your booking is confirmed. Reference: {booking_ref}. "
        f"Total price: €{grand_total}. A confirmation email has been sent to {first_email}."
    )

    # Send HTML email using Rozie event adapter API
    full_html = f"<html><head>{css}</head><body>{''.join(html)}</body></html>"

    email_payload = {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": "channel_user_123",
                "id_type": "channel_user_id",
                "id_resource": "webchat"
            }
        },
        "channel": {
            "channel_id": "channel_id_123",
            "channel_name": "webchat",
            "ui_info": {"should_consolidate_buttons": True},
            "reply_info": {"reply_mode": "sync"}
        },
        "incoming_events": [
            {
                "event_id": "event_0",
                "event_creator": {
                    "event_creator_type": "user",
                    "user_id": {
                        "id": "channel_user_0",
                        "id_type": "channel_user_id",
                        "id_resource": "webchat"
                    }
                },
                "event_template": {
                    "event_type": "skillconcept",
                    "skill": {
                        "skill_id": "EmailTest",
                        "usage_recommendation": "exclusive"
                    },
                    "text": None,
                    "concepts": [
                        {
                            "concept_value": {
                                "entity_type": "txEffectiveDate",
                                "entity_object": {"txEffectiveDate": "required"}
                            },
                            "is_checked": True,
                            "is_valid": True
                        },
                        {
                            "concept_value": {
                                "entity_type": "body",
                                "entity_object": {"body": full_html}
                            },
                            "is_checked": True,
                            "is_valid": True
                        },
                        {
                            "concept_value": {
                                "entity_type": "email",
                                "entity_object": {"email": first_email}
                            },
                            "is_checked": True,
                            "is_valid": True
                        }
                    ],
                    "event_metadata": {}
                }
            }
        ]
    }

    requests.post(
        "https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event",
        headers={
            "application-id": "application_97a3285b-e8c9-42bf-a924-d713272becaf",
            "api-key": "5b5fadd08ddc4295abfa854244cbfbb2",
            "Content-Type": "application/json"
        },
        data=json.dumps(email_payload)
    )

    requests.post(
        "http://rozie-air.dev-scc-demo.rozie.ai/pnr/add_pnr_details",
        headers={
            "Content-Type": "application/json",
            "access_token": "Test@123"
        },
        data=json.dumps(api_result)
    )

    return {
        "llm_result": api_result,
        "custom_results": [
            {"response_type": "text", "text": text_summary},
            {"response_type": "html", "cardName": "flight-booking-confirmation", "body": "".join(html), "css": css}
        ]
    }

# Example usage
if __name__ == "__main__":
    flight_offer_data_str = '{"type":"flight-offer","id":"1","source":"GDS","instantTicketingRequired":false,"nonHomogeneous":false,"paymentCardRequired":false,"lastTicketingDate":"2025-05-23","itineraries":[{"segments":[{"departure":{"iataCode":"CDG","at":"2025-05-23T15:25:00"},"arrival":{"iataCode":"HEL","at":"2025-05-23T19:20:00"},"carrierCode":"6X","number":"3618","aircraft":{"code":"733"},"operating":{"carrierCode":"6X"},"duration":"PT2H55M","id":"3","numberOfStops":0,"co2Emissions":[{"weight":175,"weightUnit":"KG","cabin":"ECONOMY"}]},{"departure":{"iataCode":"HEL","at":"2025-05-24T17:30:00"},"arrival":{"iataCode":"ICN","at":"2025-05-25T08:20:00"},"carrierCode":"6X","number":"3605","aircraft":{"code":"733"},"operating":{"carrierCode":"6X"},"duration":"PT8H50M","id":"4","numberOfStops":0,"co2Emissions":[{"weight":295,"weightUnit":"KG","cabin":"ECONOMY"}]}]},{"segments":[{"departure":{"iataCode":"ICN","at":"2025-06-06T09:53:00"},"arrival":{"iataCode":"CDG","at":"2025-06-06T14:10:00"},"carrierCode":"6X","number":"957","aircraft":{"code":"332"},"operating":{"carrierCode":"6X"},"duration":"PT11H17M","id":"7","numberOfStops":0,"co2Emissions":[{"weight":380,"weightUnit":"KG","cabin":"ECONOMY"}]}]}],"price":{"currency":"EUR","total":"446.40","base":"250.00","fees":[{"amount":"0.00","type":"SUPPLIER"},{"amount":"0.00","type":"TICKETING"},{"amount":"0.00","type":"FORM_OF_PAYMENT"}],"grandTotal":"446.40","billingCurrency":"EUR"},"pricingOptions":{"fareType":["PUBLISHED"],"includedCheckedBagsOnly":true},"validatingAirlineCodes":["6X"],"travelerPricings":[{"travelerId":"1","fareOption":"STANDARD","travelerType":"ADULT","price":{"currency":"EUR","total":"223.20","base":"125.00","taxes":[{"amount":"40.00","code":"O4"},{"amount":"5.83","code":"WL"},{"amount":"13.95","code":"QX"},{"amount":"22.30","code":"FR"},{"amount":"0.90","code":"XU"},{"amount":"15.22","code":"BP"}],"refundableTaxes":"98.20"},"fareDetailsBySegment":[{"segmentId":"3","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}},{"segmentId":"4","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}},{"segmentId":"7","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}}]},{"travelerId":"2","fareOption":"STANDARD","travelerType":"ADULT","price":{"currency":"EUR","total":"223.20","base":"125.00","taxes":[{"amount":"40.00","code":"O4"},{"amount":"5.83","code":"WL"},{"amount":"13.95","code":"QX"},{"amount":"22.30","code":"FR"},{"amount":"0.90","code":"XU"},{"amount":"15.22","code":"BP"}],"refundableTaxes":"98.20"},"fareDetailsBySegment":[{"segmentId":"3","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}},{"segmentId":"4","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}},{"segmentId":"7","cabin":"ECONOMY","fareBasis":"YCNV1","class":"Y","includedCheckedBags":{"quantity":1}}]}]}'
    traveler_data_str = '[{"traveler_id": "1", "date_of_birth": "1982-01-16", "gender": "MALE", "name": "JORGE GONZALES", "email": "<EMAIL>", "phone": "+34 *********"}, {"traveler_id": "2", "date_of_birth": "2012-10-11", "gender": "FEMALE", "name": "ADRIANA GONZALES", "email": "<EMAIL>", "phone": "+34 *********"}]'
    result = rozieair_create_flight_order(2, {
    "chat_id": "conversation_0d8b41e8-6376-495b-9ca9-142e1960976d",
    "flight_offers": [
        {
            "id": "1",
            "type": "flight-offer",
            "price": {
                "base": "596.00",
                "fees": [
                    {
                        "type": "SUPPLIER",
                        "amount": "0.00"
                    },
                    {
                        "type": "TICKETING",
                        "amount": "0.00"
                    }
                ],
                "total": "943.84",
                "currency": "EUR",
                "grandTotal": "943.84",
                "additionalServices": [
                    {
                        "type": "CHECKED_BAGS",
                        "amount": "90.00"
                    }
                ]
            },
            "oneWay": False,
            "source": "GDS",
            "itineraries": [
                {
                    "duration": "PT10H5M",
                    "segments": [
                        {
                            "id": "7",
                            "number": "602",
                            "arrival": {
                                "at": "2025-05-16T06:20:00",
                                "iataCode": "KEF"
                            },
                            "aircraft": {
                                "code": "7M8"
                            },
                            "duration": "PT5H30M",
                            "departure": {
                                "at": "2025-05-15T20:50:00",
                                "iataCode": "YYZ",
                                "terminal": "3"
                            },
                            "operating": {
                                "carrierCode": "FI"
                            },
                            "carrierCode": "FI",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        },
                        {
                            "id": "8",
                            "number": "450",
                            "arrival": {
                                "at": "2025-05-16T11:55:00",
                                "iataCode": "LHR",
                                "terminal": "2"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT3H15M",
                            "departure": {
                                "at": "2025-05-16T07:40:00",
                                "iataCode": "KEF"
                            },
                            "operating": {
                                "carrierCode": "FI"
                            },
                            "carrierCode": "FI",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        }
                    ]
                }
            ],
            "isUpsellOffer": False,
            "nonHomogeneous": False,
            "pricingOptions": {
                "fareType": [
                    "PUBLISHED"
                ],
                "includedCheckedBagsOnly": False
            },
            "travelerPricings": [
                {
                    "price": {
                        "base": "298.00",
                        "total": "471.92",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "1",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "V",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "CHECKED BAG UP TO 23KG",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "ALCOHOLIC DRINK",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "NON ALCOHOLIC DRINK",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "ENTERTAINMENT",
                                    "description": "USB POWER",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "VJ1QCALT",
                            "segmentId": "7",
                            "brandedFare": "LIGHT",
                            "brandedFareLabel": "ECONOMY LIGHT",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 0
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "V",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "CHECKED BAG UP TO 23KG",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "ALCOHOLIC DRINK",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "NON ALCOHOLIC DRINK",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "ENTERTAINMENT",
                                    "description": "USB POWER",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "VJ1QCALT",
                            "segmentId": "8",
                            "brandedFare": "LIGHT",
                            "brandedFareLabel": "ECONOMY LIGHT",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 0
                            }
                        }
                    ]
                },
                {
                    "price": {
                        "base": "298.00",
                        "total": "471.92",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "2",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "V",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "CHECKED BAG UP TO 23KG",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "ALCOHOLIC DRINK",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "NON ALCOHOLIC DRINK",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "ENTERTAINMENT",
                                    "description": "USB POWER",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "VJ1QCALT",
                            "segmentId": "7",
                            "brandedFare": "LIGHT",
                            "brandedFareLabel": "ECONOMY LIGHT",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 0
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "V",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "CHECKED BAG UP TO 23KG",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "ALCOHOLIC DRINK",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "NON ALCOHOLIC DRINK",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "ENTERTAINMENT",
                                    "description": "USB POWER",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "VJ1QCALT",
                            "segmentId": "8",
                            "brandedFare": "LIGHT",
                            "brandedFareLabel": "ECONOMY LIGHT",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 0
                            }
                        }
                    ]
                }
            ],
            "lastTicketingDate": "2025-05-14",
            "lastTicketingDateTime": "2025-05-14",
            "numberOfBookableSeats": 9,
            "validatingAirlineCodes": [
                "FI"
            ],
            "instantTicketingRequired": False
        },
        {
            "id": "2",
            "type": "flight-offer",
            "price": {
                "base": "570.00",
                "fees": [
                    {
                        "type": "SUPPLIER",
                        "amount": "0.00"
                    },
                    {
                        "type": "TICKETING",
                        "amount": "0.00"
                    }
                ],
                "total": "1016.28",
                "currency": "EUR",
                "grandTotal": "1016.28"
            },
            "oneWay": False,
            "source": "GDS",
            "itineraries": [
                {
                    "duration": "PT16H",
                    "segments": [
                        {
                            "id": "1",
                            "number": "322",
                            "arrival": {
                                "at": "2025-05-16T06:40:00",
                                "iataCode": "PDL"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT5H55M",
                            "departure": {
                                "at": "2025-05-15T20:45:00",
                                "iataCode": "YYZ",
                                "terminal": "3"
                            },
                            "operating": {
                                "carrierCode": "S4"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        },
                        {
                            "id": "2",
                            "number": "120",
                            "arrival": {
                                "at": "2025-05-16T11:40:00",
                                "iataCode": "LIS",
                                "terminal": "1"
                            },
                            "aircraft": {
                                "code": "320"
                            },
                            "duration": "PT2H15M",
                            "departure": {
                                "at": "2025-05-16T08:25:00",
                                "iataCode": "PDL"
                            },
                            "operating": {
                                "carrierCode": "S4"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        },
                        {
                            "id": "3",
                            "number": "8250",
                            "arrival": {
                                "at": "2025-05-16T17:45:00",
                                "iataCode": "LHR",
                                "terminal": "2"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT2H45M",
                            "departure": {
                                "at": "2025-05-16T15:00:00",
                                "iataCode": "LIS",
                                "terminal": "1"
                            },
                            "operating": {
                                "carrierCode": "TP"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        }
                    ]
                }
            ],
            "isUpsellOffer": False,
            "nonHomogeneous": False,
            "pricingOptions": {
                "fareType": [
                    "PUBLISHED"
                ],
                "includedCheckedBagsOnly": True
            },
            "travelerPricings": [
                {
                    "price": {
                        "base": "285.00",
                        "total": "508.14",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "1",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "1",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "2",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "3",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        }
                    ]
                },
                {
                    "price": {
                        "base": "285.00",
                        "total": "508.14",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "2",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "1",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "2",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "3",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        }
                    ]
                }
            ],
            "lastTicketingDate": "2025-05-13",
            "lastTicketingDateTime": "2025-05-13",
            "numberOfBookableSeats": 6,
            "validatingAirlineCodes": [
                "S4"
            ],
            "instantTicketingRequired": False
        },
        {
            "id": "3",
            "type": "flight-offer",
            "price": {
                "base": "570.00",
                "fees": [
                    {
                        "type": "SUPPLIER",
                        "amount": "0.00"
                    },
                    {
                        "type": "TICKETING",
                        "amount": "0.00"
                    }
                ],
                "total": "1016.28",
                "currency": "EUR",
                "grandTotal": "1016.28"
            },
            "oneWay": False,
            "source": "GDS",
            "itineraries": [
                {
                    "duration": "PT16H",
                    "segments": [
                        {
                            "id": "4",
                            "number": "322",
                            "arrival": {
                                "at": "2025-05-16T06:40:00",
                                "iataCode": "PDL"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT5H55M",
                            "departure": {
                                "at": "2025-05-15T20:45:00",
                                "iataCode": "YYZ",
                                "terminal": "3"
                            },
                            "operating": {
                                "carrierCode": "S4"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        },
                        {
                            "id": "5",
                            "number": "122",
                            "arrival": {
                                "at": "2025-05-16T13:05:00",
                                "iataCode": "LIS",
                                "terminal": "1"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT2H15M",
                            "departure": {
                                "at": "2025-05-16T09:50:00",
                                "iataCode": "PDL"
                            },
                            "operating": {
                                "carrierCode": "S4"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        },
                        {
                            "id": "6",
                            "number": "8250",
                            "arrival": {
                                "at": "2025-05-16T17:45:00",
                                "iataCode": "LHR",
                                "terminal": "2"
                            },
                            "aircraft": {
                                "code": "32Q"
                            },
                            "duration": "PT2H45M",
                            "departure": {
                                "at": "2025-05-16T15:00:00",
                                "iataCode": "LIS",
                                "terminal": "1"
                            },
                            "operating": {
                                "carrierCode": "TP"
                            },
                            "carrierCode": "S4",
                            "numberOfStops": 0,
                            "blacklistedInEU": False
                        }
                    ]
                }
            ],
            "isUpsellOffer": False,
            "nonHomogeneous": False,
            "pricingOptions": {
                "fareType": [
                    "PUBLISHED"
                ],
                "includedCheckedBagsOnly": True
            },
            "travelerPricings": [
                {
                    "price": {
                        "base": "285.00",
                        "total": "508.14",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "1",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "4",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "5",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "6",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        }
                    ]
                },
                {
                    "price": {
                        "base": "285.00",
                        "total": "508.14",
                        "currency": "EUR"
                    },
                    "fareOption": "STANDARD",
                    "travelerId": "2",
                    "travelerType": "ADULT",
                    "fareDetailsBySegment": [
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "4",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "5",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        },
                        {
                            "cabin": "ECONOMY",
                            "class": "R",
                            "amenities": [
                                {
                                    "amenityType": "BAGGAGE",
                                    "description": "PRE PAID BAGGAGE",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "MEAL",
                                    "description": "MEAL",
                                    "isChargeable": False,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                },
                                {
                                    "amenityType": "BRANDED_FARES",
                                    "description": "BASIC SEAT",
                                    "isChargeable": True,
                                    "amenityProvider": {
                                        "name": "BrandedFare"
                                    }
                                }
                            ],
                            "fareBasis": "R3BSC",
                            "segmentId": "6",
                            "brandedFare": "BSC",
                            "brandedFareLabel": "BASIC",
                            "includedCabinBags": {
                                "quantity": 1
                            },
                            "includedCheckedBags": {
                                "quantity": 1
                            }
                        }
                    ]
                }
            ],
            "lastTicketingDate": "2025-05-13",
            "lastTicketingDateTime": "2025-05-13",
            "numberOfBookableSeats": 6,
            "validatingAirlineCodes": [
                "S4"
            ],
            "instantTicketingRequired": False
        }
    ],
    "traveler_details": {
        "1": {
            "id": "1",
            "email": "<EMAIL>",
            "gender": "MALE",
            "last_name": "Doe",
            "first_name": "Jhon",
            "nationality": "IN",
            "phone_number": "65498654",
            "date_of_birth": "2000-06-14",
            "passport_number": "44949546",
            "country_calling_code": "+91",
            "passport_expiry_date": "2025-05-24"
        },
        "2": {
            "id": "2",
            "email": "<EMAIL>",
            "gender": "MALE",
            "last_name": "Foo",
            "first_name": "Jhon",
            "nationality": "IN",
            "phone_number": "65498654",
            "date_of_birth": "2000-06-14",
            "passport_number": "848475",
            "country_calling_code": "+91",
            "passport_expiry_date": "2025-05-24"
        }
    }
})
    print(json.dumps(result, indent=2))