import json
import requests
from typing_extensions import Annotated

def get_baggage_details(
    customer_last_name: Annotated[str, "The last name of the user/client."],
    pnr_id: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"] = "",
    baggage_tag: Annotated[str, "All alphanumeric ID exactly 11 characters long, no spaces"] = "",
    context_arguments: dict = {}
) -> str:
    """
    This function is a generic POST request handler for retrieving baggage details.
    
    Parameters:
    pnr_id (str): The PNR ID to search for. This parameter is required.
    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.
    
    Returns:
    str: The response from the server, formatted as a JSON string. If the request fails, it returns "Failed" along with the exception message.
    """
    if not pnr_id and not baggage_tag:
        return {
            "status": "Error",
            "suggested_action": "Either `pnr_id` or `baggage_tag` must be provided."
        }
    body = {}
    if pnr_id:
        body = {
            "pnr_id": pnr_id,
            "last_name": customer_last_name,
            "chat_id": context_arguments.get("chat_id")
        }
    else:
        body = {
            "baggage_id": baggage_tag,
            "last_name": customer_last_name,
            "chat_id": context_arguments.get("chat_id")
        }

    url = "https://rozie-air.dev-scc-demo.rozie.ai/baggage/get_baggage_details"
    
    # Set the headers for the request
    headers = {"Content-Type": "application/json","access_token":"Test@123"}
    
    # Prepare parameters for the POST request
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    
    if body:
        param["data"] = json.dumps(body)
        
        try:
            response = requests.post(**param)
            
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        
        except Exception as e:
            return "Failed", str(e)

result = get_baggage_details(baggage_tag="FL127XYZBG1", customer_last_name="Dodo")
print("result",result)