import json
import requests
from typing_extensions import Annotated

def get_policy_details(
        policy_number: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"]
) -> str:
    """
        This function retrieves policy details from the API endpoint using the provided policy number.

        Args:
            policy_number (str): The policy number to fetch details for. It should be a 6-character
                                 alphanumeric string with no spaces.

        Returns:
            str: A JSON-formatted string containing the policy details if the request is successful.
                 If the request fails or an exception occurs, returns "Failed" or a JSON object
                 with the error message.
    """

    url = f"https://rozishrddevadw1webeus.azurewebsites.net/api/data?type=policy&key={policy_number}"
    headers = {
        'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',
        "Content-Type": "application/json",
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return json.dumps(response_json, indent=2)
    except Exception as e:
        return json.dumps({"status": "Failed", "error": str(e)}, indent=2)