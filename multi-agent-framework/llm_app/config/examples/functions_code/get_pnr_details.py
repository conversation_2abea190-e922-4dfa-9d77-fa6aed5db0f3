import json
import requests
from typing_extensions import Annotated

def get_pnr_details(
    booking_reference_id: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"],
    customer_last_name: Annotated[str, "The last name of the user/client."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "pnr_id": booking_reference_id.upper(),
        "customer_last_name": customer_last_name.capitalize()
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

print(get_pnr_details(booking_reference_id="N5Q3EX", customer_last_name="Doe"))