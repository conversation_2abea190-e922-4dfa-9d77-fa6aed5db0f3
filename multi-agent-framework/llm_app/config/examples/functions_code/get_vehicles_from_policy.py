import json
import requests
from typing_extensions import Annotated

def get_vehicles_from_policy(
        policy_number: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"]
) -> str:
    """
        This function fetches all the vehicles insured with the given policy number.

        Args:
            policy_number (str): The policy number for which to fetch the vehicles.

        Returns:
            str: A JSON-formatted string containing the response from the API.
                 If the request fails, returns "Failed". In case of an exception,
                 returns a JSON object with status "Failed" and the error message.
    """

    url = f"https://rozishrddevadw1webeus.azurewebsites.net/api/data?type=vehicle&key={policy_number}"
    headers = {
        'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',
        "Content-Type": "application/json",
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return json.dumps(response_json, indent=2)
    except Exception as e:
        return json.dumps({"status": "Failed", "error": str(e)}, indent=2)