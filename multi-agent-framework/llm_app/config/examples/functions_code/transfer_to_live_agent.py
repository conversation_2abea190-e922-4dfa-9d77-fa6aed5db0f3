from typing_extensions import Annotated


def transfer_to_live_agent(
    context_arguments: dict = {},
) -> str:
   return {
        "llm_result": {
           "status": "Success",
           "message": "Transferring the customer to Live Agent."
        },
        "custom_results": [
            {
                "response_type": "text",
                "text": "Transferring you to a live agent now. Please hold while we connect you.",
            },
            {
                "response_type": "text",
                "text": "Connected to Live agent.",
            }
        ],
    }
