import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def get_flight_status(
    flight_id: Annotated[str, "All Capital alphanumeric ID exactly 5 characters long, no spaces, matching regex: FL\\d{3}[A-Z]"],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "flight_id": flight_id.upper()
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/flight/flight-status"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    result = None
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            result = response.json()
        except Exception as e:
            return "Failed", str(e)
    if result.get("status") == "Success":
        result = result.get("flight_data")
    else:
        return result
    arrival_dt = datetime.fromisoformat(result["arrival_time"][:-6]).strftime("%a, %b %d;%H:%M").split(";")
    departure_dt = datetime.fromisoformat(result["departure_time"][:-6]).strftime("%a, %b %d;%H:%M").split(";")
    bg_color = "#01bc47"
    if result["status"] == "DELAYED":
        bg_color = "#FA9620"
    return {
        "llm_result": result,
        "custom_results": [
            {
                "response_type": "html",
                "cardName": "flight-status-arrived",
                "body": f"<div class=flight-info-tile><div class=subtract><div class=background></div><div class=dashed-sep-link></div><div class=round></div><div class=round1></div></div><div class=man>{result['departure_airport_code']}</div><div class=gva>{result['arrival_airport_code']}</div><div class=card-header-{result['flight_id']}><div class=ontime><div class=div>{result['flight_id']}</div><div class=ontime1>{result['status']}</div></div></div><div class=content><div class=col-1><div class=manchester-wed-nov>{result['departure_city']} {departure_dt[0]}</div><div class=col-stack><div class=col-11><div class=departure>Departure</div><div class=div1>{departure_dt[1]}</div></div><div class=col-11><div class=departure>Terminal</div><div class=div1>1</div></div><div class=col-11><div class=departure>Gate</div><div class=div1>B22</div></div></div></div><div class=v-line></div><div class=col-1><div class=manchester-wed-nov>{result['arrival_city']} {arrival_dt[0]}</div><div class=col-stack><div class=col-11><div class=departure>Arrival</div><div class=div1>{arrival_dt[1]}</div></div><div class=col-11><div class=departure>Terminal</div><div class=div1>2</div></div><div class=col-11><div class=departure>Gate</div><div class=div1>A12</div></div></div></div></div><div class=duration><div class=departure>{result['duration']}</div></div></div>",
                "css": "<style>."+f"card-header-{result['flight_id']}"+",.div,.ontime{display:flex}.col-1,.col-stack,.v-line{align-self:stretch}."+f"card-header-{result['flight_id']}"+",.dashed-sep-link,.ontime-child,.v-line{box-sizing:border-box}.background{position:absolute;top:-845px;left:3990px;border-radius:10px;background-color:#fff;box-shadow:0 2px 4px rgba(0,0,0,.08);width:320px;height:300px}.round,.round1{top:-739px;border-radius:10000px;width:25px;height:25px;background-color:#fff;box-shadow:0 2px 4px rgba(0,0,0,.08);position:absolute}.round{left:4297px}.round1{left:3978px}.subtract{position:relative;background-color:#fff;box-shadow:0 2px 4px rgba(0,0,0,.08);width:320px;height:315px;border-radius:10px}.dashed-sep-link{position:absolute;top:117.5px;left:calc(50% - 135.5px);border-top:1px dashed #cfdae6;width:276px;height:1px}.gva,.man{position:absolute;top:82px;line-height:23px}.man{left:25px}.gva{right:20px}.ezy2185{flex:1;position:relative;letter-spacing:-.01em;line-height:24px}.div,.ontime1{position:relative;letter-spacing:.01em;line-height:16px;text-transform:uppercase;font-weight:600}.div{align-items:center;width:auto;flex-shrink:0}.ontime-child{position:relative;border-right:.5px solid #fff;width:.5px;height:10.5px}."+f"card-header-{result['flight_id']}"+",.flight-icon,.h-line-icon{position:absolute}.ontime{flex-direction:row;align-items:center;justify-content:space-between;gap:10px;text-align:right;font-size:14px;display:flex;font-weight:400}."+f"card-header-{result['flight_id']}"+"{top:0;left:0;border-radius:10px 10px 0 0;background-color:"+bg_color+";width:320px;flex-direction:row;align-items:center;justify-content:flex-start;padding:15px 25px;gap:20px;font-size:14px;color:#fff}.col-1,.col-11{display:flex;flex-direction:column}.h-line-icon{top:93px;left:103px;width:120px;height:2px}.flight-icon{top:80px;left:99px;width:28px;height:28px}.departure,.div1,.v-line{position:relative}.manchester-wed-nov{align-self:stretch;position:relative;line-height:22px;font-weight:500}.div1{font-size:20px;line-height:23px;color:#1e2225}.col-11{align-items:flex-start;justify-content:flex-start;gap:3px}.col-stack{display:flex;flex-direction:row;align-items:flex-start;justify-content:space-between;gap:50px;font-size:12px;color:#8595a6}.col-1{align-items:flex-start;justify-content:flex-start;gap:4px}.v-line{border-top:.5px solid #e1e5ea;height:.5px}.content,.duration{position:absolute;display:flex}.content{top:130px;left:25px;width:275px;flex-direction:column;align-items:center;justify-content:flex-start;gap:9px;font-size:14px}.duration{top:78px;left:129px;border-radius:100px;background-color:#fff;border:1px solid #534ad8;flex-direction:row;align-items:center;justify-content:center;padding:3px 10px;text-align:center;font-size:12px;color:#534ad8}.flight-info-tile{position:relative;width:100%;height:300px;text-align:left;font-size:20px;color:#1e2225;font-family:Lexend}</style>",
            }
        ],
    }

print(get_flight_status(flight_id="FL124"))