import json
import requests
from typing_extensions import Annotated


def flight_check_in(
    booking_reference_id: Annotated[
        str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"
    ],
    seat_number: Annotated[
        str, "Seat number of passenger whose checking-in into the flight"
    ],
    context_arguments: dict = {},
) -> str:
    """This will get the list of customer ids"""
    query_param = {"pnr_id": booking_reference_id}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    pnr_data = {}
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            pnr_data = response_json[0]
        except Exception as e:
            return "Failed", str(e)

    for passenger in pnr_data["passengers"]:
        if passenger["seat"] == seat_number:
            passenger["checked_in"] = True

    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/update_pnr"
    param = {"url": url, "timeout": 30, "headers": headers, "data": json.dumps(pnr_data)}
    try:
        response = requests.post(**param)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return response_json
    except Exception as e:
        return "Failed", str(e)

# print(flight_check_in("FL124PQR", "Mark", "Jane"))