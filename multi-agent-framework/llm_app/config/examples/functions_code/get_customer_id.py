import json
import requests
from typing_extensions import Annotated

def get_customer_id(
    last_name: Annotated[str, "The last name of the user/client. This is a required field and must be provided."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "last_name": last_name
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/customer/get_customer_id"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

