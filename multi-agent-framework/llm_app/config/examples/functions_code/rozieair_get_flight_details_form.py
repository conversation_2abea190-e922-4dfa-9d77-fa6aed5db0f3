import json

def rozieair_get_flight_details_form(context_arguments: dict = {}) -> dict:
    """Return a flight details form."""
    return {
        "llm_result": {
            "next_action": "ask customer Carrier Code, Flight Number and Scheduled Departure Date."
        },
        "custom_results": [
            {
                "response_type": "html",
                "cardName": "flight-details-form",
                "body": """
<div class="flight-form-container">
  <form onsubmit="
    event.preventDefault();
    const f = this;
    const data = {
      carrierCode: f.carrierCode.value.toUpperCase(),
      flightNumber: f.flightNumber.value,
      scheduledDepartureDate: f.scheduledDepartureDate.value
    };

    const msg =
      'Flight details submitted.\\n' +
      'Carrier Code: ' + data.carrierCode + '\\n' +
      'Flight Number: ' + data.flightNumber + '\\n' +
      'Scheduled Departure: ' + data.scheduledDepartureDate;

    const shell = document.querySelector('rozieai-webchat')?.shadowRoot
      ?.querySelector('webchat-shell')?.shadowRoot;
    const input = shell?.querySelector('textarea.user-input');
    const button = shell?.querySelector('button.submit-btn');
    if (!input || !button) return console.error('❌ Input or button not found');
    input.value = msg;
    input.dispatchEvent(new Event('input', { bubbles: true }));
    setTimeout(() => button.click(), 200);
  ">
    <label>Carrier Code (2 uppercase letters)
      <input type="text" name="carrierCode" placeholder="e.g., AA" pattern="[A-Z]{2}" required>
    </label>
    <label>Flight Number (1-4 digits)
      <input type="text" name="flightNumber" placeholder="e.g., 1234" required>
    </label>
    <label>Scheduled Departure Date
      <input type="date" name="scheduledDepartureDate" required>
    </label>
    <button type="submit">Submit</button>
  </form>
</div>
""",
                "css": """
body {
  background: linear-gradient(to bottom, #f4f7fa, #fcfbfd);
  font-family: 'Segoe UI', sans-serif;
  margin: 0;
  padding: 24px;
}

.flight-form-container {
  padding: 24px;
  max-width: 400px;
  margin: auto;
  background: linear-gradient(to bottom, #ffffff, #f6f3fa);
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid #eee;
}

.flight-form-container h3 {
  font-size: 20px;
  margin-bottom: 16px;
  color: #2e2e2e;
  text-align: center;
}

.flight-form-container label {
  display: block;
  margin-bottom: 14px;
  font-size: 14px;
  color: #444;
}

.flight-form-container input {
  width: 100%;
  padding: 10px 12px;
  margin-top: 6px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-sizing: border-box;
  background-color: #fff;
}

.flight-form-container button {
  margin-top: 20px;
  padding: 12px;
  width: 100%;
  background-color: #7a3fa5;
  color: white;
  border: none;
  font-size: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  font-weight: 600;
}

.flight-form-container button:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}
"""
            }
        ]
    }

# Example usage
if __name__ == "__main__":
    result = rozieair_get_flight_details_form()
    print(json.dumps(result, indent=2))