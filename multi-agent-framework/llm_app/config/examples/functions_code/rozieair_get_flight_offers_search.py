import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def rozieair_get_flight_offers_search(
    origin: Annotated[str, "Origin airport code (e.g., PAR)"],
    destination: Annotated[str, "Destination airport code (e.g., ICN)"],
    departure_date: Annotated[str, "Departure date (YYYY-MM-DD)"],
    return_date: Annotated[str, "Return date for round trips (YYYY-MM-DD)"] = None,
    adults: Annotated[int, "Number of adult passengers"] = 1,
    children: Annotated[int, "Number of child passengers"] = 0,
    infants: Annotated[int, "Number of infant passengers"] = 0,
    context_arguments: dict = {}
) -> dict:
    """Search for flight offers using the flight offers search API."""
    query_params = {
        "origin": origin.upper(),
        "destination": destination.upper(),
        "departure_date": departure_date,
        "adults": adults,
        "children": children,
        "infants": infants,
        "max_offers": 3
    }
    
    if return_date:
        query_params["return_date"] = return_date

    url = "https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-offers-search"
    headers = {"accept": "application/json"}
    
    try:
        response = requests.get(url, headers=headers, params=query_params, timeout=30)
        if response.status_code != 200:
            return {
                "llm_result": {"error": f"Flight offer API error: {response.status_code}"},
                "custom_results": []
            }
        
        result = response.json()
        
        # Generate HTML content
        html_parts = ['<div class="container">']
        for idx, offer in enumerate(result.get("data", []), 1):
            price = offer.get("price", {}).get("total", "0.00")
            currency = offer.get("price", {}).get("currency", "EUR")
            itineraries = offer.get("itineraries", [])
            if not itineraries:
                continue
            outbound = itineraries[0]
            return_trip = itineraries[1] if len(itineraries) > 1 else None
            origin_code = outbound["segments"][0].get("departure", {}).get("iataCode", "")
            dest_code = outbound["segments"][-1].get("arrival", {}).get("iataCode", "")
            outbound_duration = outbound.get("duration", "PT0H0M").replace("PT", "").replace("H", "h ").replace("M", "m")
            return_duration = return_trip.get("duration", "PT0H0M").replace("PT", "").replace("H", "h ").replace("M", "m") if return_trip else ""
            stops_count = max(len(outbound.get("segments", [])) - 1, 0)
            stops_str = f"{stops_count} Stop" + ("" if stops_count == 1 else "s")
            # Accordion summary
            html_parts.append(f'<details{" open" if idx == 1 else ""}>')
            html_parts.append(f'<summary class="accordion">')
            html_parts.append(f'<div class="label">#{idx}</div>')
            html_parts.append(f'<div class="summary-info">')
            html_parts.append(f'<span><strong>{origin_code} → {dest_code}</strong> • {outbound_duration}</span>')
            if return_trip:
                html_parts.append(f'<span>Return: {return_duration}</span>')
            html_parts.append(f'<span>{stops_str}</span>')
            html_parts.append(f'</div>')
            html_parts.append(f'<div class="price">{currency} {price}</div>')
            html_parts.append(f'<div class="arrow">{"▲" if idx == 1 else "▼"}</div>')
            html_parts.append(f'</summary>')
            # Panel
            html_parts.append(f'<div class="panel">')
            html_parts.append('<div class="panel-content">')

            html_parts.append('<div class="segment-wrapper">')
            html_parts.append('<div class="segment-container">')
            for segment in outbound.get("segments", []):
                departure = segment.get("departure", {})
                arrival = segment.get("arrival", {})
                try:
                    departure_time = datetime.fromisoformat(departure.get("at", "")).strftime("%b %d, %Y – %H:%M")
                except Exception:
                    departure_time = departure.get("at", "") or "N/A"
                try:
                    arrival_time = datetime.fromisoformat(arrival.get("at", "")).strftime("%b %d, %Y – %H:%M")
                except Exception:
                    arrival_time = arrival.get("at", "") or "N/A"
                flight_number = f"{segment.get('carrierCode', '')} {segment.get('number', '')}".strip()
                aircraft = segment.get("aircraft", {}).get("code", "")
                html_parts.append('<div class="timeline-item">')
                html_parts.append('<div class="timeline-circle"></div>')
                html_parts.append(f'<div class="timeline-location">{departure.get("iataCode", "")}</div>')
                html_parts.append(f'<div class="timeline-time">{departure_time}</div>')
                html_parts.append('</div>')
                html_parts.append('<div class="timeline-connector">')
                html_parts.append('<div class="timeline-dots"></div>')
                html_parts.append('</div>')
                html_parts.append('<div class="timeline-item">')
                html_parts.append('<div class="timeline-circle"></div>')
                html_parts.append(f'<div class="timeline-location">{arrival.get("iataCode", "")}</div>')
                html_parts.append(f'<div class="timeline-time">{arrival_time}</div>')
                html_parts.append('</div>')
                html_parts.append('<div class="segment-detail">')
                html_parts.append(f'<p>Flight {flight_number} • {aircraft}</p>')
                html_parts.append('</div>')
            html_parts.append('</div></div>')  # Close segment-wrapper

            if return_trip:
                html_parts.append('''
        <div class="separator">
          <div class="dotted-line"></div>
          <div class="return-label">Return Flight</div>
          <div class="dotted-line"></div>
        </div>
      ''')

                html_parts.append('<div class="segment-wrapper">')
                html_parts.append('<div class="segment-container">')
                for segment in return_trip.get("segments", []):
                    departure = segment.get("departure", {})
                    arrival = segment.get("arrival", {})
                    try:
                        departure_time = datetime.fromisoformat(departure.get("at", "")).strftime("%b %d, %Y – %H:%M")
                    except Exception:
                        departure_time = departure.get("at", "") or "N/A"
                    try:
                        arrival_time = datetime.fromisoformat(arrival.get("at", "")).strftime("%b %d, %Y – %H:%M")
                    except Exception:
                        arrival_time = arrival.get("at", "") or "N/A"
                    flight_number = f"{segment.get('carrierCode', '')} {segment.get('number', '')}".strip()
                    aircraft = segment.get("aircraft", {}).get("code", "")
                    html_parts.append('<div class="timeline-item">')
                    html_parts.append('<div class="timeline-circle"></div>')
                    html_parts.append(f'<div class="timeline-location">{departure.get("iataCode", "")}</div>')
                    html_parts.append(f'<div class="timeline-time">{departure_time}</div>')
                    html_parts.append('</div>')
                    html_parts.append('<div class="timeline-connector">')
                    html_parts.append('<div class="timeline-dots"></div>')
                    html_parts.append('</div>')
                    html_parts.append('<div class="timeline-item">')
                    html_parts.append('<div class="timeline-circle"></div>')
                    html_parts.append(f'<div class="timeline-location">{arrival.get("iataCode", "")}</div>')
                    html_parts.append(f'<div class="timeline-time">{arrival_time}</div>')
                    html_parts.append('</div>')
                    html_parts.append('<div class="segment-detail">')
                    html_parts.append(f'<p>Flight {flight_number} • {aircraft}</p>')
                    html_parts.append('</div>')
                html_parts.append('</div></div>')  # Close segment-wrapper

            offer_id = offer.get("id", "")
            html_parts.append(f'''
    <div class="select-button-wrapper">
      <button type="button" onclick="
        const msg = 'Flight selected {offer_id}';
        const shell = document.querySelector('rozieai-webchat')?.shadowRoot
          ?.querySelector('webchat-shell')?.shadowRoot;
        const input = shell?.querySelector('textarea.user-input');
        const button = shell?.querySelector('button.submit-btn');
        if (!input || !button) {{
          alert('Simulated message: ' + msg);
          return console.warn('RozieAI input or button not found. Running in test mode.');
        }}
        input.value = msg;
        input.dispatchEvent(new Event('input', {{ bubbles: true }}));
        setTimeout(() => button.click(), 200);
      ">Select Flight</button>
    </div>
  ''')
            html_parts.append('</div>')
            html_parts.append('</div>')
            html_parts.append('</details>')
        html_parts.append('</div>')
        # CSS from template
        css = """
    <style>
    body {
      margin: 0;
      background-color: #f9f9f9;
      font-family: 'Segoe UI', sans-serif;
      font-size: 12px;
      color: #333;
    }
    .container {
      max-width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    summary.accordion {
      background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);
      color: white;
      cursor: pointer;
      padding: 10px 12px;
      border-radius: 6px;
      font-size: 12px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
    }
    summary::-webkit-details-marker {
      display: none;
    }
    summary::marker {
      content: "";
    }
    details[open] summary.accordion {
      background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);
    }
    .label {
      font-weight: bold;
      background-color: #fff;
      color: #834C9D;
      border-radius: 4px;
      padding: 1px 5px;
      font-size: 11px;
      margin-right: 8px;
    }
    .summary-info {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      flex: 1;
    }
    .summary-info span {
      font-size: 11px;
      white-space: nowrap;
    }
    .price {
      font-size: 12px;
      color: #b8ffcc;
      font-weight: bold;
      margin-left: auto;
    }
    .arrow {
      font-size: 10px;
      margin-left: 6px;
      transform: rotate(0deg);
      transition: transform 0.2s ease;
    }
    details[open] .arrow {
      transform: rotate(180deg);
    }
    .panel {
      background: #f0f0f8;
      border: 1px solid #ccc;
      border-top: none;
      border-radius: 0 0 6px 6px;
      padding: 10px;
      margin-top: -4px;
    }
    .panel-content {
      display: flex;
      flex-direction: column;
    }
    .segment-wrapper {
      margin-bottom: 16px;
    }
    .segment-container {
      flex: 1;
    }
    /* Removed old .segment styles */
    .separator {
      display: flex;
      align-items: center;
      margin: 12px 0;
    }
    .dotted-line {
      flex-grow: 1;
      border-bottom: 1px dotted #aaa;
    }
    .return-label {
      padding: 0 8px;
      font-size: 11px;
      color: #555;
      font-weight: bold;
    }
    .select-button-wrapper {
      text-align: center;
      margin-top: 20px;
    }
    .select-button-wrapper button {
      background: #534ad8;
      color: white;
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
    .timeline-item {
      display: flex;
      align-items: center;
      margin: 6px 0;
      gap: 6px;
    }
    .timeline-circle {
      width: 10px;
      height: 10px;
      border: 2px solid #777;
      border-radius: 50%;
      background: white;
    }
    .timeline-connector {
      margin-left: 3px;
    }
    .timeline-dots {
      border-left: 2px dotted #777;
      height: 24px;
      margin: 4px 0;
    }
    .timeline-time, .timeline-location {
      font-size: 13px;
      color: #333;
      font-weight: 500;
    }
    .segment-detail {
      font-size: 11px;
      margin-bottom: 8px;
    }
  </style>
        """
        
        return {
            "llm_result": result,
            "context_arguments": {
                "flight_offers": result.get("data", [])
            },
            "custom_results": [
                {
                    "response_type": "html",
                    "cardName": "flight-offers",
                    "body": "".join(html_parts),
                    "css": css
                }
            ]
        }
        
    except Exception as e:
        return {
            "llm_result": {"error": str(e)},
            "custom_results": []
        }

# Example usage
if __name__ == "__main__":
    result = rozieair_get_flight_offers_search(
        origin="LON",
        destination="JFK",
        departure_date="2025-05-25",
        return_date="2025-05-30",
        adults=1,
        children=0,
        infants=0
    )
    print(json.dumps(result, indent=2))