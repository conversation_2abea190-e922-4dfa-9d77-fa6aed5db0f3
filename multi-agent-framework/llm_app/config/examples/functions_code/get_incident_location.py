import re
import requests
import json

from typing_extensions import Annotated

def get_incident_location(
        map_string: Annotated[str, "A Google Maps URL, a long URL with coordinates, or a raw lat/lng string"]
):
    """
    If the user provides the incident location in Google Maps URL, a long URL with coordinates,
    or a raw lat/lng string format,this function will extract the latitude and longitude and
    returns the reverse geocoded address using the Nominatim OpenStreetMap API. The function
    handles both short and long URLs, and it can resolve short URLs to their long form. The
    function also includes error handling for network issues and invalid input formats.

    Args:
        map_string: A Google Maps URL, a long URL with coordinates, or a raw lat/lng string.

    Returns:
        A JSON string containing the reverse geocoded address or an error message.
    """

    headers = {
        "User-Agent": "Mozilla/5.0 (compatible; YourAppName/1.0)",
        "Content-Type": "application/json",
    }

    if "maps.app.goo.gl" in map_string:
        try:
            response = requests.get(map_string, headers=headers, allow_redirects=True)
            map_string = response.url
        except Exception as e:
            return f"Failed to resolve short URL: {e}"

    match = re.search(r'@(-?\d+\.\d+),(-?\d+\.\d+)', map_string) or \
            re.match(r'(-?\d+\.\d+),\s*(-?\d+\.\d+)', map_string)

    if not match:
        return "Could not extract coordinates from input."

    lat, lng = match.groups()
    url = f"https://nominatim.openstreetmap.org/reverse?format=json&lat={lat}&lon={lng}&zoom=18&addressdetails=1"

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return json.dumps(response_json, indent=2)
    except Exception as e:
        return json.dumps({"status": "Failed", "error": str(e)}, indent=2)