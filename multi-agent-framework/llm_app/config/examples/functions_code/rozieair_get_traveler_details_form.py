import json

def rozieair_get_traveler_details_form(
    traveler_id: str,
    context_arguments: dict = {}
) -> dict:
    """Return a traveler details form for the given traveler_id."""
    return {
        "llm_result": {
          "traveler_id": traveler_id,
          "next_suggested_action": "Collect traveler details such as first and last name, date of birth, gender, email, number, passport number and its expiry. Invoke `rozieair_store_traveler_details` to validate and store the customer provided traveler details."
        },
        "custom_results": [
            {
                "response_type": "html",
                "cardName": "traveler-details-form",
                "body": f'''
<div class="traveler-form-container">
  <form onsubmit="
    event.preventDefault();
    const f = this;
    const data = {{
      id: '{traveler_id}',
      name: {{ firstName: f.firstName.value, lastName: f.lastName.value }},
      dateOfBirth: f.dateOfBirth.value,
      gender: f.gender.value,
      contact: {{
        emailAddress: f.emailAddress.value,
        phones: [{{
          deviceType: 'MOBILE',
          countryCallingCode: f.countryCallingCode.value,
          number: f.number.value
        }}]
      }},
      nationality: f.nationality.value,
      passport: {{
        number: f.passportNumber.value,
        expiryDate: f.passportExpiryDate.value
      }},
    }};
    const msg =
      'Traveler details submitted.\\n' +
      'Traveler ID: ' + data.id + '\\n' +
      'Name: ' + data.name.firstName + ' ' + data.name.lastName + '\\n' +
      'Date of Birth: ' + data.dateOfBirth + '\\n' +
      'Gender: ' + data.gender + '\\n' +
      'Email: ' + data.contact.emailAddress + '\\n' +
      'Phone: +' + data.contact.phones[0].countryCallingCode + ' ' + data.contact.phones[0].number
      + '\\nNationality: ' + data.nationality
      + '\\nPassport Number: ' + data.passport.number
      + '\\nPassport Expiry: ' + data.passport.expiryDate;

    const shell = document.querySelector('rozieai-webchat')?.shadowRoot
      ?.querySelector('webchat-shell')?.shadowRoot;
    const input = shell?.querySelector('textarea.user-input');
    const button = shell?.querySelector('button.submit-btn');
    if (!input || !button) return console.error('❌ Input or button not found');
    input.value = msg;
    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
    setTimeout(() => button.click(), 200);
  ">

    <h3>Traveler Details (ID: {traveler_id})</h3>

    <label>First Name
      <input type="text" name="firstName" placeholder="e.g., Jorge" required>
    </label>
    <label>Last Name
      <input type="text" name="lastName" placeholder="e.g., Gonzales" required>
    </label>
    <label>Date of Birth
      <input type="date" name="dateOfBirth" required>
    </label>
    <label>Gender
      <select name="gender" required>
        <option value="">Select Gender</option>
        <option value="MALE">Male</option>
        <option value="FEMALE">Female</option>
        <option value="OTHER">Other</option>
      </select>
    </label>
    <label>Email Address
      <input type="email" name="emailAddress" placeholder="e.g., <EMAIL>" required>
    </label>
    <label>Phone Country Code
      <input type="text" name="countryCallingCode" placeholder="e.g., 34" required>
    </label>
    <label>Phone Number
      <input type="text" name="number" placeholder="e.g., *********" required>
    </label>
    <label>Nationality
      <input type="text" name="nationality" placeholder="e.g., IN" required>
    </label>
    <label>Passport Number
      <input type="text" name="passportNumber" placeholder="e.g., 00000000" required>
    </label>
    <label>Passport Expiry Date
      <input type="date" name="passportExpiryDate" required>
    </label>
    <input type="hidden" name="travelerId" value="{traveler_id}">
    <button type="submit">Submit Traveler Info</button>
  </form>
</div>
''',
                "css": """
<style>
  .traveler-form-container {
    font-family: 'Segoe UI', sans-serif;
    padding: 16px;
    max-width: 400px;
    margin: auto;
    background: #f4f7f9;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.08);
  }
  .traveler-form-container h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #333;
  }
  .traveler-form-container label {
    display: block;
    margin-bottom: 12px;
    font-size: 14px;
    color: #555;
  }
  .traveler-form-container input,
  .traveler-form-container select {
    width: 100%;
    padding: 8px;
    margin-top: 4px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
  }
  .traveler-form-container button {
    margin-top: 12px;
    padding: 10px;
    width: 100%;
    background-color: #834C9D;
    color: white;
    border: none;
    font-size: 15px;
    border-radius: 4px;
    cursor: pointer;
  }
  .traveler-form-container button:hover {
    background-color: #6b3d82;
  }
</style>
"""
            }
        ]
    }

# Example usage
if __name__ == "__main__":
    result = rozieair_get_traveler_details_form(
        traveler_id="12345"
    )
    print(json.dumps(result, indent=2))