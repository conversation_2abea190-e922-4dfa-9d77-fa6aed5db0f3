import json
import requests
from typing_extensions import Annotated

def email_prohibited_item_list(
    customer_id: Annotated[str, "Valid customer Id fetched using tool execution."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "customer_id": customer_id
    }
    url = "http://rozie-air.dev-scc-demo.rozie.ai/notification/email/send-prohibited-item-doc-link"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    if query_param:
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return response_json
        except Exception as e:
            return "Failed", str(e)

# print(email_prohibited_item_list("CUST001"))
