import json
import requests
from typing_extensions import Annotated


def pnr_update_wheelchair_assistance_type(
    booking_reference_id: Annotated[
        str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"
    ],
    wheelchair_assistance: Annotated[str, "selected Wheel chair assistance type to add."],
    passenger_first_name: Annotated[str, "first name of the passenger."],
    passenger_last_name: Annotated[str, "last name of the passenger."],
    context_arguments: dict = {},
) -> str:
    """This will get the list of customer ids"""
    query_param = {"pnr_id": booking_reference_id}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    pnr_data = {}
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            pnr_data = response_json[0]
        except Exception as e:
            return "Failed", str(e)

    for passenger in pnr_data["passengers"]:
        if passenger["first_name"] == passenger_first_name and passenger["last_name"] == passenger_last_name:
            passenger["additional_services"].append(wheelchair_assistance)

    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/update_pnr"
    param = {"url": url, "timeout": 30, "headers": headers, "data": json.dumps(pnr_data)}
    try:
        response = requests.post(**param)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        updated_pnr_data = response_json[0] if isinstance(response_json, list) else response_json
    except Exception as e:
        return "Failed", str(e)

    # After the update, fetch the updated PNR if needed
    if isinstance(updated_pnr_data, dict) and "passengers" not in updated_pnr_data:
        url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr"
        headers = {"Content-Type": "application/json", "access_token": "Test@123"}
        param = {"url": url, "timeout": 30, "headers": headers, "params": {"pnr_id": booking_reference_id}}
        try:
            response = requests.get(**param)
            if response.status_code == 200:
                response_json = response.json()
                updated_pnr_data = response_json[0]
            else:
                return "Failed"
        except Exception as e:
            return "Failed"

    # Extract booking details from updated_pnr_data
    booking = updated_pnr_data
    passenger = None
    for p in booking.get("passengers", []):
        if p["first_name"].strip().lower() == passenger_first_name.lower() and \
           p["last_name"].strip().lower() == passenger_last_name.lower():
            passenger = p
            break
    if not passenger:
        return "Failed"
    
    # Get email from passenger details
    email = passenger.get("email")
    flight_segments = booking.get("flight_segments", [])
    user_name = f"{passenger.get('first_name', '')} {passenger.get('last_name', '')}".strip()

    # Get first and last flight segments
    first_segment = flight_segments[0]
    last_segment = flight_segments[-1]
    dep_code = first_segment["departure_airport"]
    arr_code = last_segment["arrival_airport"]
    dep_date = first_segment["departure_time"][:10]  # ISO date
    arr_date = last_segment["arrival_time"][:10]     # ISO date

    # Get airport details (use static fallback for now)
    def get_airport_details(iata_code):
        url = "https://raw.githubusercontent.com/jpatokal/openflights/master/data/airports.dat"
        r = requests.get(url)
        if r.status_code != 200:
            return {"city": iata_code, "country": "", "name": iata_code}
        for line in r.text.splitlines():
            fields = line.split(',')
            if len(fields) >= 6 and fields[4].strip('"').upper() == iata_code.upper():
                return {
                    "name": fields[1].strip('"'),
                    "city": fields[2].strip('"'),
                    "country": fields[3].strip('"'),
                }
        return {"city": iata_code, "country": "", "name": iata_code}

    dep_airport = get_airport_details(dep_code)
    arr_airport = get_airport_details(arr_code)

    # Build the email subject and message (for wheelchair assistance)
    subject = f"Wheelchair Assistance Confirmed | PNR: {booking_reference_id}"
    message = f"""
        This is to confirm that wheelchair assistance (<strong>{wheelchair_assistance}</strong>) has been arranged for your journey. Our ground staff will be ready to support you at departure and arrival points.
    """

    # Generate journey card HTML
    journey_card = f"""
<div class=\"journey-card\">
  <table width=\"100%\" style=\"border-collapse: collapse;\">
    <tr>
      <td align=\"left\" style=\"width: 40%;\">
        <h2 style=\"margin: 0; font-size: 24px;\">{dep_airport['city']}<br>
          <span style=\"font-weight: 600; font-size: 20px;\">{dep_airport['country']}</span></h2>
      </td>
      <td align=\"center\" style=\"width: 20%;\">
        <img src=\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/timeline.png\" alt=\"Timeline\" style=\"max-width: 250px;\" />
      </td>
      <td align=\"right\" style=\"width: 40%;\">
        <h2 style=\"margin: 0; font-size: 24px;\">{arr_airport['city']}<br>
          <span style=\"font-weight: 600; font-size: 20px;\">{arr_airport['country']}</span></h2>
      </td>
    </tr>
    <tr><td colspan=\"3\" height=\"20\"></td></tr>
    <tr>
      <td align=\"left\" style=\"font-size: 15px; line-height: 1.5;\">
        Departure airport: {dep_code}
      </td>
      <td></td>
      <td align=\"right\" style=\"font-size: 15px; line-height: 1.5;\">
        Arrival airport: {arr_code}
      </td>
    </tr>
    <tr><td colspan=\"3\" height=\"30\"></td></tr>
    <tr>
      <td align=\"left\" valign=\"middle\">
        <table>
          <tr>
            <td>
              <img src=\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/calendar.png\" alt=\"Calendar Icon\" style=\"width: 40px;\" />
            </td>
            <td style=\"padding-left: 10px; font-size: 18px;\">{dep_date}</td>
          </tr>
        </table>
      </td>
      <td></td>
      <td align=\"right\" valign=\"middle\">
        <table align=\"right\">
          <tr>
            <td style=\"font-size: 18px; padding-right: 10px;\" align=\"right\">{arr_date}</td>
            <td>
              <img src=\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/clock.png\" alt=\"Clock Icon\" style=\"width: 40px;\" />
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</div>
    """

    # Build final email template
    email_html = f"""
    <!DOCTYPE html>
    <html lang=\"en\">
    <head>
      <meta charset=\"UTF-8\" />
      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>
      <title>RozieAI Concierge Email</title>
      <style>
        body {{
          margin: 0;
          padding: 0;
          background-color: #f5f5f9;
          font-family: 'Segoe UI', Tahoma, sans-serif;
          color: #333333;
        }}
        .email-wrapper {{
          max-width: 700px;
          margin: 0 auto;
          background-color: #f0f0f8;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 4px 24px rgba(83,74,216,0.10);
        }}
        .email-header img {{
          width: 100%;
          height: auto;
          display: block;
        }}
        .email-content {{
          padding: 32px 32px 0 32px;
          background-color: #f0f0f8;
          color: #333333;
        }}
        .email-content p {{
          font-size: 17px;
          line-height: 1.7;
          margin: 18px 0;
          color: #333333;
        }}
        .journey-card {{
          max-width: 700px;
          background-image: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);
          color: white;
          border-radius: 20px;
          padding: 30px;
          font-family: 'Segoe UI', sans-serif;
          margin: 20px 0;
        }}
        .email-footer {{
          text-align: center;
          background-color: #f0f0f8;
          padding: 24px;
          font-size: 14px;
          color: #777777;
          border-bottom-left-radius: 16px;
          border-bottom-right-radius: 16px;
        }}
        .email-footer a {{
          color: #4b2aad;
          text-decoration: none;
        }}
      </style>
    </head>
    <body>
      <div class=\"email-wrapper\">
        <!-- Header Image -->
        <div class=\"email-header\">
          <img src=\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/header.png\" alt=\"RozieAI Header\" />
        </div>

        <!-- Content Section -->
        <div class=\"email-content\">
          <p>Dear {user_name},</p>
          <p>{message}</p>
          {journey_card}
          <p style=\"margin-top:32px;\">Thank you for choosing us.<br>The RozieAI Team</p>
        </div>

        <!-- Footer Section -->
        <div class=\"email-footer\">
          © 2025 RozieAI · <a href=\"https://www.rozieai.com\">www.rozieai.com</a><br />
        </div>
      </div>
    </body>
    </html>
    """

    # Send the email using the API
    api_url = "https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event"
    headers_email = {
        "application-id": "application_9f6d6202-f5d9-445d-b856-82b44844f0d4",
        "api-key": "5b5fadd08ddc4295abfa854244cbfbb2",
        "Content-Type": "application/json"
    }
    import uuid
    event_id = f"event_{uuid.uuid4()}"
    payload = {
        "version": "1.0",
        "user_info": {
            "user_id": {
                "id": f"channel_user_{booking_reference_id}",
                "id_type": "channel_user_id",
                "id_resource": "webchat"
            }
        },
        "channel": {
            "channel_id": f"channel_{booking_reference_id}",
            "channel_name": "webchat",
            "ui_info": {
                "should_consolidate_buttons": True
            },
            "reply_info": {
                "reply_mode": "sync"
            }
        },
        "incoming_events": [
            {
                "event_id": event_id,
                "event_creator": {
                    "event_creator_type": "user",
                    "user_id": {
                        "id": f"channel_user_{booking_reference_id}",
                        "id_type": "channel_user_id",
                        "id_resource": "webchat"
                    }
                },
                "event_template": {
                    "event_type": "skillconcept",
                    "skill": {
                        "skill_id": "RozieAirEmailTemplates",
                        "usage_recommendation": "exclusive"
                    },
                    "text": None,
                    "concepts": [
                        {
                            "concept_value": {
                                "entity_type": "Email",
                                "entity_object": {
                                    "Email": email
                                }
                            },
                            "is_checked": True,
                            "is_valid": True
                        },
                        {
                            "concept_value": {
                                "entity_type": "title",
                                "entity_object": {
                                    "title": subject
                                }
                            },
                            "is_checked": True,
                            "is_valid": True
                        },
                        {
                            "concept_value": {
                                "entity_type": "body",
                                "entity_object": {
                                    "body": email_html
                                }
                            },
                            "is_checked": True,
                            "is_valid": True
                        }
                    ],
                    "event_metadata": {}
                }
            }
        ]
    }
    try:
        response = requests.post(api_url, headers=headers_email, json=payload)
        response.raise_for_status()
    except Exception as e:
        return "Failed"
    return "Success"

if __name__ == "__main__":
    # Example usage for testing
    booking_reference_id = "L9WNFK"
    wheelchair_assistance = "WCHR (Wheelchair - Ramp)"
    passenger_first_name = "Vinaay"
    passenger_last_name = "Sukumar"
    result = pnr_update_wheelchair_assistance_type(
        booking_reference_id=booking_reference_id,
        wheelchair_assistance=wheelchair_assistance,
        passenger_first_name=passenger_first_name,
        passenger_last_name=passenger_last_name,
        context_arguments={}
    )
    print(f"Result: {result}")
