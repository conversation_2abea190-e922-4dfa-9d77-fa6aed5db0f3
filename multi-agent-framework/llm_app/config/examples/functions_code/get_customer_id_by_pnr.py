import json
import requests
from typing_extensions import Annotated

def get_customer_id_by_pnr(
    booking_reference_id: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"],
    context_arguments: dict = {}
)-> str:
    query_param = {
        "pnr_id": booking_reference_id.upper()
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_for_update"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps({
                "customer_id": response_json["customer_id"]
            }, indent=2)
        except Exception as e:
            return "Failed", str(e)
