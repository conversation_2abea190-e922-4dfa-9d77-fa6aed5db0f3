import json
import requests
from typing import List, Dict
from typing_extensions import Annotated

def get_case_details_service(
    case_id: Annotated[str, "The Case ID to search for."],
    context_arguments: dict ={}
) -> str:
    """
    This function is a generic POST request handler for retrieving case details.
    
    Parameters:
    id_input (str): The ID to search for. This can be either a Customer ID or a Case ID. This parameter is required.
    input_type (str): Specifies the type of the input ID. This can be either 'customer_id' or 'case_id'. This parameter is required.
    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.
    
    Returns:
    str: The response from the server, formatted as a JSON string. If the request fails, it returns "Failed" along with the exception message.
    """
    
    body = {
        "id_input": case_id,
        "input_type": "case_id",
        "chat_id": context_arguments.get("chat_id")
    }

    url = "https://rozie-air.dev-scc-demo.rozie.ai/case/get_case_details"
    
    headers = {"Content-Type": "application/json",
               "access_token": "Test@123"}
    
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    
    if body:
        param["data"] = json.dumps(body)
        
        try:
            response = requests.post(**param)
            print("response",response)  
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        
        except Exception as e:
            return "Failed", str(e)

response = get_case_details_service("C250331R212")
print(response)