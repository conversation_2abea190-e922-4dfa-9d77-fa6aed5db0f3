import json
from typing_extensions import Annotated


def rozieair_store_traveler_details(
    traveler_first_name: Annotated[str, "Customer's first name."],
    traveler_last_name: Annotated[str, "Customer's last name."],
    traveler_date_of_birth: Annotated[str, "Customer's date of birth."],
    traveler_gender: Annotated[str, "Customer's gender."],
    traveler_email: Annotated[str, "Customer's email address."],
    traveler_phone_number: Annotated[
        str, "Customer's phone number without country code, only numeric string."
    ],
    traveler_country_calling_code: Annotated[str, "Customer's country calling code."],
    traveler_nationality: Annotated[str, "Customer's nationality."],
    traveler_passport_number: Annotated[str, "Customer's passport number."],
    traveler_passport_expiry_date: Annotated[str, "Customer's passport expiry date."],
    traveler_id: Annotated[str, "Customer's unique identifier."],
    context_arguments: dict = {},
) -> dict:
    """Stores the traveler details in context arguments."""
    traveler_details = {}
    if context_arguments.get("traveler_details"):
        traveler_details = context_arguments["traveler_details"]

    traveler_details[traveler_id] = {
        "first_name": traveler_first_name,
        "last_name": traveler_last_name,
        "date_of_birth": traveler_date_of_birth,
        "gender": traveler_gender,
        "email": traveler_email,
        "phone_number": traveler_phone_number,
        "country_calling_code": traveler_country_calling_code,
        "nationality": traveler_nationality,
        "passport_number": traveler_passport_number,
        "passport_expiry_date": traveler_passport_expiry_date,
        "id": traveler_id,
    }

    return {
        "llm_result": "Traveler details stored successfully.",
        "context_arguments": {
            "traveler_details": traveler_details
        }
    }


# Example usage
if __name__ == "__main__":
    result = rozieair_store_traveler_details(
        traveler_first_name="John",
        traveler_last_name="Doe",
        traveler_date_of_birth="1990-01-01",
        traveler_gender="Male",
        traveler_email="<EMAIL>",
        traveler_phone_number="*********",
        traveler_country_calling_code="+1",
        traveler_nationality="USA",
        traveler_passport_number="AB123456",
        traveler_passport_expiry_date="2025-12-31",
        traveler_id="XXXXXXXXX",
    )
    print(json.dumps(result, indent=2))
