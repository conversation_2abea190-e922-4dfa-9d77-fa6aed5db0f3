import json
import requests
from typing_extensions import Annotated

def email_boarding_pass(
    booking_reference_id: Annotated[str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"],
    customer_id: Annotated[str, "Customer Id."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "pnr_id": booking_reference_id,
        "customer_id": customer_id
    }
    url = "http://rozie-air.dev-scc-demo.rozie.ai/notification/email/send-boarding-pass"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    if query_param:
        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return response_json
        except Exception as e:
            return "Failed", str(e)

# print(email_boarding_pass("FL124ABC", "CUTS001"))
