import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def rozieair_get_flight_status(
    carrier_code: str = "BA",
    flight_number: str = "987",
    scheduled_departure_date: str = "2025-05-30",
    context_arguments: dict = {}
) -> dict:
    """Returns hardcoded flight status information (mocked)."""
    from datetime import datetime

    flight_id = carrier_code + flight_number
    dep_time_raw = "2025-05-30T06:55:00+00:00"
    arr_time_raw = "2025-05-30T09:00:00+00:00"
    duration = "2h 5m"
    status = "ON TIME"

    # Transform datetime strings for template display
    def format_datetime_for_display(iso_string):
        dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        return {
            'date': dt.strftime('%b %d'),  # "May 30"
            'time': dt.strftime('%H:%M')   # "06:55"
        }

    dep_formatted = format_datetime_for_display(dep_time_raw)
    arr_formatted = format_datetime_for_display(arr_time_raw)

    result = {
        "flight_id": flight_id,
        "departure_city": "London",
        "departure_airport_code": "LHR",
        "arrival_city": "Berlin",
        "arrival_airport_code": "BER",
        "departure_time": dep_time_raw,  # Keep original for any other use
        "arrival_time": arr_time_raw,    # Keep original for any other use
        "departure_date": dep_formatted['date'],      # "May 30" - for template
        "departure_time_display": dep_formatted['time'],  # "06:55" - for template
        "arrival_date": arr_formatted['date'],        # "May 30" - for template
        "arrival_time_display": arr_formatted['time'],    # "09:00" - for template
        "duration": duration,
        "status": status
    }

    return result

if __name__ == "__main__":
    result = rozieair_get_flight_status(
        carrier_code="BA",
        flight_number="987",
        scheduled_departure_date="2025-05-05"
    )
    print(json.dumps(result, indent=2))