import json
import requests
from typing_extensions import Annotated


def authentication_validate_otp(
    otp: Annotated[str, "Strictly 6 digit numeric string, provided by customer for verification"],
    context_arguments: dict = {},
) -> str:
    """This will get the list of customer ids"""
    body = {
        "otp": otp,
        "chat_id": context_arguments.get("chat_id")
    }
    url = "http://rozie-air.dev-scc-demo.rozie.ai/authentication/validate_otp"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers}
    if body:
        param["data"] = json.dumps(body)

        try:
            response = requests.post(**param)

            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

print(authentication_validate_otp("639724", {"chat_id": "test"}))