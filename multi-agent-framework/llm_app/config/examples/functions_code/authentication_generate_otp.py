import json
import requests
from typing_extensions import Annotated


def authentication_send_otp(
    customer_id: Annotated[str, "The customer id."] = "",
    booking_reference: Annotated[str, "The booking reference."] = "",
    context_arguments: dict = {},
) -> str:
    """This will get the list of customer ids"""
    if not customer_id and not booking_reference:
        return {
            "status": "Failed",
            "message": "Please provide either customer_id or booking_reference"
        }
    body = {
        "chat_id": context_arguments.get("chat_id"),
        "customer_id": customer_id,
        "pnr": booking_reference
    }
    url = "http://rozie-air.dev-scc-demo.rozie.ai/authentication/generate_otp"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers}
    if body:
        param["data"] = json.dumps(body)

        try:
            response = requests.post(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

print(authentication_send_otp(booking_reference = "LA688Z", context_arguments={"chat_id": "test"}))