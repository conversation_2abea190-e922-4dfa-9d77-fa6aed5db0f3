import requests
import json
from typing import Annotated

def create_zendesk_ticket(
        subject: Annotated[str, "Format : ^FNOL\s[Policy_Number]+-\s[Policy_Holder's_First_Name]+\s[Policy_Holder's_Last_Name]."],
        body: Annotated[str, "Brief summary of the reported incident by the user."],
        policy_holder_email: Annotated[str, "Email address of the policy holder."]
):
    """
    Creates a Zendesk ticket with the specified subject and body. This
    function makes a POST request to the Zendesk API to create a ticket, using
    the provided parameters to populate the ticket details. If the request fails
    or an exception occurs, a "Failed" response is returned along with the error
    message in case of an exception.

    Args:
        subject (str): The subject of the ticket.
        body (str): The body of the ticket containing the details or description.
        policy_holder_email (str): The email address of the policyholder.

    Returns:
        str: A JSON-formatted string containing the Zendesk API response if the
             ticket creation is successful. If the request is not successful,
             returns "Failed". In the case of an exception, returns a tuple
             containing "Failed" and the error message.
    """

    url = "https://rozishrddevadw1webeus.azurewebsites.net/api/utility/create-zendesk-ticket"

    payload = json.dumps({
      "subject": subject,
      "body": body,
      "recipient_email": policy_holder_email
    })

    headers = {
        'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',
        'Content-Type': 'application/json',
    }

    if payload:
        try:
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)