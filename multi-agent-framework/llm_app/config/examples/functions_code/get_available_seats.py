import json
import requests
from typing_extensions import Annotated


def get_available_seats(
    flight_id: Annotated[
        str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"
    ],
    context_arguments: dict = {},
) -> str:
    """This will get the list of available seats in flight"""
    query_param = {"flight_id": flight_id}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/flight/available-seats"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)


print(get_available_seats("FL124"))