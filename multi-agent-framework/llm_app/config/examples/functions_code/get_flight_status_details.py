import json
import requests
from datetime import datetime
from typing_extensions import Annotated

def get_flight_status_details(
    flight_id: Annotated[str, "All Capital alphanumeric ID exactly 5 characters long, no spaces, matching regex: FL\\d{3}[A-Z]"],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    query_param = {
        "flight_id": flight_id.upper()
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/flight/flight-status"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    result = None
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            result = response.json()
        except Exception as e:
            return "Failed", str(e)
    if result.get("status") == "Success":
        result = result.get("flight_data")
    return result

print(get_flight_status_details(flight_id="FL158"))