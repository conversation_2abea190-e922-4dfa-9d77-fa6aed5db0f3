import json
import requests
from typing_extensions import Annotated

def create_case_service(
    customer_id: Annotated[str, "The ID of the customer."],
    case_title: Annotated[str, "The title of the case. Describe the case in short."],
    case_description: Annotated[str, "The description of the case. Include the details of the case."],
    case_status: Annotated[str, "The status of the case."],
    case_notify_via: Annotated[str, "The preferred method of notification for the case. This can either be Email or Phone."],
    context_arguments: dict = {}
) -> str:
    """
    This function is a generic POST request handler for creating a case.
    
    Parameters:
    customer_id (str): The ID of the customer. This parameter is required.
    case_title (str): The title of the case. This parameter is required.
    case_description (str): The description of the case. This parameter is required.
    case_status (str): The status of the case. This parameter is required.
    case_notify_via (str): The preferred method of notification for the case. This parameter is required.
    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.
    
    Returns:
    str: The response from the server, formatted as a JSON string. If the request fails, it returns "Failed" along with the exception message.
    """
    
    body = {
        "customer_id": customer_id,
        "case_title": case_title,
        "case_description": case_description,
        "case_status": case_status,
        "case_notify_via": case_notify_via,
        "chat_id": context_arguments.get("chat_id")
    }

    url = "https://rozie-air.dev-scc-demo.rozie.ai/case/create_case"
    
    # Set the headers for the request
    headers = {"Content-Type": "application/json",
               "access_token":"Test@123"}
    
    # Prepare parameters for the POST request
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    
    if body:
        param["data"] = json.dumps(body)
        
        try:
            response = requests.post(**param)
            
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        
        except Exception as e:
            return "Failed", str(e)
        
result = create_case_service("123", "test", "test", "test", "test")
print("result",result)