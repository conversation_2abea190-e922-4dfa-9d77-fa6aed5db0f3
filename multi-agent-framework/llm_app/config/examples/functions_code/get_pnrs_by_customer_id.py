import json
import requests
from typing_extensions import Annotated

def get_pnrs_by_customer_id(
    customer_id: Annotated[str, "The customer id."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    url = f"http://rozie-air.dev-scc-demo.rozie.ai/pnr/active/{customer_id}"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers
    }
    try:
        response = requests.get(**param)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return json.dumps(response_json, indent=2)
    except Exception as e:
        return "Failed", str(e)

print(get_pnrs_by_customer_id("CUST001"))