import requests
import json

from typing_extensions import Annotated

def verify_otp(
        policy_holder_email: Annotated[str, "A valid email address matching regex: ^["
                                            "a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"],
        user_entered_otp: Annotated[str, "A 6-digit OTP code."],
):
    """
    Verify the OTP code entered by the user.

    Args:
        policy_holder_email: A valid email address matching regex:
                             ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$.
        user_entered_otp: A 6-digit OTP code that user has entered.

    Returns:
        str: The formatted JSON response as a string if successful, or "Failed" in case of an error.
        tuple: A tuple containing "Failed" and the exception details if an error occurs during the
               API request.
    """

    url = "https://rozishrddevadw1webeus.azurewebsites.net/api/utility/verify-otp"

    payload = json.dumps({
      "email": policy_holder_email,
      "otp_code": user_entered_otp,
    })

    headers = {
      'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',
      'Content-Type': 'application/json',
    }

    if payload:
        try:
            response = requests.request("POST", url, headers=headers, data=payload)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)