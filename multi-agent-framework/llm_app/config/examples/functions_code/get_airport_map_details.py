import json
import requests
from typing import List, Dict
from typing_extensions import Annotated

def get_airport_map_details(
    airport_map_input: Annotated[List[str], "List of city names or airport codes."],
    input_type: Annotated[str, "Specifies if the input is 'city' or 'code'."],
    context_arguments: dict = {} 
) -> str:
    """Generic POST request handler for Airport Map data retrieval."""
    
    body = {
        "airport_map_input": airport_map_input,
        "input_type": input_type,
        "chat_id": context_arguments.get("chat_id")
    }

    url = "https://rozie-air.dev-scc-demo.rozie.ai/airport_map/get_airport_map_details"
    
    # Set the headers for the request
    headers = {"Content-Type": "application/json","access_token":"Test@123"}
    
    # Prepare parameters for the POST request
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    
    if body:
        param["data"] = json.dumps(body)
        
        try:
            response = requests.post(**param)
            
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        
        except Exception as e:
            return "Failed", str(e)
        
result = get_airport_map_details(["Mumbai"], "city")
print("result",result   )