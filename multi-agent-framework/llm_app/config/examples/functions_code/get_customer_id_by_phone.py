import json
import requests
from typing_extensions import Annotated

def get_customer_id_by_phone(
    phone_number: Annotated[str, "phone number of the customer."],
    context_arguments: dict = {}
)-> str:
    """This will get the list of customer ids """
    phone_number = phone_number.strip().replace(" ", "")
    query_param = {
        "phone_number": phone_number
	}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/customer/get_customer_id"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {
            "url": url,
            "timeout": 30,
            "headers": headers,
            "params": query_param
    }
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        except Exception as e:
            return "Failed", str(e)

