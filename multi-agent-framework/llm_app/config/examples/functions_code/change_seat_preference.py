import json
import requests
from typing_extensions import Annotated


def change_seat_preference(
    booking_reference_id: Annotated[
        str, "All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$"
    ],
    old_seat_number: Annotated[
        str, "Old seat number of passenger whose seat is getting changed"
    ],
    new_seat_number: Annotated[str, "Preferred new seat number of passenger whose seat is getting changed."],
    context_arguments: dict = {},
) -> str:
    """This will get the list of customer ids"""
    query_param = {"pnr_id": booking_reference_id}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    pnr_data = {}
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            pnr_data = response_json[0]
        except Exception as e:
            return "Failed", str(e)
    seat_dict = {}
    query_param = {"flight_id": pnr_data["flight_details"]["flight_number"]}
    url = "http://rozie-air.dev-scc-demo.rozie.ai/flight/available-seats"
    headers = {"Content-Type": "application/json", "access_token": "Test@123"}
    param = {"url": url, "timeout": 30, "headers": headers, "params": query_param}
    if query_param:
        try:
            response = requests.get(**param)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            for seat in response_json:
                seat_dict[seat["seat_number"]] = seat
        except Exception as e:
            return "Failed", str(e)

    for passenger in pnr_data["passengers"]:
        if passenger["seat"] == old_seat_number:
            passenger["seat"] = new_seat_number
            passenger["passenger_class"] = seat_dict[new_seat_number]["class"]

    url = "http://rozie-air.dev-scc-demo.rozie.ai/pnr/update_pnr"
    param = {"url": url, "timeout": 30, "headers": headers, "data": json.dumps(pnr_data)}
    try:
        response = requests.post(**param)
        if response.status_code != 200:
            return "Failed"
        response_json = response.json()
        return response_json
    except Exception as e:
        return "Failed", str(e)

# print(change_seat_preference("FL124ABC", "3C", "12D"))