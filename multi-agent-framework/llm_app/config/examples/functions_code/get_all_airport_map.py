import json
import requests
from typing import List, Dict
from typing_extensions import Annotated

def get_all_airport_map(
    context_arguments: dict = {} 
) -> str:
    """Generic POST request handler for get all Airport Map data retrieval."""
    
    body = {
        "chat_id": context_arguments.get("chat_id")
    }

    url = "https://rozie-air.dev-scc-demo.rozie.ai/airport_map/get_all_airport_map"
    
    # Set the headers for the request
    headers = {"Content-Type": "application/json","access_token":"Test@123"}
    
    # Prepare parameters for the POST request
    param = {
        "url": url,
        "timeout": 30,
        "headers": headers,
    }
    
    if body:
        param["data"] = json.dumps(body)
        
        try:
            response = requests.get(**param)
            print("response", response)
            if response.status_code != 200:
                return "Failed"
            response_json = response.json()
            return json.dumps(response_json, indent=2)
        
        except Exception as e:
            return "Failed", str(e)
        
result = get_all_airport_map()
print("result",result)