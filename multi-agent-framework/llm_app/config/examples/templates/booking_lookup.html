<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>Flight Booking Lookup</title>
  <style>
    .flight-form-container {
      padding: 24px;
      max-width: 400px;
      margin: 40px auto;
      background: linear-gradient(to bottom, #ffffff, #f6f3fa);
      border-radius: 16px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
      border: 1px solid #eee;
      font-family: 'Segoe UI', sans-serif;
    }

    .flight-form-container h3 {
      font-size: 20px;
      margin-bottom: 16px;
      color: #2e2e2e;
      text-align: center;
    }

    .flight-form-container label {
      display: block;
      margin-bottom: 14px;
      font-size: 14px;
      color: #444;
    }

    .flight-form-container input {
      width: 100%;
      padding: 10px 12px;
      margin-top: 6px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 8px;
      box-sizing: border-box;
      background-color: #fff;
    }

    .flight-form-container button {
      margin-top: 20px;
      padding: 12px;
      width: 100%;
      background-color: #7a3fa5;
      color: white;
      border: none;
      font-size: 16px;
      border-radius: 8px;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      font-weight: 600;
    }

    .flight-form-container button:hover {
      transform: scale(1.02);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
  </style>
</head>

<body>

  <div class="flight-form-container">
    <form onsubmit="
      event.preventDefault();
      const f = this;
      const data = {
        bookingReference: f.bookingReference.value.toUpperCase(),
        lastName: f.lastName.value
      };

      const msg =
        'Booking details submitted.\\n' +
        'Booking Reference: ' + data.bookingReference + '\\n' +
        'Last Name: ' + data.lastName;

      // This part is for RozieAI webchat auto-submission
      const shell = document.querySelector('rozieai-webchat')?.shadowRoot
        ?.querySelector('webchat-shell')?.shadowRoot;
      const input = shell?.querySelector('textarea.user-input');
      const button = shell?.querySelector('button.submit-btn');
      if (!input || !button) {
        alert('Simulated message: \\n' + msg);
        return console.warn('RozieAI input or button not found. Running in test mode.');
      }
      input.value = msg;
      input.dispatchEvent(new Event('input', { bubbles: true }));
      setTimeout(() => button.click(), 200);
    ">
      <label>Booking Reference Number (PNR)
        <input type="text" name="bookingReference" placeholder="e.g., ABC123" pattern="^[A-Z0-9]{5,8}$" required>
      </label>
      <label>Last Name
        <input type="text" name="lastName" placeholder="e.g., Smith" required>
      </label>
      <button type="submit">Submit</button>
    </form>
  </div>

</body>

</html>