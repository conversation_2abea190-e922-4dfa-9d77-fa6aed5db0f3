{"function_id": "change_seat_preference", "code": "def change_seat_preference(\n    booking_reference_id: Annotated[\n        str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"\n    ],\n    old_seat_number: Annotated[\n        str, \"Old seat number of passenger whose seat is getting changed\"\n    ],\n    new_seat_number: Annotated[str, \"Preferred new seat number of passenger whose seat is getting changed.\"],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids\"\"\"\n    query_param = {\"pnr_id\": booking_reference_id}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    pnr_data = {}\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            pnr_data = response_json[0]\n        except Exception as e:\n            return \"Failed\", str(e)\n    seat_dict = {}\n    query_param = {\"flight_id\": pnr_data[\"flight_details\"][\"flight_number\"]}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/flight/available-seats\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            for seat in response_json:\n                seat_dict[seat[\"seat_number\"]] = seat\n        except Exception as e:\n            return \"Failed\", str(e)\n\n    for passenger in pnr_data[\"passengers\"]:\n        if passenger[\"seat\"] == old_seat_number:\n            passenger[\"seat\"] = new_seat_number\n            passenger[\"passenger_class\"] = seat_dict[new_seat_number][\"class\"]\n\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/update_pnr\"\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"data\": json.dumps(pnr_data)}\n    try:\n        response = requests.post(**param)\n        if response.status_code != 200:\n            return \"Failed\"\n        response_json = response.json()\n        return response_json\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "The change_seat_preference function is used to update the seat preference for single passenger at a time.", "function_name": "change_seat_preference"}