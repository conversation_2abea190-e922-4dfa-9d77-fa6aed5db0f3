{"function_id": "get_customer_id", "code": "def get_customer_id(\n    last_name: Annotated[str, \"The last name of the user/client. This is a required field and must be provided.\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    query_param = {\n        \"last_name\": last_name\n\t}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/customer/get_customer_id\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n            \"params\": query_param\n    }\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "The get_customer_id returns the basic information of the client, to validate that they have account created. Use this to validate the calling customer or to get customer_id.", "function_name": "get_customer_id"}