{"function_id": "verify_otp", "code": "def verify_otp(\n        policy_holder_email: Annotated[str, \"A valid email address matching regex: ^[\"\n                                            \"a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$\"],\n        user_entered_otp: Annotated[str, \"A 6-digit OTP code.\"],\n):\n    \"\"\"\n    Verify the OTP code entered by the user.\n\n    Args:\n        policy_holder_email: A valid email address matching regex:\n                             ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$.\n        user_entered_otp: A 6-digit OTP code that user has entered.\n\n    Returns:\n        str: The formatted JSON response as a string if successful, or \"Failed\" in case of an error.\n        tuple: A tuple containing \"Failed\" and the exception details if an error occurs during the\n               API request.\n    \"\"\"\n\n    url = \"https://rozishrddevadw1webeus.azurewebsites.net/api/utility/verify-otp\"\n\n    payload = json.dumps({\n      \"email\": policy_holder_email,\n      \"otp_code\": user_entered_otp,\n    })\n\n    headers = {\n      'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',\n      'Content-Type': 'application/json',\n    }\n\n    if payload:\n        try:\n            response = requests.request(\"POST\", url, headers=headers, data=payload)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "Verify the OTP code entered by the user against the one sent to their email. Also it will check if the OTP code is expired or not.", "function_name": "verify_otp"}