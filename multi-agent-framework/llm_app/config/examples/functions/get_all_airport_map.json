{"function_id": "get_all_airport_map", "code": "def get_all_airport_map(\n    context_arguments: dict = {} \n) -> str:\n    \"\"\"Generic POST request handler for get all Airport Map data retrieval.\"\"\"\n    \n    body = {\n        \"chat_id\": context_arguments.get(\"chat_id\")\n    }\n\n    url = \"https://rozie-air.dev-scc-demo.rozie.ai/airport_map/get_all_airport_map\"\n    \n    # Set the headers for the request\n    headers = {\"Content-Type\": \"application/json\",\"access_token\":\"Test@123\"}\n    \n    # Prepare parameters for the POST request\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        \n        try:\n            response = requests.get(**param)\n            print(\"response\", response)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        \n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "The get_all_airport_map function retrieves a comprehensive list of all airports in the system. Unlike the specific airport details function, this endpoint requires no input parameters other than the context arguments. It returns complete information about all available airports, including their codes, cities, locations, and other relevant details. This is useful for getting an overview of all airports in the network.", "function_name": "get_all_airport_map"}