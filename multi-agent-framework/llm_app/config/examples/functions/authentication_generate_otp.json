{"function_id": "authentication_send_otp", "code": "def authentication_send_otp(\n    customer_id: Annotated[str, \"The customer id.\"] = \"\",\n    booking_reference: Annotated[str, \"The booking reference.\"] = \"\",\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids\"\"\"\n    if not customer_id and not booking_reference:\n        return {\n            \"status\": \"Failed\",\n            \"message\": \"Please provide either customer_id or booking_reference\"\n        }\n    body = {\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"customer_id\": customer_id,\n        \"pnr\": booking_reference\n    }\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/authentication/generate_otp\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers}\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "function to generate and share the OTP to customer.", "function_name": "authentication_send_otp"}