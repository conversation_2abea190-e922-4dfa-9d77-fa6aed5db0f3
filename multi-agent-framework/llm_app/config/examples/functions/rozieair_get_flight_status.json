{"function_id": "rozieair_get_flight_status", "code": "def rozieair_get_flight_status(\n    carrier_code: str = \"BA\",\n    flight_number: str = \"987\",\n    scheduled_departure_date: str = \"2025-05-30\",\n    context_arguments: dict = {}\n) -> dict:\n    \"\"\"Returns hardcoded flight status information (mocked).\"\"\"\n    from datetime import datetime\n\n    flight_id = carrier_code + flight_number\n    dep_time_raw = \"2025-05-30T06:55:00+00:00\"\n    arr_time_raw = \"2025-05-30T09:00:00+00:00\"\n    duration = \"2h 5m\"\n    status = \"ON TIME\"\n\n    # Transform datetime strings for template display\n    def format_datetime_for_display(iso_string):\n        dt = datetime.fromisoformat(iso_string.replace('Z', '+00:00'))\n        return {\n            'date': dt.strftime('%b %d'),  # \"May 30\"\n            'time': dt.strftime('%H:%M')   # \"06:55\"\n        }\n\n    dep_formatted = format_datetime_for_display(dep_time_raw)\n    arr_formatted = format_datetime_for_display(arr_time_raw)\n\n    result = {\n        \"flight_id\": flight_id,\n        \"departure_city\": \"London\",\n        \"departure_airport_code\": \"LHR\",\n        \"arrival_city\": \"Berlin\",\n        \"arrival_airport_code\": \"BER\",\n        \"departure_time\": dep_time_raw,  # Keep original for any other use\n        \"arrival_time\": arr_time_raw,    # Keep original for any other use\n        \"departure_date\": dep_formatted['date'],      # \"May 30\" - for template\n        \"departure_time_display\": dep_formatted['time'],  # \"06:55\" - for template\n        \"arrival_date\": arr_formatted['date'],        # \"May 30\" - for template\n        \"arrival_time_display\": arr_formatted['time'],    # \"09:00\" - for template\n        \"duration\": duration,\n        \"status\": status\n    }\n\n    return result", "description": "Get flight status information and return a formatted card with flight details. The function returns raw API data that will be formatted using the flight-status template.", "function_name": "rozieair_get_flight_status", "reflect_tool_result": false, "template": "flight-status"}