{"function_id": "make_salon_booking", "code": "def make_salon_booking(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location of the selected salon branch.\"],\n    Date_of_Appointment: Annotated[str, \"Specifies the date of the appointment in YYYY-MM-DD format.\"],    \n    Service_Description: Annotated[list ,\"It should list of dictionary like [{'Salon_Service':'service name', 'Stylist_Name':'stylist name'}], A list of services with associated stylist details. Each entry should be an object with 'Salon_Service' (service name) and 'Stylist_Name' (stylist's name).\"],\n    Appointment_Time: Annotated[str, \"Specifies the time of the appointment in HH:MM:SS format.\"],\n    Phone_Number: Annotated[str, \"A valid US PhoneNumber, That customer wants to book appointment with.\"],\n    Appointment_Note: Annotated[str, \"Any additional notes for the appointment.\"] = \"\",\n    context_arguments: dict = {}  \n) -> str:\n    body = {\n        \"business_id\": context_arguments.get(\"BusinessId\"),\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"Date_of_Appointment\": Date_of_Appointment,\n        \"Salons_Branch_Name\": Salons_Branch_Name,\n        \"Service_Description\": Service_Description,\n        \"Appointment_Time\": Appointment_Time,\n        \"Appointment_Note\": Appointment_Note,\n        \"phone_number\": Phone_Number if Phone_Number else context_arguments.get(\"PhoneNumber\")\n    }\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/make_booking\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in make_salon_booking:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "code_dummy_booking": "def make_salon_booking(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location of the selected salon branch.\"],\n    Date_of_Appointment: Annotated[str, \"Specifies the date of the appointment in YYYY-MM-DD format.\"],\n    Service_Description: Annotated[list ,\"It should list of dictionary like [{'Salon_Service':'service name', 'Stylist_Name':'stylist name'}], A list of services with associated stylist details. Each entry should be an object with 'Salon_Service' (service name) and 'Stylist_Name' (stylist's name).\"],\n    Appointment_Time: Annotated[str, \"Specifies the time of the appointment in HH:MM:SS format.\"],\n    Phone_Number: Annotated[str, \"A valid US PhoneNumber, That customer wants to book appointment with.\"],\n    Appointment_Note: Annotated[str, \"Any additional notes for the appointment.\"] = \"\",\n    context_arguments: dict = {}\n) -> str:\n    body = {\n        \"business_id\": context_arguments.get(\"BusinessId\"),\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"Date_of_Appointment\": Date_of_Appointment,\n        \"Salons_Branch_Name\": Salons_Branch_Name,\n        \"Service_Description\": Service_Description,\n        \"Appointment_Time\": Appointment_Time,\n        \"Appointment_Note\": Appointment_Note,\n        \"phone_number\": Phone_Number if Phone_Number else context_arguments.get(\"PhoneNumber\")\n    }\n    return \"Booking Confirmed\"", "description": "The make_salon_booking function allows you to schedule an appointment at a salon by providing essential details such as the branch name, appointment date in YYYY-MM-DD format, time in HH:MM:SS format, and a list of services along with the corresponding stylist’s name for each service. You can also include an optional appointment note. This function helps streamline the booking process by ensuring the right services and stylist are scheduled at the specified time and date, making it easier to manage salon appointments.", "function_name": "make_salon_booking"}