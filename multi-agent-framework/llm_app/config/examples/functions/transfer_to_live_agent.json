{"function_id": "transfer_to_live_agent", "code": "def transfer_to_live_agent(\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"\n    This function will transfer the call to live agent. \n    \"\"\"\n\n    return {\n        \"status\": \"Agent_Unavailable\",\n        \"suggested_action\": \"Let customer know that salon representatives are currently unavailable. After that silently invoke `get_self_serve_guideline` to get instruction.\"\n    }", "description": "Function will transfer the call to live agent.", "function_name": "transfer_to_live_agent"}