{"function_id": "pnr_name_change", "code": "def pnr_name_change(\n    booking_reference_id: Annotated[\n        str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"\n    ],\n    old_first_name: Annotated[str, \"old first name of passenger (before correction).\"],\n    new_first_name: Annotated[str, \"New corrected first name of passenger.\"],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids and send an acknowledgement email after name change.\"\"\"\n    body = {\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"pnr_id\": booking_reference_id,\n        \"old_first_name\": old_first_name,\n        \"first_name\": new_first_name,\n    }\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/name_correction\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers}\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            updated_pnr_data = response_json[0] if isinstance(response_json, list) else response_json\n        except Exception as e:\n            return \"Failed\"\n    else:\n        return \"Failed\"\n\n    # After the name correction API call and before extracting passenger:\n    if isinstance(updated_pnr_data, dict) and \"passengers\" not in updated_pnr_data:\n        # Fetch the updated PNR details\n        url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr\"\n        headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n        param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": {\"pnr_id\": booking_reference_id}}\n        try:\n            response = requests.get(**param)\n            if response.status_code == 200:\n                response_json = response.json()\n                updated_pnr_data = response_json[0]\n            else:\n                return \"Failed\"\n        except Exception as e:\n            return \"Failed\"\n\n    # --- Begin: Send acknowledgement email logic (inlined, not imported) ---\n    # Extract booking details from updated_pnr_data\n    booking = updated_pnr_data\n    # Find the passenger with the new first name\n    passenger = None\n    for p in booking.get(\"passengers\", []):\n        if p[\"first_name\"].strip().lower() == new_first_name.lower():\n            passenger = p\n            break\n    if not passenger:\n        return \"Failed\"\n    flight_segments = booking.get(\"flight_segments\", [])\n    email = passenger.get(\"email\")\n    user_name = f\"{passenger.get('first_name', '')} {passenger.get('last_name', '')}\".strip()\n    additional_services = passenger.get(\"additional_services\", [])\n\n    # Get first and last flight segments\n    first_segment = flight_segments[0]\n    last_segment = flight_segments[-1]\n    dep_code = first_segment[\"departure_airport\"]\n    arr_code = last_segment[\"arrival_airport\"]\n    dep_date = first_segment[\"departure_time\"][:10]  # ISO date\n    arr_date = last_segment[\"arrival_time\"][:10]     # ISO date\n    flight_number = first_segment[\"flight_number\"]\n\n    # Get airport details (use static fallback for now)\n    def get_airport_details(iata_code):\n        url = \"https://raw.githubusercontent.com/jpatokal/openflights/master/data/airports.dat\"\n        r = requests.get(url)\n        if r.status_code != 200:\n            return {\"city\": iata_code, \"country\": \"\", \"name\": iata_code}\n        for line in r.text.splitlines():\n            fields = line.split(',')\n            if len(fields) >= 6 and fields[4].strip('\"').upper() == iata_code.upper():\n                return {\n                    \"name\": fields[1].strip('\"'),\n                    \"city\": fields[2].strip('\"'),\n                    \"country\": fields[3].strip('\"'),\n                }\n        return {\"city\": iata_code, \"country\": \"\", \"name\": iata_code}\n\n    dep_airport = get_airport_details(dep_code)\n    arr_airport = get_airport_details(arr_code)\n\n    # Build the email subject and message (for name correction completed)\n    subject = f\"Name Correction Completed | PNR: {booking_reference_id}\"\n    message = f\"\"\"\n        Your name has been successfully updated to: <strong>{new_first_name} {passenger.get('last_name', '')}</strong> for PNR <strong>{booking_reference_id}</strong>.<br>\n        If you did not request this change, please contact support immediately.\n    \"\"\"\n\n    # Generate journey card HTML (reuse your latest version)\n    journey_card = f\"\"\"\n<div class=\\\"journey-card\\\">\n  <table width=\\\"100%\\\" style=\\\"border-collapse: collapse;\\\">\n    <tr>\n      <td align=\\\"left\\\" style=\\\"width: 40%;\\\">\n        <h2 style=\\\"margin: 0; font-size: 24px;\\\">{dep_airport['city']}<br>\n          <span style=\\\"font-weight: 600; font-size: 20px;\\\">{dep_airport['country']}</span></h2>\n      </td>\n      <td align=\\\"center\\\" style=\\\"width: 20%;\\\">\n        <img src=\\\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/timeline.png\\\" alt=\\\"Timeline\\\" style=\\\"max-width: 250px;\\\" />\n      </td>\n      <td align=\\\"right\\\" style=\\\"width: 40%;\\\">\n        <h2 style=\\\"margin: 0; font-size: 24px;\\\">{arr_airport['city']}<br>\n          <span style=\\\"font-weight: 600; font-size: 20px;\\\">{arr_airport['country']}</span></h2>\n      </td>\n    </tr>\n    <tr><td colspan=\\\"3\\\" height=\\\"20\\\"></td></tr>\n    <tr>\n      <td align=\\\"left\\\" style=\\\"font-size: 15px; line-height: 1.5;\\\">\n        Departure airport: {dep_code}\n      </td>\n      <td></td>\n      <td align=\\\"right\\\" style=\\\"font-size: 15px; line-height: 1.5;\\\">\n        Arrival airport: {arr_code}\n      </td>\n    </tr>\n    <tr><td colspan=\\\"3\\\" height=\\\"30\\\"></td></tr>\n    <tr>\n      <td align=\\\"left\\\" valign=\\\"middle\\\">\n        <table>\n          <tr>\n            <td>\n              <img src=\\\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/calendar.png\\\" alt=\\\"Calendar Icon\\\" style=\\\"width: 40px;\\\" />\n            </td>\n            <td style=\\\"padding-left: 10px; font-size: 18px;\\\">{dep_date}</td>\n          </tr>\n        </table>\n      </td>\n      <td></td>\n      <td align=\\\"right\\\" valign=\\\"middle\\\">\n        <table align=\\\"right\\\">\n          <tr>\n            <td style=\\\"font-size: 18px; padding-right: 10px;\\\" align=\\\"right\\\">{arr_date}</td>\n            <td>\n              <img src=\\\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/clock.png\\\" alt=\\\"Clock Icon\\\" style=\\\"width: 40px;\\\" />\n            </td>\n          </tr>\n        </table>\n      </td>\n    </tr>\n  </table>\n</div>\n    \"\"\"\n\n    # Build final email template\n    email_html = f\"\"\"\n    <!DOCTYPE html>\n    <html lang=\\\"en\\\">\n    <head>\n      <meta charset=\\\"UTF-8\\\" />\n      <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\"/>\n      <title>RozieAI Concierge Email</title>\n      <style>\n        body {{\n          margin: 0;\n          padding: 0;\n          background-color: #f5f5f9;\n          font-family: 'Segoe UI', Tahoma, sans-serif;\n          color: #333333;\n        }}\n        .email-wrapper {{\n          max-width: 700px;\n          margin: 0 auto;\n          background-color: #f0f0f8;\n          border-radius: 16px;\n          overflow: hidden;\n          box-shadow: 0 4px 24px rgba(83,74,216,0.10);\n        }}\n        .email-header img {{\n          width: 100%;\n          height: auto;\n          display: block;\n        }}\n        .email-content {{\n          padding: 32px 32px 0 32px;\n          background-color: #f0f0f8;\n          color: #333333;\n        }}\n        .email-content p {{\n          font-size: 17px;\n          line-height: 1.7;\n          margin: 18px 0;\n          color: #333333;\n        }}\n        .journey-card {{\n          max-width: 700px;\n          background-image: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);\n          color: white;\n          border-radius: 20px;\n          padding: 30px;\n          font-family: 'Segoe UI', sans-serif;\n          margin: 20px 0;\n        }}\n        .email-footer {{\n          text-align: center;\n          background-color: #f0f0f8;\n          padding: 24px;\n          font-size: 14px;\n          color: #777777;\n          border-bottom-left-radius: 16px;\n          border-bottom-right-radius: 16px;\n        }}\n        .email-footer a {{\n          color: #4b2aad;\n          text-decoration: none;\n        }}\n      </style>\n    </head>\n    <body>\n      <div class=\\\"email-wrapper\\\">\n        <!-- Header Image -->\n        <div class=\\\"email-header\\\">\n          <img src=\\\"https://x0eqvtzqcztrrw6z5qtrfw.on.drv.tw/rozie-assets/header.png\\\" alt=\\\"RozieAI Header\\\" />\n        </div>\n\n        <!-- Content Section -->\n        <div class=\\\"email-content\\\">\n          <p>Dear {user_name},</p>\n          <p>{message}</p>\n          {journey_card}\n          <p style=\\\"margin-top:32px;\\\">Thank you for choosing us.<br>The RozieAI Team</p>\n        </div>\n\n        <!-- Footer Section -->\n        <div class=\\\"email-footer\\\">\n          © 2025 RozieAI · <a href=\\\"https://www.rozieai.com\\\">www.rozieai.com</a><br />\n        </div>\n      </div>\n    </body>\n    </html>\n    \"\"\"\n\n    # Send the email using the API (same as in pnr_prebook_meal)\n    api_url = \"https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event\"\n    headers_email = {\n        \"application-id\": \"application_9f6d6202-f5d9-445d-b856-82b44844f0d4\",\n        \"api-key\": \"5b5fadd08ddc4295abfa854244cbfbb2\",\n        \"Content-Type\": \"application/json\"\n    }\n    import uuid\n    event_id = f\"event_{uuid.uuid4()}\"\n    payload = {\n        \"version\": \"1.0\",\n        \"user_info\": {\n            \"user_id\": {\n                \"id\": f\"channel_user_{booking_reference_id}\",\n                \"id_type\": \"channel_user_id\",\n                \"id_resource\": \"webchat\"\n            }\n        },\n        \"channel\": {\n            \"channel_id\": f\"channel_{booking_reference_id}\",\n            \"channel_name\": \"webchat\",\n            \"ui_info\": {\n                \"should_consolidate_buttons\": True\n            },\n            \"reply_info\": {\n                \"reply_mode\": \"sync\"\n            }\n        },\n        \"incoming_events\": [\n            {\n                \"event_id\": event_id,\n                \"event_creator\": {\n                    \"event_creator_type\": \"user\",\n                    \"user_id\": {\n                        \"id\": f\"channel_user_{booking_reference_id}\",\n                        \"id_type\": \"channel_user_id\",\n                        \"id_resource\": \"webchat\"\n                    }\n                },\n                \"event_template\": {\n                    \"event_type\": \"skillconcept\",\n                    \"skill\": {\n                        \"skill_id\": \"RozieAirEmailTemplates\",\n                        \"usage_recommendation\": \"exclusive\"\n                    },\n                    \"text\": None,\n                    \"concepts\": [\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"Email\",\n                                \"entity_object\": {\n                                    \"Email\": email\n                                }\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        },\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"title\",\n                                \"entity_object\": {\n                                    \"title\": subject\n                                }\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        },\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"body\",\n                                \"entity_object\": {\n                                    \"body\": email_html\n                                }\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        }\n                    ],\n                    \"event_metadata\": {}\n                }\n            }\n        ]\n    }\n    try:\n        response = requests.post(api_url, headers=headers_email, json=payload)\n        response.raise_for_status()\n    except Exception as e:\n        return \"Failed\"\n    return \"Success\"", "description": "The pnr_name_change function is used to update the mistaken passenger name.", "function_name": "pnr_name_change"}