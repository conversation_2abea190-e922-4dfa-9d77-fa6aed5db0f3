{"function_id": "authentication_validate_otp", "code": "def authentication_validate_otp(\n    otp: Annotated[str, \"Strictly 6 digit numeric string, provided by customer for verification\"],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids\"\"\"\n    body = {\n        \"otp\": otp,\n        \"chat_id\": context_arguments.get(\"chat_id\")\n    }\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/authentication/validate_otp\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers}\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n        try:\n            response = requests.post(**param)\n\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "function to validate the otp.", "function_name": "authentication_validate_otp"}