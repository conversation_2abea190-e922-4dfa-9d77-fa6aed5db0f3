{"function_id": "create_zendesk_ticket", "code": "def create_zendesk_ticket(\n        subject: Annotated[str, \"Format : ^FNOL\\s[Policy_Number]+-\\s[Policy_Holder's_First_Name]+\\s[Policy_Holder's_Last_Name].\"],\n        body: Annotated[str, \"Brief summary of the reported incident by the user.\"],\n        policy_holder_email: Annotated[str, \"Email address of the policy holder.\"]\n):\n    \"\"\"\n    Creates a Zendesk ticket with the specified subject and body. This\n    function makes a POST request to the Zendesk API to create a ticket, using\n    the provided parameters to populate the ticket details. If the request fails\n    or an exception occurs, a \"Failed\" response is returned along with the error\n    message in case of an exception.\n\n    Args:\n        subject (str): The subject of the ticket.\n        body (str): The body of the ticket containing the details or description.\n        policy_holder_email (str): The email address of the policyholder.\n\n    Returns:\n        str: A JSON-formatted string containing the Zendesk API response if the\n             ticket creation is successful. If the request is not successful,\n             returns \"Failed\". In the case of an exception, returns a tuple\n             containing \"Failed\" and the error message.\n    \"\"\"\n\n    url = \"https://rozishrddevadw1webeus.azurewebsites.net/api/utility/create-zendesk-ticket\"\n\n    payload = json.dumps({\n      \"subject\": subject,\n      \"body\": body,\n      \"recipient_email\": policy_holder_email\n    })\n\n    headers = {\n        'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',\n        'Content-Type': 'application/json',\n    }\n\n    if payload:\n        try:\n            response = requests.request(\"POST\", url, headers=headers, data=payload)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "create_zendesk_ticket function will create a Zendesk ticket based on user's FNOL incident description gathered.", "function_name": "create_zendesk_ticket"}