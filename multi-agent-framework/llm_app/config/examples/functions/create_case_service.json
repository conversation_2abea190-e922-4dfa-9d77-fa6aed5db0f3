{"function_id": "create_case_service", "code": "def create_case_service(\n    customer_id: Annotated[str, \"The ID of the customer.\"],\n    case_title: Annotated[str, \"The title of the case. Describe the case in short.\"],\n    case_description: Annotated[str, \"The description of the case. Include the details of the case.\"],\n    case_status: Annotated[str, \"The status of the case.\"],\n    case_notify_via: Annotated[str, \"The preferred method of notification for the case. This can either be Email or Phone.\"],\n    context_arguments: dict = {}\n) -> str:\n    \"\"\"\n    This function is a generic POST request handler for creating a case.\n    \n    Parameters:\n    customer_id (str): The ID of the customer. This parameter is required.\n    case_title (str): The title of the case. This parameter is required.\n    case_description (str): The description of the case. This parameter is required.\n    case_status (str): The status of the case. This parameter is required.\n    case_notify_via (str): The preferred method of notification for the case. This parameter is required.\n    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.\n    \n    Returns:\n    str: The response from the server, formatted as a JSON string. If the request fails, it returns \"Failed\" along with the exception message.\n    \"\"\"\n    \n    body = {\n        \"customer_id\": customer_id,\n        \"case_title\": case_title,\n        \"case_description\": case_description,\n        \"case_status\": case_status,\n        \"case_notify_via\": case_notify_via,\n        \"chat_id\": context_arguments.get(\"chat_id\")\n    }\n\n    url = \"https://rozie-air.dev-scc-demo.rozie.ai/case/create_case\"\n    \n    # Set the headers for the request\n    headers = {\"Content-Type\": \"application/json\",\n               \"access_token\":\"Test@123\"}\n    \n    # Prepare parameters for the POST request\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        \n        try:\n            response = requests.post(**param)\n            \n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        \n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The create_case_service function allows users to create a new case in the system. It requires several mandatory parameters: customer_id to identify the customer, case_title for the case heading, case_description for detailed information about the case, case_status to set the initial status, and case_notify_via to specify how the customer should be notified about case updates. This function helps in tracking and managing customer service requests systematically.", "function_name": "create_case_service"}