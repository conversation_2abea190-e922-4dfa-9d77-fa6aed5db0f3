{"function_id": "get_salon_prices", "code": "def get_salon_prices(\n    Salons_Branch_Name: Annotated[str, \"This parameter specifies the location selected by customer.\"],\n    salon_service: Annotated[list, \"List of salon services selected by the customer.\"],  \n    context_arguments: dict = {} ) -> str:\n    body = {\n    \"business_id\": context_arguments.get(\"BusinessId\"),\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Branch_Name\":Salons_Branch_Name,\n    \"Salon_Service\": salon_service\n\t}  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_salon_prices\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n\n    if body:\n        param[\"data\"] = json.dumps(body)\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_salon_prices:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "description": "The get_salon_prices function retrieves the prices for services at a specific salon branch. It provides a list of selected services along with their corresponding prices. Use this function to quickly check the service prices at a particular salon, including the prices set by individual stylists.", "function_name": "get_salon_prices"}