{"function_id": "send_otp", "code": "def send_otp(\n        policy_holder_email: Annotated[str, \"A valid email address matching regex: ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}$\"]\n):\n    \"\"\"\n    Generates a one-time password (OTP) and sends it to the provided email address.\n\n    Args:\n        policy_holder_email: A valid email address matching regex:\n                             ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$.\n\n    Returns:\n        str: The formatted JSON response as a string if successful, or \"Failed\" in case of an error.\n        tuple: A tuple containing \"Failed\" and the exception details if an error occurs during the\n               API request.\n    \"\"\"\n\n    url = \"https://rozishrddevadw1webeus.azurewebsites.net/api/utility/send-otp\"\n\n    payload = json.dumps({\n      \"email\": policy_holder_email,\n      \"expiry_time_min\": 10\n    })\n\n    headers = {\n      'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',\n      'Content-Type': 'application/json',\n    }\n\n    if payload:\n        try:\n            response = requests.request(\"POST\", url, headers=headers, data=payload)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "send_otp function will get the policy_holder_email as a user parameter and sends the OTP using SendGrid API", "function_name": "send_otp"}