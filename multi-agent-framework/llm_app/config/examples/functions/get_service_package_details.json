{"function_id": "get_service_package_details", "code": "def get_service_package_details(\n    Salons_Branch_Name: Annotated[str, \"Represents the valid salon location name selected by customer.\"],\n    Salon_Service: Annotated[list, \"Represents a list of valid services names whose details need to be retrieved.\"],\n    context_arguments:dict = {},\n) -> str:\n\n    body = {\n    \"business_id\": context_arguments.get(\"BusinessId\"),\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Branch_Name\": Salons_Branch_Name,\n    \"Salon_Service\": Salon_Service\n\t}  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_service_package_details\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_locations:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "description": "The get_service_package_details function helps you find service and package details like name, category, description and duration. Details for package additionally include the services included in that package.", "function_name": "get_service_package_details"}