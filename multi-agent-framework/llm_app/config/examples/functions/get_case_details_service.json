{"function_id": "get_case_details_service", "code": "def get_case_details_service(\n    case_id: Annotated[str, \"The Case ID to search for.\"],\n    context_arguments: dict ={}\n) -> str:\n    \"\"\"\n    This function is a generic POST request handler for retrieving case details.\n    \n    Parameters:\n    id_input (str): The ID to search for. This can be either a Customer ID or a Case ID. This parameter is required.\n    input_type (str): Specifies the type of the input ID. This can be either 'customer_id' or 'case_id'. This parameter is required.\n    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.\n    \n    Returns:\n    str: The response from the server, formatted as a JSON string. If the request fails, it returns \"Failed\" along with the exception message.\n    \"\"\"\n    \n    body = {\n        \"id_input\": case_id,\n        \"input_type\": \"case_id\",\n        \"chat_id\": context_arguments.get(\"chat_id\")\n    }\n\n    url = \"https://rozie-air.dev-scc-demo.rozie.ai/case/get_case_details\"\n    \n    headers = {\"Content-Type\": \"application/json\",\n               \"access_token\": \"Test@123\"}\n    \n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        \n        try:\n            response = requests.post(**param)\n            print(\"response\",response)  \n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        \n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The get_case_details_service function retrieves detailed information about customer service cases.", "function_name": "get_case_details_service"}