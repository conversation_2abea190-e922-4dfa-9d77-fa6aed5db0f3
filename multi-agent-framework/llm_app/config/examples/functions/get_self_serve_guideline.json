{"function_id": "get_self_serve_guideline", "code": "def get_self_serve_guideline(\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"\n    This function will return the self serve instructions. \n    \"\"\"\n    self_serve_guideline = \"\"\"Ask the customer to briefly describe the reason they want to connect with a live agent.\n    Must wait for their response.\n    Only after collecting the response, if customer's request is to assist with New Booking, Rescheduling, or Cancelling, inform the customer that you can assist with that directly.\n    Then, ask the customer to confirm whether they would like to proceed with your assistance.\"\"\"\n    return self_serve_guideline", "description": "This function will return the self serve instructions.", "function_name": "get_self_serve_guideline"}