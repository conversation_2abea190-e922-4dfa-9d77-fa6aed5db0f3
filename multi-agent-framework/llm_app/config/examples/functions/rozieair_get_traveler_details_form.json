{"function_id": "rozieair_get_traveler_details_form", "code": "def rozieair_get_traveler_details_form(\n    traveler_id: str,\n    context_arguments: dict = {}\n) -> dict:\n    \"\"\"Return a traveler details form for the given traveler_id.\"\"\"\n    return {\n        \"llm_result\": {\n          \"traveler_id\": traveler_id,\n          \"next_suggested_action\": \"Collect traveler details such as first and last name, date of birth, gender, email, number, passport number and its expiry. Invoke `rozieair_store_traveler_details` to validate and store the customer provided traveler details.\"\n        },\n        \"custom_results\": [\n            {\n                \"response_type\": \"html\",\n                \"cardName\": \"traveler-details-form\",\n                \"body\": f'''\n<div class=\"traveler-form-container\">\n  <form onsubmit=\"\n    event.preventDefault();\n    const f = this;\n    const data = {{\n      id: '{traveler_id}',\n      name: {{ firstName: f.firstName.value, lastName: f.lastName.value }},\n      dateOfBirth: f.dateOfBirth.value,\n      gender: f.gender.value,\n      contact: {{\n        emailAddress: f.emailAddress.value,\n        phones: [{{\n          deviceType: 'MOBILE',\n          countryCallingCode: f.countryCallingCode.value,\n          number: f.number.value\n        }}]\n      }},\n      nationality: f.nationality.value,\n      passport: {{\n        number: f.passportNumber.value,\n        expiryDate: f.passportExpiryDate.value\n      }},\n    }};\n    const msg =\n      'Traveler details submitted.\\\\n' +\n      'Traveler ID: ' + data.id + '\\\\n' +\n      'Name: ' + data.name.firstName + ' ' + data.name.lastName + '\\\\n' +\n      'Date of Birth: ' + data.dateOfBirth + '\\\\n' +\n      'Gender: ' + data.gender + '\\\\n' +\n      'Email: ' + data.contact.emailAddress + '\\\\n' +\n      'Phone: +' + data.contact.phones[0].countryCallingCode + ' ' + data.contact.phones[0].number\n      + '\\\\nNationality: ' + data.nationality\n      + '\\\\nPassport Number: ' + data.passport.number\n      + '\\\\nPassport Expiry: ' + data.passport.expiryDate;\n\n    const shell = document.querySelector('rozieai-webchat')?.shadowRoot\n      ?.querySelector('webchat-shell')?.shadowRoot;\n    const input = shell?.querySelector('textarea.user-input');\n    const button = shell?.querySelector('button.submit-btn');\n    if (!input || !button) return console.error('❌ Input or button not found');\n    input.value = msg;\n    input.dispatchEvent(new Event('input', {{ bubbles: true }}));\n    setTimeout(() => button.click(), 200);\n  \">\n\n    <h3>Traveler Details (ID: {traveler_id})</h3>\n\n    <label>First Name\n      <input type=\"text\" name=\"firstName\" placeholder=\"e.g., Jorge\" required>\n    </label>\n    <label>Last Name\n      <input type=\"text\" name=\"lastName\" placeholder=\"e.g., Gonzales\" required>\n    </label>\n    <label>Date of Birth\n      <input type=\"date\" name=\"dateOfBirth\" required>\n    </label>\n    <label>Gender\n      <select name=\"gender\" required>\n        <option value=\"\">Select Gender</option>\n        <option value=\"MALE\">Male</option>\n        <option value=\"FEMALE\">Female</option>\n        <option value=\"OTHER\">Other</option>\n      </select>\n    </label>\n    <label>Email Address\n      <input type=\"email\" name=\"emailAddress\" placeholder=\"e.g., <EMAIL>\" required>\n    </label>\n    <label>Phone Country Code\n      <input type=\"text\" name=\"countryCallingCode\" placeholder=\"e.g., 34\" required>\n    </label>\n    <label>Phone Number\n      <input type=\"text\" name=\"number\" placeholder=\"e.g., *********\" required>\n    </label>\n    <label>Nationality\n      <input type=\"text\" name=\"nationality\" placeholder=\"e.g., IN\" required>\n    </label>\n    <label>Passport Number\n      <input type=\"text\" name=\"passportNumber\" placeholder=\"e.g., 00000000\" required>\n    </label>\n    <label>Passport Expiry Date\n      <input type=\"date\" name=\"passportExpiryDate\" required>\n    </label>\n    <input type=\"hidden\" name=\"travelerId\" value=\"{traveler_id}\">\n    <button type=\"submit\">Submit Traveler Info</button>\n  </form>\n</div>\n''',\n                \"css\": \"\"\"\n<style>\n  .traveler-form-container {\n    font-family: 'Segoe UI', sans-serif;\n    padding: 16px;\n    max-width: 400px;\n    margin: auto;\n    background: #f4f7f9;\n    border-radius: 8px;\n    box-shadow: 0 0 10px rgba(0,0,0,0.08);\n  }\n  .traveler-form-container h3 {\n    margin-bottom: 12px;\n    font-size: 18px;\n    color: #333;\n  }\n  .traveler-form-container label {\n    display: block;\n    margin-bottom: 12px;\n    font-size: 14px;\n    color: #555;\n  }\n  .traveler-form-container input,\n  .traveler-form-container select {\n    width: 100%;\n    padding: 8px;\n    margin-top: 4px;\n    font-size: 14px;\n    border: 1px solid #ccc;\n    border-radius: 4px;\n    box-sizing: border-box;\n  }\n  .traveler-form-container button {\n    margin-top: 12px;\n    padding: 10px;\n    width: 100%;\n    background-color: #834C9D;\n    color: white;\n    border: none;\n    font-size: 15px;\n    border-radius: 4px;\n    cursor: pointer;\n  }\n  .traveler-form-container button:hover {\n    background-color: #6b3d82;\n  }\n</style>\n\"\"\"\n            }\n        ]\n    }", "description": "Return a traveler details form for the given traveler_id. The form allows the user to fill out and submit traveler information such as name, date of birth, gender, email, and phone number.", "function_name": "rozieair_get_traveler_details_form", "reflect_tool_result": false}