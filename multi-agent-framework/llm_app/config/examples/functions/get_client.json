{"function_id": "get_client", "code": "def get_client(\n    Phone_Number: Annotated[str, \"A valid US PhoneNumber, That customer wants to book appointment with.\"]= \"\",\n    context_arguments: dict = {}  \n) -> str:\n        \"\"\"\n        This function checks if a client exists based on the phone number\n        \"\"\"\n        \n        body = {\n        \"business_id\": context_arguments.get(\"BusinessId\"),\n        \"phone_number\": Phone_Number if Phone_Number else context_arguments.get(\"PhoneNumber\"),\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n\t    }\n        url = \"https://brook.dev-scc-demo.rozie.ai/phorest/get_client_account\"\n        headers = {\"Content-Type\": \"application/json\"}\n        param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n        }\n        if body:\n            param[\"data\"] = json.dumps(body)\n            try:\n                response = requests.post(**param)\n                if response.status_code != 200:\n                    return \"Failed\"\n                response_json = response.json()\n                return json.dumps(response_json, indent=2)\n            except Exception as e:\n                return \"Failed\", str(e)", "description": "The get_client function verifies if a client exists in the Phorest system by checking their phone number. It is helpful for confirming whether a client is already registered in the system. If the Phone_Number is not provided, the default phone number will be the number from which the customer dialed in.", "function_name": "get_client"}