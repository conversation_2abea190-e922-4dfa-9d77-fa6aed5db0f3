{"function_id": "rozieair_get_flight_details_form", "code": "def rozieair_get_flight_details_form(context_arguments: dict = {}) -> dict:\n    \"\"\"Return a flight details form.\"\"\"\n    return {\n        \"llm_result\": {\n            \"next_action\": \"ask customer Carrier Code, Flight Number and Scheduled Departure Date.\"\n        },\n        \"custom_results\": [\n            {\n                \"response_type\": \"html\",\n                \"cardName\": \"flight-details-form\",\n                \"body\": \"\"\"\n<div class=\"flight-form-container\">\n  <form onsubmit=\"\n    event.preventDefault();\n    const f = this;\n    const data = {\n      carrierCode: f.carrierCode.value.toUpperCase(),\n      flightNumber: f.flightNumber.value,\n      scheduledDepartureDate: f.scheduledDepartureDate.value\n    };\n\n    const msg =\n      'Flight details submitted.\\\\n' +\n      'Carrier Code: ' + data.carrierCode + '\\\\n' +\n      'Flight Number: ' + data.flightNumber + '\\\\n' +\n      'Scheduled Departure: ' + data.scheduledDepartureDate;\n\n    const shell = document.querySelector('rozieai-webchat')?.shadowRoot\n      ?.querySelector('webchat-shell')?.shadowRoot;\n    const input = shell?.querySelector('textarea.user-input');\n    const button = shell?.querySelector('button.submit-btn');\n    if (!input || !button) return console.error('❌ Input or button not found');\n    input.value = msg;\n    input.dispatchEvent(new Event('input', { bubbles: true }));\n    setTimeout(() => button.click(), 200);\n  \">\n    <label>Carrier Code (2 uppercase letters)\n      <input type=\"text\" name=\"carrierCode\" placeholder=\"e.g., AA\" pattern=\"[A-Z]{2}\" required>\n    </label>\n    <label>Flight Number (1-4 digits)\n      <input type=\"text\" name=\"flightNumber\" placeholder=\"e.g., 1234\" required>\n    </label>\n    <label>Scheduled Departure Date\n      <input type=\"date\" name=\"scheduledDepartureDate\" required>\n    </label>\n    <button type=\"submit\">Submit</button>\n  </form>\n</div>\n\"\"\",\n                \"css\": \"\"\"\nbody {\n  background: linear-gradient(to bottom, #f4f7fa, #fcfbfd);\n  font-family: 'Segoe UI', sans-serif;\n  margin: 0;\n  padding: 24px;\n}\n\n.flight-form-container {\n  padding: 24px;\n  max-width: 400px;\n  margin: auto;\n  background: linear-gradient(to bottom, #ffffff, #f6f3fa);\n  border-radius: 16px;\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);\n  border: 1px solid #eee;\n}\n\n.flight-form-container h3 {\n  font-size: 20px;\n  margin-bottom: 16px;\n  color: #2e2e2e;\n  text-align: center;\n}\n\n.flight-form-container label {\n  display: block;\n  margin-bottom: 14px;\n  font-size: 14px;\n  color: #444;\n}\n\n.flight-form-container input {\n  width: 100%;\n  padding: 10px 12px;\n  margin-top: 6px;\n  font-size: 14px;\n  border: 1px solid #ccc;\n  border-radius: 8px;\n  box-sizing: border-box;\n  background-color: #fff;\n}\n\n.flight-form-container button {\n  margin-top: 20px;\n  padding: 12px;\n  width: 100%;\n  background-color: #7a3fa5;\n  color: white;\n  border: none;\n  font-size: 16px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n  font-weight: 600;\n}\n\n.flight-form-container button:hover {\n  transform: scale(1.02);\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n}\n\"\"\"\n            }\n        ]\n    }", "description": "Return a traveler details form for the given traveler_id. The form allows the user to fill out and submit traveler information such as name, date of birth, gender, email, and phone number.", "function_name": "rozieair_get_flight_details_form", "reflect_tool_result": false}