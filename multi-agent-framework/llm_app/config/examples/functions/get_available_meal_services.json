{"function_id": "get_available_meal_services", "code": "def get_available_meal_services(\n    flight_id: Annotated[str, \"Alphanumeric five character long id with no spaces.\"],\n    seat_class: Annotated[str, \"Complete class name of the seat for which meal options should be looked up. Class name should be same as fetched from pnr details.\"],\n    context_arguments: dict = {},\n) -> str:\n    query_param = {\n        \"flight_id\": flight_id,\n        \"seat_class\": seat_class\n\t}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/flight/get-meal-options\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n            \"params\": query_param\n    }\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code not in [200, 500]:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The function to get available meal type for the flight. Function will return a dictionary of available meal option and its cost", "function_name": "get_available_meal_services"}