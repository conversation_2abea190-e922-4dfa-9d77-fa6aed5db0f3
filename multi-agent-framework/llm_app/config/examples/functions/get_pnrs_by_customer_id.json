{"function_id": "get_pnrs_by_customer_id", "code": "def get_pnrs_by_customer_id(\n    customer_id: Annotated[str, \"The customer id.\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    url = f\"http://rozie-air.dev-scc-demo.rozie.ai/pnr/active/{customer_id}\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers\n    }\n    try:\n        response = requests.get(**param)\n        if response.status_code != 200:\n            return \"Failed\"\n        response_json = response.json()\n        return json.dumps(response_json, indent=2)\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "The get_pnrs_by_customer_id returns the details associated with PNR.", "function_name": "get_pnrs_by_customer_id"}