{"function_id": "get_baggage_details", "code": "def get_baggage_details(\n    customer_last_name: Annotated[str, \"The last name of the user/client.\"],\n    pnr_id: Annotated[str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"] = \"\",\n    baggage_tag: Annotated[str, \"All alphanumeric ID exactly 11 characters long, no spaces\"] = \"\",\n    context_arguments: dict = {}\n) -> str:\n    \"\"\"\n    This function is a generic POST request handler for retrieving baggage details.\n    \n    Parameters:\n    pnr_id (str): The PNR ID to search for. This parameter is required.\n    context_arguments (dict): Additional context arguments that can be passed to the request. For example, this can include a chat_id if the request is being made in the context of a chat conversation.\n    \n    Returns:\n    str: The response from the server, formatted as a JSON string. If the request fails, it returns \"Failed\" along with the exception message.\n    \"\"\"\n    if not pnr_id and not baggage_tag:\n        return {\n            \"status\": \"Error\",\n            \"suggested_action\": \"Either `pnr_id` or `baggage_tag` must be provided.\"\n        }\n    body = {}\n    if pnr_id:\n        body = {\n            \"pnr_id\": pnr_id,\n            \"last_name\": customer_last_name,\n            \"chat_id\": context_arguments.get(\"chat_id\")\n        }\n    else:\n        body = {\n            \"baggage_id\": baggage_tag,\n            \"last_name\": customer_last_name,\n            \"chat_id\": context_arguments.get(\"chat_id\")\n        }\n\n    url = \"https://rozie-air.dev-scc-demo.rozie.ai/baggage/get_baggage_details\"\n    \n    # Set the headers for the request\n    headers = {\"Content-Type\": \"application/json\",\"access_token\":\"Test@123\"}\n    \n    # Prepare parameters for the POST request\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        \n        try:\n            response = requests.post(**param)\n            \n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        \n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The get_baggage_details function allows users to retrieve information about baggage associated with a specific booking. It one of PNR (Passenger Name Record) ID or Baggage_id to retrieve baggage details.", "function_name": "get_baggage_details"}