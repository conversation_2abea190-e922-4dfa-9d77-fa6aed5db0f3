{"function_id": "email_boarding_pass", "code": "def email_boarding_pass(\n    booking_reference_id: Annotated[str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"],\n    customer_id: Annotated[str, \"Customer Id.\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    query_param = {\n        \"pnr_id\": booking_reference_id,\n        \"customer_id\": customer_id\n    }\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/notification/email/send-boarding-pass\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    if query_param:\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return response_json\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "function email boarding pass to customer.", "function_name": "email_boarding_pass"}