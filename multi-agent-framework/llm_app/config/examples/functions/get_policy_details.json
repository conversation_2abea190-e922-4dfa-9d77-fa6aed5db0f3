{"function_id": "get_policy_details", "code": "def get_policy_details(\n        policy_number: Annotated[str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"]\n) -> str:\n    \"\"\"\n        This function retrieves policy details from the API endpoint using the provided policy number.\n\n        Args:\n            policy_number (str): The policy number to fetch details for. It should be a 6-character\n                                 alphanumeric string with no spaces.\n\n        Returns:\n            str: A JSON-formatted string containing the policy details if the request is successful.\n                 If the request fails or an exception occurs, returns \"Failed\" or a JSON object\n                 with the error message.\n    \"\"\"\n\n    url = f\"https://rozishrddevadw1webeus.azurewebsites.net/api/data?type=policy&key={policy_number}\"\n    headers = {\n        'application-id': 'application_9098687a-0374-4e4d-9a74-fa680631040a',\n        \"Content-Type\": \"application/json\",\n    }\n\n    try:\n        response = requests.get(url, headers=headers, timeout=30)\n        if response.status_code != 200:\n            return \"Failed\"\n        response_json = response.json()\n        return json.dumps(response_json, indent=2)\n    except Exception as e:\n        return json.dumps({\"status\": \"Failed\", \"error\": str(e)}, indent=2)", "description": "get_policy_details function will fetch policy details based on the policy number provided by the user.", "function_name": "get_policy_details"}