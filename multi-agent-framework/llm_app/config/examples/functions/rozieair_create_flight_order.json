{"function_id": "rozieair_create_flight_order", "code": "def rozieair_create_flight_order(\n    selected_offered_flight_number: Annotated[int, \"The index of selected flight from offered flight option. valid options 1, 2 or 3.\"],\n    context_arguments: dict = {}\n):\n    \"\"\"Create a flight order using a selected flight offer and traveler details.\n\n    This function combines the selected flight offer data and traveler details into a payload\n    that is sent to the flight order API endpoint. The payload must include:\n    - flightOffers: A list containing the selected flight offer data.\n    - travelers: A list of traveler details, each including id, dateOfBirth, gender, name, and contact information.\n    The flight offer data and traveler details are retrieved from the context arguments.\n\n    Parameters:\n        selected_offered_flight_number (int): An integer index representing the flight option selected by customer\n\n    Returns:\n        The JSON response from the API, or an error message if the request fails.\n    \"\"\"\n    try:\n        flight_offer_data = context_arguments.get(\"flight_offers\")[selected_offered_flight_number-1]\n        traveler_data = list(context_arguments.get(\"traveler_details\").values())\n    except json.JSONDecodeError as e:\n        return {\"error\": f\"Invalid JSON input: {str(e)}\"}\n\n    formatted_travelers = []\n    for t in traveler_data:\n        if not all(k in t for k in (\"id\", \"first_name\", \"last_name\", \"date_of_birth\", \"gender\", \"email\", \"phone_number\")):\n            return {\"error\": \"Invalid traveler data: missing required fields in traveler unknown\"}\n\n        formatted = {\n            \"id\": str(t[\"id\"]),\n            \"dateOfBirth\": t[\"date_of_birth\"],\n            \"gender\": t[\"gender\"],\n            \"name\": {\n                \"firstName\": t[\"first_name\"].upper(),\n                \"lastName\": t[\"last_name\"].upper()\n            },\n            \"contact\": {\n                \"emailAddress\": t[\"email\"],\n                \"phones\": [\n                    {\n                        \"deviceType\": \"MOBILE\",\n                        \"countryCallingCode\": t[\"country_calling_code\"].split(\" \")[0].replace(\"+\", \"\"),\n                        \"number\": t[\"phone_number\"][:10]\n                    }\n                ]\n            },\n            \"documents\": [\n                {\n                    \"documentType\": \"PASSPORT\",\n                    \"number\": t[\"passport_number\"],\n                    \"expiryDate\": t[\"passport_expiry_date\"],\n                    \"issuanceCountry\": t.get(\"nationality\", \"IN\"),\n                    \"nationality\": t.get(\"nationality\", \"IN\"),\n                    \"holder\": True\n                }\n            ]\n        }\n        formatted_travelers.append(formatted)\n\n    payload = {\n        \"data\": {\n            \"type\": \"flight-order\",\n            \"flightOffers\": [flight_offer_data],\n            \"travelers\": formatted_travelers\n        }\n    }\n    url = \"https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-order\"\n    headers = {\"accept\": \"application/json\", \"Content-Type\": \"application/json\"}\n    try:\n        response = requests.post(url, headers=headers, data=json.dumps(payload))\n        status_code = response.status_code\n        api_result = response.json()\n    except Exception:\n        return {\"error\": \"Failed to parse response\", \"status_code\": response.status_code, \"text\": response.text}\n\n    # If status code is not 200, return error and skip email, HTML, and custom response logic\n    if status_code != 200:\n        return {\n            \"error\": \"Flight booking API failed\",\n            \"status_code\": status_code,\n            \"response\": api_result\n        }\n\n    # The following logic is only executed if status_code == 200\n    # Check for booking reference and send mail only if status code is 200\n    data = api_result.get(\"data\", {})\n    associated_records = data.get(\"associatedRecords\", [])\n    booking_ref = associated_records[0].get(\"reference\") if associated_records else None\n    travelers = data.get(\"travelers\", [])\n    offers = data.get(\"flightOffers\", [])\n    if status_code == 200 and booking_ref:\n        # Inline email logic (placeholder)\n        print(f\"Sending booking confirmation email for reference: {booking_ref}\")\n        # Implement your email sending logic here\n\n    # Post-process the API response to generate UI card and summary\n    data = api_result.get(\"data\", {})\n    booking_ref = data.get(\"associatedRecords\", [{}])[0].get(\"reference\", \"N/A\")\n    booking_date = data.get(\"associatedRecords\", [{}])[0].get(\"creationDate\", \"N/A\")\n    queuing_office = data.get(\"queuingOfficeId\", \"N/A\")\n    travelers = data.get(\"travelers\", [])\n    offers = data.get(\"flightOffers\", [])\n    pricing = offers[0].get(\"travelerPricings\", []) if offers else []\n    html = [\n        '<div class=\"container\">',\n        '<details open>',\n        '<summary class=\"accordion-header\">Booking Information</summary>',\n        '<div class=\"accordion-content booking-info\">',\n        f'<p><span class=\"label\">Booking Reference:</span> {booking_ref}</p>',\n        f'<p><span class=\"label\">Booking Date:</span> {booking_date[:10] if booking_date != \"N/A\" else \"N/A\"}</p>',\n        f'<p><span class=\"label\">Queuing Office ID:</span> {queuing_office}</p>',\n        '</div></details>'\n    ]\n    html.append('<details>')\n    html.append('<summary class=\"accordion-header\">Traveler Details</summary>')\n    html.append('<div class=\"accordion-content travelers\">')\n    for t in travelers:\n        name = t.get(\"name\", {})\n        contact = t.get(\"contact\", {})\n        docs = t.get(\"documents\", [{}])[0]\n        html.append('<div class=\"traveler\">')\n        html.append(f'<p><span class=\"label\">Name:</span> {name.get(\"firstName\", \"N/A\")} {name.get(\"lastName\", \"N/A\")}</p>')\n        html.append(f'<p><span class=\"label\">Date of Birth:</span> {t.get(\"dateOfBirth\", \"N/A\")}</p>')\n        html.append(f'<p><span class=\"label\">Gender:</span> {t.get(\"gender\", \"N/A\").capitalize()}</p>')\n        html.append(f'<p><span class=\"label\">Email:</span> {contact.get(\"emailAddress\", \"N/A\")}</p>')\n        phone = contact.get(\"phones\", [{}])[0]\n        phone_str = f'+{phone.get(\"countryCallingCode\", \"\")} {phone.get(\"number\", \"\")}' if phone else \"N/A\"\n        html.append(f'<p><span class=\"label\">Phone:</span> {phone_str}</p>')\n        html.append(f'<p><span class=\"label\">Passport Number:</span> {docs.get(\"number\", \"N/A\")}</p>')\n        html.append(f'<p><span class=\"label\">Passport Expiry:</span> {docs.get(\"expiryDate\", \"N/A\")}</p>')\n        html.append(f'<p><span class=\"label\">Nationality:</span> {docs.get(\"nationality\", \"N/A\")}</p>')\n        html.append('</div><hr>')\n    html.append('</div></details>')\n    html.append('<details>')\n    html.append('<summary class=\"accordion-header\">Flight Itinerary</summary>')\n    html.append('<div class=\"accordion-content itinerary\">')\n    for offer in offers:\n        for itin in offer.get(\"itineraries\", []):\n            for seg in itin.get(\"segments\", []):\n                dep = seg.get(\"departure\", {})\n                arr = seg.get(\"arrival\", {})\n                dep_time = dep.get(\"at\", \"N/A\")\n                arr_time = arr.get(\"at\", \"N/A\")\n                try:\n                    dep_time_fmt = datetime.fromisoformat(dep_time).strftime(\"%b %d, %Y at %H:%M\")\n                except Exception:\n                    dep_time_fmt = dep_time\n                try:\n                    arr_time_fmt = datetime.fromisoformat(arr_time).strftime(\"%b %d, %Y at %H:%M\")\n                except Exception:\n                    arr_time_fmt = arr_time\n                html.append('<div class=\"flight-segment\">')\n                html.append(f'<h3>{\"Departure\" if itin == offer.get(\"itineraries\", [])[0] else \"Return\"} Flight</h3>')\n                html.append(f'<p><span class=\"label\">Flight:</span> {seg.get(\"carrierCode\", \"N/A\")} {seg.get(\"number\", \"N/A\")}</p>')\n                html.append(f'<p><span class=\"label\">Departure:</span> {dep.get(\"iataCode\", \"N/A\")} - {dep_time_fmt}</p>')\n                html.append(f'<p><span class=\"label\">Arrival:</span> {arr.get(\"iataCode\", \"N/A\")} - {arr_time_fmt}</p>')\n                html.append(f'<p><span class=\"label\">Duration:</span> {seg.get(\"duration\", \"N/A\").replace(\"PT\", \"\").replace(\"H\", \"h \").replace(\"M\", \"m\")}</p>')\n                html.append(f'<p><span class=\"label\">Aircraft:</span> {seg.get(\"aircraft\", {}).get(\"code\", \"N/A\")}</p>')\n                html.append(f'<p><span class=\"label\">Operating Carrier:</span> {seg.get(\"operating\", {}).get(\"carrierCode\", \"N/A\")}</p>')\n                html.append('</div>')\n    html.append('</div></details>')\n    html.append('<details>')\n    html.append('<summary class=\"accordion-header\">Pricing Summary</summary>')\n    html.append('<div class=\"accordion-content pricing\">')\n    html.append('<table class=\"pricing-table\"><thead><tr><th>Traveler</th><th>Base Fare</th><th>Taxes</th><th>Total</th></tr></thead><tbody>')\n    for p in pricing:\n        traveler_id = p.get(\"travelerId\", \"N/A\")\n        traveler = {}\n        for t in travelers:\n            if t.get(\"id\") == traveler_id:\n                traveler = t\n                break\n        name = traveler.get(\"name\", {})\n        base = p.get(\"price\", {}).get(\"base\", \"N/A\")\n        taxes = p.get(\"price\", {}).get(\"refundableTaxes\", \"N/A\")\n        total = p.get(\"price\", {}).get(\"total\", \"N/A\")\n        html.append(f'<tr><td>{name.get(\"firstName\", \"N/A\")} {name.get(\"lastName\", \"N/A\")}</td><td>€{base}</td><td>€{taxes}</td><td>€{total}</td></tr>')\n    grand_total = offers[0].get(\"price\", {}).get(\"grandTotal\", \"N/A\") if offers else \"N/A\"\n    html.append(f'</tbody><tfoot><tr><th colspan=\"3\">Grand Total</th><th>€{grand_total}</th></tr></tfoot></table>')\n    html.append('</div></details></div>')\n    css = \"\"\"\n    <style>\n    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f6f8; margin: 0; padding: 20px; color: #333; }\n    .container { max-width: 800px; margin: auto; background: #fff; border-radius: 8px; padding: 30px; box-shadow: 0 4px 12px rgba(0,0,0,0.05); }\n    h1 { text-align: center; color: #854E9F; }\n    .accordion-section { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 6px; overflow: hidden; }\n    .accordion-header { background-color: #f2f2f2; padding: 12px 20px; cursor: pointer; font-weight: bold; font-size: 1.1em; color: #854E9F; border-bottom: 1px solid #ddd; }\n    .accordion-content { padding: 20px; display: block; }\n    .booking-info, .traveler, .flight-segment, .pricing { margin-bottom: 15px; }\n    .label { font-weight: bold; display: inline-block; width: 150px; color: #555; }\n    .flight-segment { border: 1px solid #e1e1e1; border-radius: 5px; padding: 15px; margin-bottom: 15px; background-color: #fafafa; }\n    .flight-segment h3 { margin-top: 0; color: #2980b9; }\n    .pricing-table { width: 100%; border-collapse: collapse; }\n    .pricing-table th, .pricing-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }\n    .pricing-table th { background-color: #f0f0f0; }\n    @media (max-width: 600px) { .label { width: 100%; display: block; margin-bottom: 5px; } }\n    </style>\n    \"\"\"\n    # Safely extract first_email from formatted_travelers\n    first_email = None\n    for t in formatted_travelers:\n        if t.get(\"contact\", {}).get(\"emailAddress\"):\n            first_email = t[\"contact\"][\"emailAddress\"]\n            break\n    if not first_email:\n        return {\"error\": \"Could not find valid email address in traveler data.\"}\n    text_summary = (\n        f\"Your booking is confirmed. Reference: {booking_ref}. \"\n        f\"Total price: €{grand_total}. A confirmation email has been sent to {first_email}.\"\n    )\n\n    # Send HTML email using Rozie event adapter API\n    full_html = f\"<html><head>{css}</head><body>{''.join(html)}</body></html>\"\n\n    email_payload = {\n        \"version\": \"1.0\",\n        \"user_info\": {\n            \"user_id\": {\n                \"id\": \"channel_user_123\",\n                \"id_type\": \"channel_user_id\",\n                \"id_resource\": \"webchat\"\n            }\n        },\n        \"channel\": {\n            \"channel_id\": \"channel_id_123\",\n            \"channel_name\": \"webchat\",\n            \"ui_info\": {\"should_consolidate_buttons\": True},\n            \"reply_info\": {\"reply_mode\": \"sync\"}\n        },\n        \"incoming_events\": [\n            {\n                \"event_id\": \"event_0\",\n                \"event_creator\": {\n                    \"event_creator_type\": \"user\",\n                    \"user_id\": {\n                        \"id\": \"channel_user_0\",\n                        \"id_type\": \"channel_user_id\",\n                        \"id_resource\": \"webchat\"\n                    }\n                },\n                \"event_template\": {\n                    \"event_type\": \"skillconcept\",\n                    \"skill\": {\n                        \"skill_id\": \"EmailTest\",\n                        \"usage_recommendation\": \"exclusive\"\n                    },\n                    \"text\": None,\n                    \"concepts\": [\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"txEffectiveDate\",\n                                \"entity_object\": {\"txEffectiveDate\": \"required\"}\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        },\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"body\",\n                                \"entity_object\": {\"body\": full_html}\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        },\n                        {\n                            \"concept_value\": {\n                                \"entity_type\": \"email\",\n                                \"entity_object\": {\"email\": first_email}\n                            },\n                            \"is_checked\": True,\n                            \"is_valid\": True\n                        }\n                    ],\n                    \"event_metadata\": {}\n                }\n            }\n        ]\n    }\n\n    requests.post(\n        \"https://api-manager-sandbox.rozie.ai/event-adapter/v1/adapters-event\",\n        headers={\n            \"application-id\": \"application_97a3285b-e8c9-42bf-a924-d713272becaf\",\n            \"api-key\": \"5b5fadd08ddc4295abfa854244cbfbb2\",\n            \"Content-Type\": \"application/json\"\n        },\n        data=json.dumps(email_payload)\n    )\n\n    requests.post(\n        \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/add_pnr_details\",\n        headers={\n            \"Content-Type\": \"application/json\",\n            \"access_token\": \"Test@123\"\n        },\n        data=json.dumps(api_result)\n    )\n\n    return {\n        \"llm_result\": api_result,\n        \"custom_results\": [\n            {\"response_type\": \"text\", \"text\": text_summary},\n            {\"response_type\": \"html\", \"cardName\": \"flight-booking-confirmation\", \"body\": \"\".join(html), \"css\": css}\n        ]\n    }", "description": "Create a flight order using a selected flight offer and traveler details. This function combines the flight offer data and traveler information into a payload that is sent to the flight order API endpoint.", "function_name": "rozieair_create_flight_order", "reflect_tool_result": false}