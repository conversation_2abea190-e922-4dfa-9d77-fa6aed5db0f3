{"function_id": "get_pnr_details", "code": "def get_pnr_details(\n    booking_reference_id: Annotated[str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"],\n    customer_last_name: Annotated[str, \"The last name of the user/client.\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    query_param = {\n        \"pnr_id\": booking_reference_id.upper(),\n        \"customer_last_name\": customer_last_name.capitalize()\n\t}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n            \"params\": query_param\n    }\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The get_pnr_details returns the details associated with PNR.", "function_name": "get_pnr_details"}