{"function_id": "get_incident_location", "code": "def get_incident_location(\n        map_string: Annotated[str, \"A Google Maps URL, a long URL with coordinates, or a raw lat/lng string\"]\n):\n    \"\"\"\n    If the user provides the incident location in Google Maps URL, a long URL with coordinates,\n    or a raw lat/lng string format,this function will extract the latitude and longitude and\n    returns the reverse geocoded address using the Nominatim OpenStreetMap API. The function\n    handles both short and long URLs, and it can resolve short URLs to their long form. The\n    function also includes error handling for network issues and invalid input formats.\n\n    Args:\n        map_string: A Google Maps URL, a long URL with coordinates, or a raw lat/lng string.\n\n    Returns:\n        A JSON string containing the reverse geocoded address or an error message.\n    \"\"\"\n\n    headers = {\n        \"User-Agent\": \"Mozilla/5.0 (compatible; YourAppName/1.0)\",\n        \"Content-Type\": \"application/json\",\n    }\n\n    if \"maps.app.goo.gl\" in map_string:\n        try:\n            response = requests.get(map_string, headers=headers, allow_redirects=True)\n            map_string = response.url\n        except Exception as e:\n            return f\"Failed to resolve short URL: {e}\"\n\n    match = re.search(r'@(-?\\d+\\.\\d+),(-?\\d+\\.\\d+)', map_string) or \\\n            re.match(r'(-?\\d+\\.\\d+),\\s*(-?\\d+\\.\\d+)', map_string)\n\n    if not match:\n        return \"Could not extract coordinates from input.\"\n\n    lat, lng = match.groups()\n    url = f\"https://nominatim.openstreetmap.org/reverse?format=json&lat={lat}&lon={lng}&zoom=18&addressdetails=1\"\n\n    try:\n        response = requests.get(url, headers=headers, timeout=30)\n        if response.status_code != 200:\n            return \"Failed\"\n        response_json = response.json()\n        return json.dumps(response_json, indent=2)\n    except Exception as e:\n        return json.dumps({\"status\": \"Failed\", \"error\": str(e)}, indent=2)", "description": "get_incident_location function extracts the latitude and longitude from a user shared Google Maps URL, a long URL with coordinates, or a raw lat/lng string. It then returns the reverse geocoded address using the Nominatim OpenStreetMap API.", "function_name": "get_incident_location"}