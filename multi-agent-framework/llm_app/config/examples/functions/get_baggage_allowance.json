{"function_id": "get_baggage_allowance", "code": "def get_baggage_allowance(\n    membership_type: Annotated[Literal[\"Classic\", \"Elite\", \"PremierElite\", \"MillionMiler\"], \"Mabuhay Miles Membership type\"],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids\"\"\"\n    try:\n        membership_details = {\n            \"Classic\": \"No specific additional baggage allowance mentioned for Classic Members in the provided text. Standard ticketed allowance applies.\",\n            \"Elite\": \"You'll receive an additional luggage allowance of ten (10) kg flying on Philippine Airlines (except to/from USA, Canada, Middle East, Japan, and Guam) and PAL Express.\",\n            \"PremierElite\": \"You'll receive an additional luggage allowance of thirty (30) kgs flying on Philippine Airlines/PAL Express for flights using weight system. You may also carry one (1) extra piece of luggage (based on your ticket's class of service) when flying to routes using the piece system.\",\n            \"MillionMiler\": \"Additional free baggage allowance: Plus 40kgs (on all weight-system flights), Plus 2pcs (on all piece-system flights, weight is subject to class of service availed), Plus 20kgs for sports equipment (on all flights).\",\n        }\n        return membership_details.get(membership_type, \"membership details not found [Classic, Elite, PremierElite, MillionMiler]\")\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "function to get baggage allowance based on Mabuhay Miles Membership type.", "function_name": "get_baggage_allowance"}