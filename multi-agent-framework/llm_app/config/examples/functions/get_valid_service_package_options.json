{"function_id": "get_valid_service_package_options", "code": "def get_valid_service_package_options(\n    Salons_Branch_Name: Annotated[str, \"A required parameter. Represents the valid salon location name selected by customer.\"],\n    Salon_Service: Annotated[list, \"An optional parameter. Represents list of valid salon services selected by the customer. If parameter provided, the function will filter and return only stylists who offer these specific services.\"] = [],\n    context_arguments:dict = {},\n) -> str:\n\n    body = {\n    \"business_id\": context_arguments.get(\"BusinessId\"),\n    \"chat_id\": context_arguments.get(\"chat_id\"),\n    \"Salons_Branch_Name\": Salons_Branch_Name,\n    \"Salon_Service\": Salon_Service\n\t}  \n    url = \"http://brook.dev-scc-demo.rozie.ai/phorest/get_valid_service_package_options\" \n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    if body:\n        param[\"data\"] = json.dumps(body)\n\n    try:\n        response = requests.post(**param)\n        if response.status_code not in [200, 201, 202]:\n            if response.json():\n                return \"Failed\"\n            return \"Failed\"\n        response_data = response.json()\n    except Exception as e:\n        print(\"Error in get_locations:\", e)\n        return f\"Failed {str(e)}\"\n\n    return json.dumps(response_data, indent=2)", "description": "Validates the service name provided by the customer against the list of valid service package options available at a specific salon branch. This ensures that the correct service name is consistently used throughout subsequent processes. This function returns validated service name, the list of valid service package options (if requested), or an error message if validation fails.", "function_name": "get_valid_service_package_options"}