{"function_id": "flight_check_in", "code": "def flight_check_in(\n    booking_reference_id: Annotated[\n        str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"\n    ],\n    seat_number: Annotated[\n        str, \"Seat number of passenger whose checking-in into the flight\"\n    ],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of customer ids\"\"\"\n    query_param = {\"pnr_id\": booking_reference_id}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_details_by_pnr\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    pnr_data = {}\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            pnr_data = response_json[0]\n        except Exception as e:\n            return \"Failed\", str(e)\n\n    for passenger in pnr_data[\"passengers\"]:\n        if passenger[\"seat\"] == seat_number:\n            passenger[\"checked_in\"] = True\n\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/update_pnr\"\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"data\": json.dumps(pnr_data)}\n    try:\n        response = requests.post(**param)\n        if response.status_code != 200:\n            return \"Failed\"\n        response_json = response.json()\n        return response_json\n    except Exception as e:\n        return \"Failed\", str(e)", "description": "The flight_check_in function is used to check in single passenger at a time to the flight.", "function_name": "flight_check_in"}