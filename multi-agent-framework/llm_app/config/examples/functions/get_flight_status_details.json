{"function_id": "get_flight_status_details", "code": "def get_flight_status_details(\n    flight_id: Annotated[str, \"All Capital alphanumeric ID exactly 5 characters long, no spaces, matching regex: FL\\\\d{3}[A-Z]\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    query_param = {\n        \"flight_id\": flight_id.upper()\n\t}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/flight/flight-status\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n            \"params\": query_param\n    }\n    result = None\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            result = response.json()\n        except Exception as e:\n            return \"Failed\", str(e)\n    if result.get(\"status\") == \"Success\":\n        result = result.get(\"flight_data\")\n    return result", "description": "The get_flight_status_details returns the status of particular flight. It includes delay status.", "function_name": "get_flight_status_details"}