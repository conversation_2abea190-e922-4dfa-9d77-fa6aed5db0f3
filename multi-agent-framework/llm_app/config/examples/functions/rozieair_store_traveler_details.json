{"function_id": "rozieair_store_traveler_details", "code": "def rozieair_store_traveler_details(\n    traveler_first_name: Annotated[str, \"Customer's first name.\"],\n    traveler_last_name: Annotated[str, \"Customer's last name.\"],\n    traveler_date_of_birth: Annotated[str, \"Customer's date of birth.\"],\n    traveler_gender: Annotated[str, \"Customer's gender.\"],\n    traveler_email: Annotated[str, \"Customer's email address.\"],\n    traveler_phone_number: Annotated[\n        str, \"Customer's phone number without country code, only numeric string.\"\n    ],\n    traveler_country_calling_code: Annotated[str, \"Customer's country calling code.\"],\n    traveler_nationality: Annotated[str, \"Customer's nationality.\"],\n    traveler_passport_number: Annotated[str, \"Customer's passport number.\"],\n    traveler_passport_expiry_date: Annotated[str, \"Customer's passport expiry date.\"],\n    traveler_id: Annotated[str, \"Customer's unique identifier.\"],\n    context_arguments: dict = {},\n) -> dict:\n    \"\"\"Stores the traveler details in context arguments.\"\"\"\n    traveler_details = {}\n    if context_arguments.get(\"traveler_details\"):\n        traveler_details = context_arguments[\"traveler_details\"]\n\n    traveler_details[traveler_id] = {\n        \"first_name\": traveler_first_name,\n        \"last_name\": traveler_last_name,\n        \"date_of_birth\": traveler_date_of_birth,\n        \"gender\": traveler_gender,\n        \"email\": traveler_email,\n        \"phone_number\": traveler_phone_number,\n        \"country_calling_code\": traveler_country_calling_code,\n        \"nationality\": traveler_nationality,\n        \"passport_number\": traveler_passport_number,\n        \"passport_expiry_date\": traveler_passport_expiry_date,\n        \"id\": traveler_id,\n    }\n\n    return {\n        \"llm_result\": \"Traveler details stored successfully.\",\n        \"context_arguments\": {\n            \"traveler_details\": traveler_details\n        }\n    }", "description": "Function must be called every time customer provides traveler details. This will store the details to be referred later.", "function_name": "rozieair_store_traveler_details", "reflect_tool_result": false}