{"function_id": "get_client_active_bookings", "code": "def get_client_active_bookings(\n    phone_number: Annotated[str, \"The phone number of the user/client. This is a required field and must be provided. \"],\n    context_arguments: dict = {} \n)-> str:\n    \"\"\"Generic POST request handler for Phorest API interactions.\"\"\"\n    body = {\n        \"business_id\": context_arguments.get(\"BusinessId\"),\n        \"phone_number\": phone_number,\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n\t}\n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/get_client_service_history\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n        }\n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The get_client_active_bookings function helps you find upcoming active bookings/appointments of particular user/client. You need to provide user phone number from which the bookings were made, which is required. This function returns the active appointments with there details.", "function_name": "get_client_active_bookings"}