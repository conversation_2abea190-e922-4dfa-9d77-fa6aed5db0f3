{"function_id": "get_customer_id_by_pnr", "code": "def get_customer_id_by_pnr(\n    booking_reference_id: Annotated[str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"],\n    context_arguments: dict = {}\n)-> str:\n    query_param = {\n        \"pnr_id\": booking_reference_id.upper()\n\t}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/pnr/get_pnr_for_update\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\n            \"url\": url,\n            \"timeout\": 30,\n            \"headers\": headers,\n            \"params\": query_param\n    }\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps({\n                \"customer_id\": response_json[\"customer_id\"]\n            }, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "Use this function to get customer id using pnr.", "function_name": "get_customer_id_by_pnr"}