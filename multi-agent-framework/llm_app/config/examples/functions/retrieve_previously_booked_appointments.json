{"function_id": "retrieve_previously_booked_appointments", "code": "def retrieve_previously_booked_appointments(\n    phone_number: Annotated[str, \"The phone number of the client whose appointment history needs to be retrieved.\"],\n    stylist_name: Annotated[str | None, \"The name of the stylist to filter appointments by.\"] = None,\n    service_name: Annotated[str | None, \"The name of the service to filter appointments by.\"] = None,\n    context_arguments: dict = {}\n) -> str:\n    \"\"\"\n    Retrieves the previously booked appointments for a client from the Phorest system.\n    \n    Args:\n        phone_number: <PERSON>lient's phone number to lookup their appointment history\n        stylist_name: Optional stylist name to filter appointments\n        service_name: Optional service name to filter appointments\n        context_arguments: Additional context parameters including chat_id\n    \n    Returns:\n        str: JSON string containing the appointment history or error message\n    \"\"\"\n    body = {\n        \"business_id\": context_arguments.get(\"BusinessId\"),\n        \"phone_number\": phone_number,\n        \"chat_id\": context_arguments.get(\"chat_id\"),\n        \"stylist_name\": stylist_name,\n        \"service_name\": service_name\n    }\n    \n    url = \"https://brook.dev-scc-demo.rozie.ai/phorest/retrieve_previously_booked_appointments\"\n    headers = {\"Content-Type\": \"application/json\"}\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)\n", "description": "This function used for retrieving client appointment history. Returns JSON with past appointments including dates, times, services, and stylist details. This function only returns past information, not future appointments.", "function_name": "retrieve_previously_booked_appointments"}