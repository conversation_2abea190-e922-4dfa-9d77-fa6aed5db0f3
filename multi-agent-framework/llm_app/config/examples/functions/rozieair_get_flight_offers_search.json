{"function_id": "rozieair_get_flight_offers_search", "code": "def rozieair_get_flight_offers_search(\n    origin: Annotated[str, \"Origin airport code (e.g., PAR)\"],\n    destination: Annotated[str, \"Destination airport code (e.g., ICN)\"],\n    departure_date: Annotated[str, \"Departure date (YYYY-MM-DD)\"],\n    return_date: Annotated[str, \"Return date for round trips (YYYY-MM-DD)\"] = None,\n    adults: Annotated[int, \"Number of adult passengers\"] = 1,\n    children: Annotated[int, \"Number of child passengers\"] = 0,\n    infants: Annotated[int, \"Number of infant passengers\"] = 0,\n    context_arguments: dict = {}\n) -> dict:\n    \"\"\"Search for flight offers using the flight offers search API.\"\"\"\n    query_params = {\n        \"origin\": origin.upper(),\n        \"destination\": destination.upper(),\n        \"departure_date\": departure_date,\n        \"adults\": adults,\n        \"children\": children,\n        \"infants\": infants,\n        \"max_offers\": 3\n    }\n    \n    if return_date:\n        query_params[\"return_date\"] = return_date\n\n    url = \"https://rozie-amadeus.dev-scc-demo.rozie.ai/api/v1/flight-offers-search\"\n    headers = {\"accept\": \"application/json\"}\n    \n    try:\n        response = requests.get(url, headers=headers, params=query_params, timeout=30)\n        if response.status_code != 200:\n            return {\n                \"llm_result\": {\"error\": f\"Flight offer API error: {response.status_code}\"},\n                \"custom_results\": []\n            }\n        \n        result = response.json()\n        \n        # Generate HTML content\n        html_parts = ['<div class=\"container\">']\n        for idx, offer in enumerate(result.get(\"data\", []), 1):\n            price = offer.get(\"price\", {}).get(\"total\", \"0.00\")\n            currency = offer.get(\"price\", {}).get(\"currency\", \"EUR\")\n            itineraries = offer.get(\"itineraries\", [])\n            if not itineraries:\n                continue\n            outbound = itineraries[0]\n            return_trip = itineraries[1] if len(itineraries) > 1 else None\n            origin_code = outbound[\"segments\"][0].get(\"departure\", {}).get(\"iataCode\", \"\")\n            dest_code = outbound[\"segments\"][-1].get(\"arrival\", {}).get(\"iataCode\", \"\")\n            outbound_duration = outbound.get(\"duration\", \"PT0H0M\").replace(\"PT\", \"\").replace(\"H\", \"h \").replace(\"M\", \"m\")\n            return_duration = return_trip.get(\"duration\", \"PT0H0M\").replace(\"PT\", \"\").replace(\"H\", \"h \").replace(\"M\", \"m\") if return_trip else \"\"\n            stops_count = max(len(outbound.get(\"segments\", [])) - 1, 0)\n            stops_str = f\"{stops_count} Stop\" + (\"\" if stops_count == 1 else \"s\")\n            # Accordion summary\n            html_parts.append(f'<details{\" open\" if idx == 1 else \"\"}>')\n            html_parts.append(f'<summary class=\"accordion\">')\n            html_parts.append(f'<div class=\"label\">#{idx}</div>')\n            html_parts.append(f'<div class=\"summary-info\">')\n            html_parts.append(f'<span><strong>{origin_code} → {dest_code}</strong> • {outbound_duration}</span>')\n            if return_trip:\n                html_parts.append(f'<span>Return: {return_duration}</span>')\n            html_parts.append(f'<span>{stops_str}</span>')\n            html_parts.append(f'</div>')\n            html_parts.append(f'<div class=\"price\">{currency} {price}</div>')\n            html_parts.append(f'<div class=\"arrow\">{\"▲\" if idx == 1 else \"▼\"}</div>')\n            html_parts.append(f'</summary>')\n            # Panel\n            html_parts.append(f'<div class=\"panel\">')\n            html_parts.append('<div class=\"panel-content\">')\n\n            html_parts.append('<div class=\"segment-wrapper\">')\n            html_parts.append('<div class=\"segment-container\">')\n            for segment in outbound.get(\"segments\", []):\n                departure = segment.get(\"departure\", {})\n                arrival = segment.get(\"arrival\", {})\n                try:\n                    departure_time = datetime.fromisoformat(departure.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                except Exception:\n                    departure_time = departure.get(\"at\", \"\") or \"N/A\"\n                try:\n                    arrival_time = datetime.fromisoformat(arrival.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                except Exception:\n                    arrival_time = arrival.get(\"at\", \"\") or \"N/A\"\n                flight_number = f\"{segment.get('carrierCode', '')} {segment.get('number', '')}\".strip()\n                aircraft = segment.get(\"aircraft\", {}).get(\"code\", \"\")\n                html_parts.append('<div class=\"timeline-item\">')\n                html_parts.append('<div class=\"timeline-circle\"></div>')\n                html_parts.append(f'<div class=\"timeline-location\">{departure.get(\"iataCode\", \"\")}</div>')\n                html_parts.append(f'<div class=\"timeline-time\">{departure_time}</div>')\n                html_parts.append('</div>')\n                html_parts.append('<div class=\"timeline-connector\">')\n                html_parts.append('<div class=\"timeline-dots\"></div>')\n                html_parts.append('</div>')\n                html_parts.append('<div class=\"timeline-item\">')\n                html_parts.append('<div class=\"timeline-circle\"></div>')\n                html_parts.append(f'<div class=\"timeline-location\">{arrival.get(\"iataCode\", \"\")}</div>')\n                html_parts.append(f'<div class=\"timeline-time\">{arrival_time}</div>')\n                html_parts.append('</div>')\n                html_parts.append('<div class=\"segment-detail\">')\n                html_parts.append(f'<p>Flight {flight_number} • {aircraft}</p>')\n                html_parts.append('</div>')\n            html_parts.append('</div></div>')  # Close segment-wrapper\n\n            if return_trip:\n                html_parts.append('''\n        <div class=\"separator\">\n          <div class=\"dotted-line\"></div>\n          <div class=\"return-label\">Return Flight</div>\n          <div class=\"dotted-line\"></div>\n        </div>\n      ''')\n\n                html_parts.append('<div class=\"segment-wrapper\">')\n                html_parts.append('<div class=\"segment-container\">')\n                for segment in return_trip.get(\"segments\", []):\n                    departure = segment.get(\"departure\", {})\n                    arrival = segment.get(\"arrival\", {})\n                    try:\n                        departure_time = datetime.fromisoformat(departure.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                    except Exception:\n                        departure_time = departure.get(\"at\", \"\") or \"N/A\"\n                    try:\n                        arrival_time = datetime.fromisoformat(arrival.get(\"at\", \"\")).strftime(\"%b %d, %Y – %H:%M\")\n                    except Exception:\n                        arrival_time = arrival.get(\"at\", \"\") or \"N/A\"\n                    flight_number = f\"{segment.get('carrierCode', '')} {segment.get('number', '')}\".strip()\n                    aircraft = segment.get(\"aircraft\", {}).get(\"code\", \"\")\n                    html_parts.append('<div class=\"timeline-item\">')\n                    html_parts.append('<div class=\"timeline-circle\"></div>')\n                    html_parts.append(f'<div class=\"timeline-location\">{departure.get(\"iataCode\", \"\")}</div>')\n                    html_parts.append(f'<div class=\"timeline-time\">{departure_time}</div>')\n                    html_parts.append('</div>')\n                    html_parts.append('<div class=\"timeline-connector\">')\n                    html_parts.append('<div class=\"timeline-dots\"></div>')\n                    html_parts.append('</div>')\n                    html_parts.append('<div class=\"timeline-item\">')\n                    html_parts.append('<div class=\"timeline-circle\"></div>')\n                    html_parts.append(f'<div class=\"timeline-location\">{arrival.get(\"iataCode\", \"\")}</div>')\n                    html_parts.append(f'<div class=\"timeline-time\">{arrival_time}</div>')\n                    html_parts.append('</div>')\n                    html_parts.append('<div class=\"segment-detail\">')\n                    html_parts.append(f'<p>Flight {flight_number} • {aircraft}</p>')\n                    html_parts.append('</div>')\n                html_parts.append('</div></div>')  # Close segment-wrapper\n\n            offer_id = offer.get(\"id\", \"\")\n            html_parts.append(f'''\n    <div class=\"select-button-wrapper\">\n      <button type=\"button\" onclick=\"\n        const msg = 'Flight selected {offer_id}';\n        const shell = document.querySelector('rozieai-webchat')?.shadowRoot\n          ?.querySelector('webchat-shell')?.shadowRoot;\n        const input = shell?.querySelector('textarea.user-input');\n        const button = shell?.querySelector('button.submit-btn');\n        if (!input || !button) {{\n          alert('Simulated message: ' + msg);\n          return console.warn('RozieAI input or button not found. Running in test mode.');\n        }}\n        input.value = msg;\n        input.dispatchEvent(new Event('input', {{ bubbles: true }}));\n        setTimeout(() => button.click(), 200);\n      \">Select Flight</button>\n    </div>\n  ''')\n            html_parts.append('</div>')\n            html_parts.append('</div>')\n            html_parts.append('</details>')\n        html_parts.append('</div>')\n        # CSS from template\n        css = \"\"\"\n    <style>\n    body {\n      margin: 0;\n      background-color: #f9f9f9;\n      font-family: 'Segoe UI', sans-serif;\n      font-size: 12px;\n      color: #333;\n    }\n    .container {\n      max-width: 100%;\n      padding: 8px;\n      box-sizing: border-box;\n    }\n    summary.accordion {\n      background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);\n      color: white;\n      cursor: pointer;\n      padding: 10px 12px;\n      border-radius: 6px;\n      font-size: 12px;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 6px;\n    }\n    summary::-webkit-details-marker {\n      display: none;\n    }\n    summary::marker {\n      content: \"\";\n    }\n    details[open] summary.accordion {\n      background: linear-gradient(45deg, rgb(83, 74, 216) -23.5%, rgb(131, 76, 157) 107.38%);\n    }\n    .label {\n      font-weight: bold;\n      background-color: #fff;\n      color: #834C9D;\n      border-radius: 4px;\n      padding: 1px 5px;\n      font-size: 11px;\n      margin-right: 8px;\n    }\n    .summary-info {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 0.5rem;\n      flex: 1;\n    }\n    .summary-info span {\n      font-size: 11px;\n      white-space: nowrap;\n    }\n    .price {\n      font-size: 12px;\n      color: #b8ffcc;\n      font-weight: bold;\n      margin-left: auto;\n    }\n    .arrow {\n      font-size: 10px;\n      margin-left: 6px;\n      transform: rotate(0deg);\n      transition: transform 0.2s ease;\n    }\n    details[open] .arrow {\n      transform: rotate(180deg);\n    }\n    .panel {\n      background: #f0f0f8;\n      border: 1px solid #ccc;\n      border-top: none;\n      border-radius: 0 0 6px 6px;\n      padding: 10px;\n      margin-top: -4px;\n    }\n    .panel-content {\n      display: flex;\n      flex-direction: column;\n    }\n    .segment-wrapper {\n      margin-bottom: 16px;\n    }\n    .segment-container {\n      flex: 1;\n    }\n    /* Removed old .segment styles */\n    .separator {\n      display: flex;\n      align-items: center;\n      margin: 12px 0;\n    }\n    .dotted-line {\n      flex-grow: 1;\n      border-bottom: 1px dotted #aaa;\n    }\n    .return-label {\n      padding: 0 8px;\n      font-size: 11px;\n      color: #555;\n      font-weight: bold;\n    }\n    .select-button-wrapper {\n      text-align: center;\n      margin-top: 20px;\n    }\n    .select-button-wrapper button {\n      background: #534ad8;\n      color: white;\n      padding: 6px 12px;\n      border: none;\n      border-radius: 4px;\n      cursor: pointer;\n      font-size: 12px;\n    }\n    .timeline-item {\n      display: flex;\n      align-items: center;\n      margin: 6px 0;\n      gap: 6px;\n    }\n    .timeline-circle {\n      width: 10px;\n      height: 10px;\n      border: 2px solid #777;\n      border-radius: 50%;\n      background: white;\n    }\n    .timeline-connector {\n      margin-left: 3px;\n    }\n    .timeline-dots {\n      border-left: 2px dotted #777;\n      height: 24px;\n      margin: 4px 0;\n    }\n    .timeline-time, .timeline-location {\n      font-size: 13px;\n      color: #333;\n      font-weight: 500;\n    }\n    .segment-detail {\n      font-size: 11px;\n      margin-bottom: 8px;\n    }\n  </style>\n        \"\"\"\n        \n        return {\n            \"llm_result\": result,\n            \"context_arguments\": {\n                \"flight_offers\": result.get(\"data\", [])\n            },\n            \"custom_results\": [\n                {\n                    \"response_type\": \"html\",\n                    \"cardName\": \"flight-offers\",\n                    \"body\": \"\".join(html_parts),\n                    \"css\": css\n                }\n            ]\n        }\n        \n    except Exception as e:\n        return {\n            \"llm_result\": {\"error\": str(e)},\n            \"custom_results\": []\n        }", "description": "Search for flight offers and return a formatted card with flight details. The function returns both raw API data and a formatted HTML card showing prices, durations, stops, and flight information.", "function_name": "rozieair_get_flight_offers_search", "reflect_tool_result": false}