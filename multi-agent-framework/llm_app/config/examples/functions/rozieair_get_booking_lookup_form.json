{"function_id": "rozieair_get_booking_lookup_form", "code": "def rozieair_get_booking_lookup_form(\n    context_arguments: dict = {}\n) -> dict:\n    \"\"\"Return a flight booking lookup form for booking reference and last name.\"\"\"\n    return {\n        \"llm_result\": {},\n        \"custom_results\": [\n            {\n                \"response_type\": \"html\",\n                \"cardName\": \"flight-booking-lookup-form\",\n                \"body\": '''\n<div class=\"flight-form-container\">\n  <form onsubmit=\"\n    event.preventDefault();\n    const f = this;\n    const data = {\n      bookingReference: f.bookingReference.value.toUpperCase(),\n      lastName: f.lastName.value\n    };\n\n    const msg =\n      'Booking details submitted.\\\\n' +\n      'Booking Reference: ' + data.bookingReference + '\\\\n' +\n      'Last Name: ' + data.lastName;\n\n    const shell = document.querySelector('rozieai-webchat')?.shadowRoot\n      ?.querySelector('webchat-shell')?.shadowRoot;\n    const input = shell?.querySelector('textarea.user-input');\n    const button = shell?.querySelector('button.submit-btn');\n    if (!input || !button) return console.error('❌ Input or button not found');\n    input.value = msg;\n    input.dispatchEvent(new Event('input', { bubbles: true }));\n    setTimeout(() => button.click(), 200);\n  \">\n    \n    <label>Booking Reference Number (PNR)\n      <input type=\"text\" name=\"bookingReference\" placeholder=\"e.g., ABC123\" pattern=\"^[A-Z0-9]{5,8}$\" required>\n    </label>\n    <label>Last Name\n      <input type=\"text\" name=\"lastName\" placeholder=\"e.g., Smith\" required>\n    </label>\n    <button type=\"submit\">Submit</button>\n  </form>\n</div>\n''',\n                \"css\": \"\"\"\n<style>\n  .flight-form-container {\n    padding: 24px;\n    max-width: 400px;\n    margin: auto;\n    background-color: #7a3fa5;\n    border-radius: 16px;\n    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);\n    border: 1px solid #eee;\n    font-family: 'Segoe UI', sans-serif;\n  }\n\n  .flight-form-container h3 {\n    font-size: 20px;\n    margin-bottom: 16px;\n    color: #2e2e2e;\n    text-align: center;\n  }\n\n  .flight-form-container label {\n    display: block;\n    margin-bottom: 14px;\n    font-size: 14px;\n    color: #444;\n  }\n\n  .flight-form-container input {\n    width: 100%;\n    padding: 10px 12px;\n    margin-top: 6px;\n    font-size: 14px;\n    border: 1px solid #ccc;\n    border-radius: 8px;\n    box-sizing: border-box;\n    background-color: #fff;\n  }\n\n  .flight-form-container button {\n    margin-top: 20px;\n    padding: 12px;\n    width: 100%;\n    background: linear-gradient(to right, #7a3fa5, #4f71ec);\n    color: white;\n    border: none;\n    font-size: 16px;\n    border-radius: 8px;\n    cursor: pointer;\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n    font-weight: 600;\n  }\n\n  .flight-form-container button:hover {\n    transform: scale(1.02);\n    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n  }\n</style>\n\"\"\"\n            }\n        ]\n    }", "description": "Return a traveler details form for the given traveler_id. The form allows the user to fill out and submit traveler information such as name, date of birth, gender, email, and phone number.", "function_name": "rozieair_get_booking_lookup_form", "reflect_tool_result": false}