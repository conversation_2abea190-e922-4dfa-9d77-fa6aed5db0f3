{"function_id": "email_prohibited_item_list", "code": "def email_prohibited_item_list(\n    customer_id: Annotated[str, \"Valid customer Id fetched using tool execution.\"],\n    context_arguments: dict = {}\n)-> str:\n    \"\"\"This will get the list of customer ids \"\"\"\n    query_param = {\n        \"customer_id\": customer_id\n    }\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/notification/email/send-prohibited-item-doc-link\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    if query_param:\n        try:\n            response = requests.post(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return response_json\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "This function share a email with prohibited items list document.", "function_name": "email_prohibited_item_list"}