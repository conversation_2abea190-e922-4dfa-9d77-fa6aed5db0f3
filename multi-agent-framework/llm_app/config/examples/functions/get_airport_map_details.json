{"function_id": "get_airport_map_details", "code": "def get_airport_map_details(\n    airport_map_input: Annotated[List[str], \"List of city names or airport codes.\"],\n    input_type: Annotated[str, \"Specifies if the input is 'city' or 'code'.\"],\n    context_arguments: dict = {} \n) -> str:\n    \"\"\"Generic POST request handler for Airport Map data retrieval.\"\"\"\n    \n    body = {\n        \"airport_map_input\": airport_map_input,\n        \"input_type\": input_type,\n        \"chat_id\": context_arguments.get(\"chat_id\")\n    }\n\n    url = \"https://rozie-air.dev-scc-demo.rozie.ai/airport_map/get_airport_map_details\"\n    \n    # Set the headers for the request\n    headers = {\"Content-Type\": \"application/json\",\"access_token\":\"Test@123\"}\n    \n    # Prepare parameters for the POST request\n    param = {\n        \"url\": url,\n        \"timeout\": 30,\n        \"headers\": headers,\n    }\n    \n    if body:\n        param[\"data\"] = json.dumps(body)\n        \n        try:\n            response = requests.post(**param)\n            \n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        \n        except Exception as e:\n            return \"Failed\", str(e)\n ", "description": "The get_airport_map_details function retrieves detailed information about airports based on either city names or airport codes. It accepts two parameters: airport_map_input, which is a list of either city names or airport codes, and input_type, which specifies whether the input list contains city names or airport codes. This function helps users get comprehensive information about airports, including their locations, facilities, and other relevant details.", "function_name": "get_airport_map_details"}