{"function_id": "get_available_seats", "code": "def get_available_seats(\n    flight_id: Annotated[\n        str, \"All alphanumeric ID exactly 6 characters long, no spaces, matching regex: ^[A-Z0-9]{6}$\"\n    ],\n    context_arguments: dict = {},\n) -> str:\n    \"\"\"This will get the list of available seats in flight\"\"\"\n    query_param = {\"flight_id\": flight_id}\n    url = \"http://rozie-air.dev-scc-demo.rozie.ai/flight/available-seats\"\n    headers = {\"Content-Type\": \"application/json\", \"access_token\": \"Test@123\"}\n    param = {\"url\": url, \"timeout\": 30, \"headers\": headers, \"params\": query_param}\n    if query_param:\n        try:\n            response = requests.get(**param)\n            if response.status_code != 200:\n                return \"Failed\"\n            response_json = response.json()\n            return json.dumps(response_json, indent=2)\n        except Exception as e:\n            return \"Failed\", str(e)", "description": "The get_available_seats returns the list of unoccupied seats with their details like type, class and price. ", "function_name": "get_available_seats"}