{"roster_id": "salon718-Dev", "time_zone": "America/New_York", "use_case_domain": "Hair Salon", "company_name": "[Salon]718", "avatar_name": "brook", "knowledge_base_agent": true, "knowledge_base_config": {"illuminar_config": {"base_url": "https://api-manager-sandbox.rozie.ai/event-adapter", "search_url": "/v1/adapters-illuminar", "application_id": "application_f7421e21-83c9-4f99-ba9c-f308abd129e1", "api_key": "5b5fadd08ddc4295abfa854244cbfbb2", "enhancement": false, "params": {"num_of_results": 2, "folders": ["Salon718"]}}, "function_map": [], "knowledge_base_topics": ["Salon Working Hours", "Salon Policies"], "knowledge_snippets": []}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to [salon]718 {Location}! I'm <PERSON>, a virtual assistant. How can I help you today?"}], "profile_found": [{"type": "text", "message": "Hi {UserName}! Welcome back to [salon]718 {Location}! This is <PERSON>, your virtual assistant. How can I help you today?"}]}, "workflows": {"Salon_New_Booking": ["Trigger this workflow if the customer's intent revolves around booking a new appointment or service related to hair salon.", "scope exclusions: adding service, modification to appointment"], "Salon_Cancel_Booking": ["Trigger this workflow if the customer requests to cancel an existing confirmed hair salon appointment."], "Salon_Connect_to_live_agent": ["Trigger this workflow if the customer asks to speak to a live agent or salon representative."], "Salon_Reschedule_Booking": ["Trigger this workflow if the customer's intent involves rescheduling(changing the date, time or stylist) an existing confirmed hair salon appointment.", "scope exclusions: adding service"], "Salon_Appointment_Inquiry": ["Trigger this workflow if the customer's intent involves asking about details of upcoming hair salon appointments, such as dates, times, or services.", "scope exclusions: adding service, modification to appointment"], "Salon_Customer_Running_Late": ["Trigger this workflow if the customer's intent involves saying they are running late for the appointment.", "scope exclusions: modification to appointment, rescheduling an appointment"]}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_Base": "gpt-4.1", "Unknown_Info": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1"}}