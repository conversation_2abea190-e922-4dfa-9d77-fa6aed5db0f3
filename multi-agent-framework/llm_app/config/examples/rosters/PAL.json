{"roster_id": "PAL", "time_zone": "America/New_York", "use_case_domain": "Airline", "company_name": "Philippine Airline [PAL]", "avatar_name": "<PERSON>", "knowledge_base_agent": true, "knowledge_base_config": {"illuminar_config": {"base_url": "https://api-manager-sandbox.rozie.ai/event-adapter", "search_url": "/v1/adapters-illuminar", "application_id": "application_f498d5df-6ee6-4130-9922-7b8a04955f97", "api_key": "5b5fadd08ddc4295abfa854244cbfbb2", "enhancement": false, "params": {"num_of_results": 2, "folders": ["PAL"]}}, "function_map": ["get_baggage_allowance"], "knowledge_base_topics": [], "knowledge_snippets": []}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to Philippine Airlines! I'm <PERSON>, a virtual concierge for guests. Please describe how I can help you?"}]}, "workflows": {"Flight_Check_In": ["If customer conversation is about checking into the flight then trigger this workflow."], "Baggage_Support": ["If customer conversation is about checking the baggage's status then trigger this workflow."], "Flight_Status": ["If customer conversation is about checking the flight's status then trigger this workflow."], "Case_Status": ["If customer conversation is about update on customer's support case then trigger this workflow."], "Name_Correction": ["If customer conversation is about correcting their name in ticket then trigger this workflow."]}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_Base": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1"}}