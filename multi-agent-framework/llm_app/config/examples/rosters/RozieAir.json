{"roster_id": "RozieAir", "time_zone": "America/New_York", "use_case_domain": "Airline", "company_name": "Rozie Airline", "avatar_name": "<PERSON>", "knowledge_base_agent": true, "knowledge_base_config": {"illuminar_config": {"base_url": "https://api-manager-sandbox.rozie.ai/event-adapter", "search_url": "/v1/adapters-illuminar", "application_id": "application_9f6d6202-f5d9-445d-b856-82b44844f0d4", "api_key": "5b5fadd08ddc4295abfa854244cbfbb2", "enhancement": false, "params": {"num_of_results": 2, "folders": ["Rozie <PERSON>"]}}, "function_map": [], "knowledge_base_topics": [], "knowledge_snippets": [], "instructions": ["When encountering an SSR (Special Service Request) code in a query, response, or document, do the following:", " 1. Always detect the SSR code (e.g., WCHR, VGML, UMNR)", " 2. append or display the full meaning of the code in parentheses next to it.", "This structure defines user topics (intents) and the specific follow-up entities you should ask for in order to gather more information and improve the accuracy of your response.\n\n intents:\n  baggage_allowance:\n    ask_to_improve_answer:\n      - origin_airport\n      - destination_airport\n      - fare_type\n      - aeroplan_status\n\n  baggage_size:\n    ask_to_improve_answer:\n      - fare_type\n      - airplane_type"]}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to Rozie Airlines! I'm <PERSON>, a virtual concierge for guests. Please describe how I can help you?"}]}, "workflows": {"RozieAir_Flight_Booking_Flow": ["If customer conversation is about searching for flights or booking tickets, then trigger this workflow. Searches 400+ airlines for best fares, supports one-way/multi-city routes, and provides flight details including prices"], "RozieAir_Flight_Status": ["If customer conversation is about checking the flight's status then trigger this workflow."], "Case_Status": ["If customer conversation is about update on customer's support case then trigger this workflow."], "Name_Correction": ["If customer conversation is about correcting their name in ticket then trigger this workflow."], "Prebook_Meal": ["If customer conversation is about pre booking the meal then trigger this workflow."], "Baggage_Support": ["If customer conversation is about checking the baggage's status then trigger this workflow."], "RozieAir_Add_Wheelchair_Assistance": ["If customer conversation is about wheelchair assistance then trigger this workflow."]}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_Base": "gpt-4.1", "Unknown_Info": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1"}}