{"roster_id": "RozieAir_V2", "time_zone": "America/New_York", "use_case_domain": "Airline", "company_name": "Rozie Airline", "avatar_name": "<PERSON>", "use_triage_agent_v2": true, "channel_guidelines": "Be professional, friendly, and efficient. Use clear language appropriate for airline customer service. Always acknowledge the customer's request before providing assistance.", "knowledge_base_agent": true, "knowledge_base_config": {"illuminar_config": {"base_url": "https://api-manager-sandbox.rozie.ai/event-adapter", "search_url": "/v1/adapters-illuminar", "application_id": "application_9f6d6202-f5d9-445d-b856-82b44844f0d4", "api_key": "5b5fadd08ddc4295abfa854244cbfbb2", "enhancement": false, "params": {"num_of_results": 2, "folders": ["Rozie <PERSON>"]}}, "function_map": [], "knowledge_base_topics": [], "knowledge_snippets": [], "instructions": ["When encountering an SSR (Special Service Request) code in a query, response, or document, do the following:", " 1. Always detect the SSR code (e.g., WCHR, VGML, UMNR)", " 2. append or display the full meaning of the code in parentheses next to it.", "This structure defines user topics (intents) and the specific follow-up entities you should ask for in order to gather more information and improve the accuracy of your response.\n\n intents:\n  baggage_allowance:\n    ask_to_improve_answer:\n      - origin_airport\n      - destination_airport\n      - fare_type\n      - aeroplan_status\n\n  baggage_size:\n    ask_to_improve_answer:\n      - fare_type\n      - airplane_type"]}, "welcome_message": {"default": [{"type": "text", "message": "Welcome to Rozie Airlines! I'm <PERSON>, your enhanced virtual concierge. I'm here to help you with all your airline needs. How can I assist you today?"}]}, "workflows": {"RozieAir_Flight_Booking_Flow": {"name": "Flight Booking", "description": "Search for flights and book tickets. Searches 400+ airlines for best fares, supports one-way/multi-city routes, and provides flight details including prices."}, "RozieAir_Flight_Status": {"name": "Flight Status", "description": "Check the current status of your flight including delays, gate information, and departure times."}, "Case_Status": {"name": "Case Status", "description": "Get updates on your existing support case or customer service inquiry."}, "Name_Correction": {"name": "Name Correction", "description": "Correct the name on your ticket or booking to match your identification."}, "Prebook_Meal": {"name": "Meal Prebooking", "description": "Pre-select and book your meal preferences for your upcoming flight."}, "Baggage_Support": {"name": "Baggage Support", "description": "Check baggage status, report lost luggage, or get information about baggage policies."}, "RozieAir_Add_Wheelchair_Assistance": {"name": "Wheelchair Assistance", "description": "Request wheelchair assistance or special mobility services for your travel."}}, "model": {"intent_detection_agent": "gpt-4.1", "response_builder_agent": "gpt-4.1", "conversation_status_agent": "gpt-4.1", "quick_response_builder_agent": "gpt-4.1", "memory_agent": "gpt-4.1", "Knowledge_Base": "gpt-4.1", "Unknown_Info": "gpt-4.1", "default": "gpt-4.1", "workflow_agent": "gpt-4.1", "triage_agent_v2": "gpt-4.1"}}