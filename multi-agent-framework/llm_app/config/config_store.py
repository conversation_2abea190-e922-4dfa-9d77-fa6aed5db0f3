import os
from llm_app.helpers.dynamo_helper import dynamo_resource_operation
from llm_app.helpers.utils import list_files_in_folder, read_config
from llm_app.helpers.request_helper import get_request
from llm_app.logging.logging_context import rozie_correlation_id_var, rozie_application_id_var
from llm_app.common.attributes import GEN_AGENT_ATTRIBUTES


def get_roster_config(roster_id):
    deployment_type = os.environ.get("DEPLOYMENT_TYPE", "LOCAL")
    if deployment_type == "AWS":
        primary_key = {"roster_id": roster_id}
        result = dynamo_resource_operation(
            operation="get_item",
            table_name=f"multi-agent-framework-resources-rosters-config-{os.environ.get('FASTAPI_ENV','dev')}",
            Key=primary_key,
        )
        return result
    elif deployment_type == "AZURE":
        base_url = os.getenv("STUDIO_GEN_AGENT_BASE_URL")
        endpoint = GEN_AGENT_ATTRIBUTES.get("roster_endpoint")
        url = f"{base_url}{endpoint}{roster_id}"
        headers = {
            "application-id": rozie_application_id_var.get(),
            "rozie-correlation-id": rozie_correlation_id_var.get(),
        }

        status, response = get_request(url, headers=headers)
        if status == "Failed":
            return None

        if response.get("roster_schema").get("roster_type") != GEN_AGENT_ATTRIBUTES.get(
                "roster_type"):
            return None

        roster_config = {
            "roster_id": response.get("roster_id"),
            "roaster_name": response.get("roster_name"),
            "avatar_name": response.get("roster_schema").get("avatar_name", ""),
            "company_name": response.get("roster_schema").get("company_name", ""),
            "knowledge_base_agent": response.get("roster_schema").get("knowledge_base", {}).get(
                "is_enabled", False),
            "knowledge_base_config": {},
            "time_zone": response.get("roster_schema").get("time_zone"),
            "use_case_domain": response.get("roster_schema").get("use_case_domain"),
            "welcome_message": response.get("roster_schema").get("welcome_message"),
            "workflows": response.get("roster_schema").get("workflows"),
            "roster_agents": response.get("roster_schema").get("roster_agents"),
            "model": {
                "intent_detection_agent": "gpt-4.1",
                "response_builder_agent": "gpt-4.1",
                "conversation_status_agent": "gpt-4.1",
                "quick_response_builder_agent": "gpt-4.1",
                "memory_agent": "gpt-4.1",
                "Knowledge_Base": "gpt-4.1",
                "Unknown_Info": "gpt-4.1",
                "default": "gpt-4.1",
                "workflow_agent": "gpt-4.1"
            }
        }
        if roster_config.get("knowledge_base_agent"):
            kb_config = response.get("roster_schema").get("knowledge_base")
            roster_config["knowledge_base_config"] = {
                "illuminar_config": {
                    "base_url": kb_config.get("base_url",
                                              "https://api-manager-sandbox.rozie.ai/event-adapter"),
                    "search_url": kb_config.get("search_url", "/v1/adapters-illuminar"),
                    "application_id": rozie_application_id_var.get(),
                    "api_key": kb_config.get("api_key", ""),
                    "enhancement": False,
                    "params": kb_config.get("params", {}),
                },
                "function_map": kb_config.get("function_map", []),
                "knowledge_base_topics": kb_config.get("knowledge_base_topics", []),
                "knowledge_snippets": kb_config.get("knowledge_snippets", []),
            }

        return roster_config
    else:
        folder_path = f"llm_app/config/examples/rosters"
        file_list = list_files_in_folder(folder_path)
        for _file in file_list:
            config = read_config(folder_path, _file)
            if config.get("roster_id") == roster_id:
                return config


def get_agent_config(agent_id):
    deployment_type = os.environ.get("DEPLOYMENT_TYPE", "LOCAL")
    if deployment_type == "AWS":
        primary_key = {"agent_id": agent_id}
        result = dynamo_resource_operation(
            operation="get_item",
            table_name=f"multi-agent-framework-resources-agents-config-{os.environ.get('FASTAPI_ENV','dev')}",
            Key=primary_key,
        )
        return result
    elif deployment_type == "AZURE":
        base_url = os.getenv("STUDIO_GEN_AGENT_BASE_URL")
        endpoint = GEN_AGENT_ATTRIBUTES.get("workflow_endpoint")
        url = f"{base_url}{endpoint}{agent_id}"
        headers = {
            "application-id": rozie_application_id_var.get(),
            "rozie-correlation-id": rozie_correlation_id_var.get()
        }

        status, response = get_request(url, headers=headers)
        if status == "Failed":
            return None

        if response.get("agent_bio").get("agent_type") != GEN_AGENT_ATTRIBUTES.get("roster_type"):
            return None

        agent_config = {
            "agent_id": response.get("agent_id"),
            "function_map": response.get("agent_bio").get("function_map"),
            "use_transferred_context": True,
            "agent_bio": {
                "agent_name": response.get("agent_name"),
                "agent_role": response.get("agent_bio").get("agent_role"),
                "agent_title": response.get("agent_bio").get("agent_title"),
                "initiation_guideline": response.get("agent_bio").get("initiation_guideline", []),
                "personality": response.get("agent_bio").get("personality"),
                "entities": response.get("agent_bio").get("entities", []),
                "conditional_actions": response.get("agent_bio").get("conditional_actions", []),
                "important_guidelines": response.get("agent_bio").get("important_guidelines", []),
            }
        }

        return agent_config
    else:
        folder_path = f"llm_app/config/examples/workflow_specialists"
        file_list = list_files_in_folder(folder_path)
        for _file in file_list:
            config = read_config(folder_path, _file)
            if config.get("agent_id") == agent_id:
                return config


def get_function_config(func_id):
    deployment_type = os.environ.get("DEPLOYMENT_TYPE", "LOCAL")
    if deployment_type == "AWS":
        primary_key = {"function_id": func_id}
        result = dynamo_resource_operation(
            operation="get_item",
            table_name=f"multi-agent-framework-resources-functions-config-{os.environ.get('FASTAPI_ENV','dev')}",
            Key=primary_key,
        )
        return result
    elif deployment_type == "AZURE":
        base_url = os.getenv("STUDIO_GEN_AGENT_BASE_URL")
        endpoint = GEN_AGENT_ATTRIBUTES.get("function_endpoint")
        url = f"{base_url}{endpoint}{func_id}"
        headers = {
            "application-id": rozie_application_id_var.get(),
            "rozie-correlation-id": rozie_correlation_id_var.get()
        }

        status, response = get_request(url, headers=headers)
        if status == "Failed":
            return None

        if response.get("api_schema").get("type") != GEN_AGENT_ATTRIBUTES.get("roster_type"):
            return None

        function_config = {
            "function_id": response.get("api_id"),
            "code": response.get("api_schema").get("code"),
            "description": response.get("api_schema").get("description"),
            "function_name": response.get("api_schema").get("ApiName"),
            "reflect_tool_result": response.get("api_schema").get("reflect_tool_result", True),
        }

        return function_config
    else:
        folder_path = f"llm_app/config/examples/functions"
        file_list = list_files_in_folder(folder_path)
        for _file in file_list:
            config = read_config(folder_path, _file)
            if config.get("function_name") == func_id:
                return config
