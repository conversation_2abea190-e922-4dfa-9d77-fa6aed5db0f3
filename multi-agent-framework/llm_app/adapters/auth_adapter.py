from fastapi import HTT<PERSON><PERSON>x<PERSON>, Security, status, WebSocket
from fastapi.security import <PERSON><PERSON>eyHeader
import os

API_KEY = os.environ.get("APP_API_KEY")
API_KEY_NAME = "access_token"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)


async def get_api_key(api_key_header: str = Security(api_key_header)):
    if api_key_header == API_KEY:
        return api_key_header
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )


async def ws_auth(websocket: WebSocket) -> bool:
    headers = websocket.headers
    token = headers.get(API_KEY_NAME)

    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return False

    if token != API_KEY:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return False
    return True
