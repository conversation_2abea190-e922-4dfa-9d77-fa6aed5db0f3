from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
import time

from loguru import logger

from llm_app.logging.logging_context import ( 
    rozie_correlation_id_var,
    rozie_application_id_var,
    client_var,
    application_var,
    environment_var,
    language_var,
    channel_var
)


class LoggingContextMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        headers = request.headers
        rozie_correlation_id = headers.get("rozie-correlation-id")
        rozie_application_id = headers.get("application-id")
        client = headers.get("client")
        application = headers.get("application")
        environment = headers.get("environment")
        language = headers.get("language")
        channel = headers.get("channel")

        tokens = []
        tokens.append(rozie_correlation_id_var.set(rozie_correlation_id))
        tokens.append(rozie_application_id_var.set(rozie_application_id))
        tokens.append(client_var.set(client))
        tokens.append(application_var.set(application))
        tokens.append(environment_var.set(environment))
        tokens.append(language_var.set(language))
        tokens.append(channel_var.set(channel))
        response = None
        try:
            response = await call_next(request)
        except Exception as e:
            logger.exception("Unhandled exception during request processing", status="FAILURE", errors=[str(e)])
            raise e from None
        finally:
            for token in reversed(tokens):
                var = token.var
                var.reset(token)

        return response
