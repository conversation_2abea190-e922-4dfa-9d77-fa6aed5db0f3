import sys
import os
import json
import uuid
from loguru import logger
import logging

from llm_app.logging.logging_context import (
    rozie_correlation_id_var,
    rozie_application_id_var,
    client_var,
    application_var,
    environment_var,
    language_var,
    conversation_id,
    user_id_var,
    channel_user_id_var,
)

from llm_app.common.attributes import CONSTANTS
from autogen_core import ROOT_LOGGER_NAME


# --- Configuration from Environment Variables ---
# Align with how CONSTANTS might be populated or use directly
SERVICE_NAME = CONSTANTS.get("SERVICE_NAME", "Rozie Agentic Framework")
SERVICE_VERSION = CONSTANTS.get("APP_VERSION", "0.1.0")
DEFAULT_ENVIRONMENT = os.getenv("FASTAPI_ENV", "dev")
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
# --- End Configuration ---


def format_record(record: dict) -> str:
    """Custom formatter for Loguru to produce the desired JSON structure."""
    format_dict = {
        "id": str(uuid.uuid4()),
        "timestamp": int(record["time"].timestamp() * 1000),
        "rozie_correlation_id": record["extra"].get("rozie_correlation_id"),
        "rozie_application_id": record["extra"].get("rozie_application_id"),
        "user_id": record["extra"].get("user_id"),
        "channel_user_id": record["extra"].get("channel_user_id"),
        "client": record["extra"].get("client"),
        "application": record["extra"].get("application"),
        "environment": record["extra"].get("environment") or DEFAULT_ENVIRONMENT,
        "language": record["extra"].get("language"),
        "service": record["extra"].get("service_name"),
        "conversation_id": record["extra"].get("conversation_id"),
        "channel": record["extra"].get("channel"),
        "service_version": record["extra"].get("service_version"),
        "log_level": record["level"].name,
        "log_message": record["message"],
        "data": record["extra"].get("data", []),
        "errors": record["extra"].get("errors", []),
        "status": record["extra"].get("status", "SUCCESS"),
    }

    debug_info = {
        "filename": record["file"].name,
        "func_name": record["function"],
        "level_name": record["level"].name,
        "line_no": record["line"],
        "module": record["module"],
        "process": record["process"].id,
        "thread": record["thread"].id,
    }
    if any(v is not None for v in debug_info.values()):  # Check if any value is non-None
        format_dict["debug_info"] = debug_info

    # Handle Exceptions
    if record["exception"]:
        exc_type, exc_value, _ = record["exception"]
        # Ensure errors is a list before appending
        if not isinstance(format_dict["errors"], list):
            try:
                # Attempt to convert existing value to list if not already
                format_dict["errors"] = list(format_dict["errors"])
            except TypeError:
                # Fallback if conversion fails
                format_dict["errors"] = [f"Original errors value type: {type(format_dict['errors'])}"]
        if exc_type:
            format_dict["errors"].append(f"Exception: {exc_type.__name__}: {exc_value}")
        if format_dict["status"] == "SUCCESS":
            format_dict["status"] = "FAILURE"

    # Serialize and return
    serialized = json.dumps(format_dict, default=str, ensure_ascii=False)
    record["extra"]["serialized"] = serialized
    return "{extra[serialized]}\n"


def setup_loguru_logging():
    """Configure Loguru logger."""
    logger.remove()  # Remove default handlers

    logger.add(
        sys.stdout,
        level=LOG_LEVEL,
        format=format_record,
        serialize=False,
    )

    # Automatically add contextvars and static info to 'extra'
    def patch_record(record):
        record["extra"]["rozie_correlation_id"] = rozie_correlation_id_var.get()
        record["extra"]["rozie_application_id"] = rozie_application_id_var.get()
        record["extra"]["user_id"] = user_id_var.get()
        record["extra"]["channel_user_id"] = channel_user_id_var.get()
        record["extra"]["client"] = client_var.get()
        record["extra"]["application"] = application_var.get()
        record["extra"]["environment"] = environment_var.get()
        record["extra"]["language"] = language_var.get()

        record["extra"]["conversation_id"] = conversation_id.get()

    logger.configure(
        patcher=patch_record,
        extra={  # Bind static info globally
            "service_name": SERVICE_NAME,
            "service_version": SERVICE_VERSION,
            # Add any other static info you want in every log
        },
    )

    # Silence Uvicorn and other standard library loggers if desired
    # to prevent non-JSON logs from them interfering.
    logging.getLogger("uvicorn").handlers.clear()
    logging.getLogger("uvicorn").propagate = False  # Prevent propagation to root logger
    logging.getLogger("uvicorn.access").handlers.clear()
    logging.getLogger("uvicorn.access").propagate = False

    logging.getLogger(ROOT_LOGGER_NAME).handlers.clear()
    logging.getLogger(ROOT_LOGGER_NAME).propagate = False
    # You might need to do this for other verbose libraries too

    logger.info(f"Loguru logging configured. Level: {LOG_LEVEL}, Service: {SERVICE_NAME} v{SERVICE_VERSION}")
