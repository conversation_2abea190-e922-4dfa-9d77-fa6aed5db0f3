import uuid
from contextvars import ContextVar

# Define context variables with default values
rozie_correlation_id_var: ContextVar[str] = ContextVar("rozie_correlation_id", default=f"{str(uuid.uuid4())}")
user_id_var: ContextVar[str] = ContextVar("rozie_user_id", default=f"rozie_user_{uuid.uuid4()}")
channel_user_id_var: ContextVar[str] = ContextVar("channel_user_id", default=f"{uuid.uuid4()}")
rozie_application_id_var: ContextVar[str | None] = ContextVar("rozie_application_id", default=None)
client_var: ContextVar[str | None] = ContextVar("client", default="unknown")
application_var: ContextVar[str | None] = ContextVar("application", default="unknown")
environment_var: ContextVar[str | None] = ContextVar("environment", default="unknown")
language_var: ContextVar[str | None] = ContextVar("language", default=None)
conversation_id: ContextVar[str | None] = ContextVar("conversation_id", default=None)
channel_var: ContextVar[str | None] = ContextVar("channel", default=None)
