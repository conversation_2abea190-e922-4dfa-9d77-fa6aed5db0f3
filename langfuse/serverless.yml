service: ${self:custom.serviceName}

custom:
  serviceName: framework-langfuse
  ecrRepoUri: ${env:IMAGE_URL}
  domainName: ${env:DOMAIN_NAME}
  awsRegion: ${env:AWS_REGION} 
  dbusername: ${env:DB_USERNAME}
  dbpassword: ${env:DB_PASSWORD}
  nextAuthSecret: ${env:NEXTAUTH_SECRET}
  nextAuthUrl: ${env:NEXTAUTH_URL}
  salt: ${env:SALT}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "${self:provider.region}"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource: 
        - !Sub arn:aws:logs:${self:provider.region}:${AWS::AccountId}:log-group:${self:service}-${self:provider.stage}:*
        - !Sub arn:aws:ecs:${self:provider.region}:${AWS::AccountId}:service/${self:service}-${self:provider.stage}-cluster/*
    - Effect: "Allow"
      Action: 
        - ec2:DescribeVpcs
        - ec2:CreateVpc
        - ec2:DeleteVpc
        - ec2:DescribeSubnets
        - ec2:CreateSubnet
        - ec2:DeleteSubnet
        - ec2:DescribeRouteTables
        - ec2:CreateRouteTable
        - ec2:DeleteRouteTable
      Resource:
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:vpc/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:subnet/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:subnet/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:subnet/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:subnet/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:route-table/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:security-group/*
        - !Sub arn:aws:ec2:${self:provider.region}:${AWS::AccountId}:route-table/*
    - Effect: "Allow"
      Action: 
        - ssm:GetParameter
        - ssm:GetParameters
        - ssm:GetParametersByPath
      Resource: "*"
        
resources:
  Parameters:
    VPC:
      Type: String
      Description: "VPC ID from the elsa-shared stack"
      Default: "vpc-097a913dec572ef35"

    AZ1PublicSubnet1:
      Type: String
      Description: "AZ1 Public Subnet ID from the elsa-shared stack"
      Default: "subnet-057c4dca9332cd472"

    AZ2PublicSubnet1:
      Type: String
      Description: "AZ2 Public Subnet ID from the elsa-shared stack"
      Default: "subnet-06471393ec0ceae62"

    AZ1PrivateSubnet1:
      Type: String
      Description: "AZ1 Private Subnet ID from the elsa-shared stack"
      Default: "subnet-0470d8cf9d24d6fd2"

    AZ2PrivateSubnet1:
      Type: String
      Description: "AZ2 Private Subnet ID from the elsa-shared stack"
      Default: "subnet-036f342a4e93bdb8e"
      
  Resources:
    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                  Resource: !Sub arn:aws:logs:${self:provider.region}:${AWS::AccountId}:log-group:${self:service}-${self:provider.stage}:*               
     
    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30
            
    ALBSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for ALB
        GroupName: ${self:service}-${self:provider.stage}-alb-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 3000
            CidrIp: 0.0.0.0/0  # Only allow Lambda traffic
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 3000
            CidrIp: 0.0.0.0/0
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0  # Only send traffic to ECS

    FargateSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for ECS Fargate
        GroupName: ${self:service}-${self:provider.stage}-fg-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 3000
            SourceSecurityGroupId: !Ref ALBSecurityGroup  # Only allow traffic from ALB
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 3000
            SourceSecurityGroupId: !Ref ALBSecurityGroup  # Only allow traffic from ALB
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0  # Outbound traffic allowed (modify as needed)

    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-alb"
        Type: "application"
        Scheme: "internet-facing"
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref ALBSecurityGroup
        LoadBalancerAttributes:
          - Key: "idle_timeout.timeout_seconds"
            Value: 4000

    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTPS
        Port: 443
        SslPolicy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
        Certificates:
          - CertificateArn: ${self:custom.acmCertificateArn}
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup
      
    LoadBalancerListenerForHTTP:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
        - LoadBalancerListener  
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP
        Port: 80
        DefaultActions:
          - Type: redirect
            RedirectConfig:
              Protocol: HTTPS
              Port: "443"
              StatusCode: "HTTP_301"

    ElsaTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-tg"
        Port: 3000
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/api/public/health"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    TaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
        - LangfuseDB
      Properties:
        Family: ${self:service}-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 256
        Memory: 512
        ContainerDefinitions:
          - Name: ${self:service}-${self:provider.stage}
            Image: ${self:custom.ecrRepoUri}
            Memory: 512
            Cpu: 256
            Essential: true
            PortMappings:
              - ContainerPort: 3000
                Protocol: "tcp"
            Environment:
              - Name: DB_HOST
                Value: !GetAtt LangfuseDB.Endpoint.Address
              - Name: DB_PORT
                Value: "5432"
              - Name: DB_USER
                Value: ${self:custom.dbusername}
              - Name: DB_PASS
                Value: ${self:custom.dbpassword}
              - Name: DB_NAME
                Value: "langfuse"
              - Name: DATABASE_URL
                Value: !Sub "postgresql://${self:custom.dbusername}:${self:custom.dbpassword}@${LangfuseDB.Endpoint.Address}:5432/langfuse"
              - Name: NEXTAUTH_SECRET
                Value: ${self:custom.nextAuthSecret}
              - Name: SALT
                Value: ${self:custom.salt}
              - Name: NEXTAUTH_URL
                Value: ${self:custom.nextAuthUrl}
              - Name: DIRECT_URL
                Value: !Sub "postgresql://${self:custom.dbusername}:${self:custom.dbpassword}@${LangfuseDB.Endpoint.Address}:5432/langfuse"
              - Name: LANGFUSE_CSP_ENFORCE_HTTPS
                Value: false
              - Name: PORT
                Value: 3000
              - Name: HOSTNAME
                Value: 0.0.0.0   
              - Name: LANGFUSE_DEFAULT_PROJECT_ROLE
                Value: VIEWER  
              - Name: ENABLE_EVENT_LOG
                Value: true
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "${self:service}-${self:provider.stage}"

    EcrEcsSlsTask:
      Type: AWS::ECS::Service
      DependsOn:
        - TaskDefinition
        - ElsaTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref TaskDefinition
        ServiceName: ${self:service}-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          Alarms:
            AlarmNames:
              - "${self:service}-${self:provider.stage}-service-alarm"
            Enable: true
            Rollback: true
          DeploymentCircuitBreaker:
            Enable: true
            Rollback: true
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref FargateSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LoadBalancers:
          - ContainerName: ${self:service}-${self:provider.stage}
            ContainerPort: 3000
            TargetGroupArn: !Ref ElsaTargetGroup

    LangfuseDBSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: RDS security group
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 5432
            ToPort: 5432
            SourceSecurityGroupId: !Ref FargateSecurityGroup

    LangfuseDBSubnetGroup:
      Type: AWS::RDS::DBSubnetGroup
      Properties:
        DBSubnetGroupDescription: Subnet group for RDS instance
        SubnetIds:
          - !Ref AZ1PrivateSubnet1
          - !Ref AZ2PrivateSubnet1

    LangfuseDB:
      Type: AWS::RDS::DBInstance
      Properties:
        AllocatedStorage: 20
        DBInstanceClass: db.t3.micro
        Engine: postgres
        EngineVersion: "16.4"
        StorageEncrypted: true
        MasterUsername: ${self:custom.dbusername}
        MasterUserPassword: ${self:custom.dbpassword}
        AvailabilityZone: "${self:provider.region}a"
        MultiAZ: false
        PubliclyAccessible: true
        StorageType: "gp2"
        MaxAllocatedStorage: 1000
        DBName: langfuse
        AutoMinorVersionUpgrade: true
        DBSubnetGroupName: !Ref LangfuseDBSubnetGroup
        VPCSecurityGroups:
          - !Ref LangfuseDBSecurityGroup

    APIRecordSet:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.domainName}
        Type: A
        AliasTarget:
          DNSName: !GetAtt 'ApplicationLoadBalancer.DNSName'
          HostedZoneId: !GetAtt 'ApplicationLoadBalancer.CanonicalHostedZoneID'
          EvaluateTargetHealth: false

