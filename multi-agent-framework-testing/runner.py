import requests
import json
import time
import threading
from uuid import uuid4
from collections import defaultdict

# Configuration Constants
# BASIC_URL = "http://agapi.dev-scc-demo.rozie.ai"
# BASIC_URL = "http://agapi-demo.dev-scc-demo.rozie.ai"
BASIC_URL = "http://0.0.0.0"
PATH_INITIATE = "chat/v3_async/initiate_chat"
PATH_SEND = "chat/v3_async/send_message"
PATH_RECEIVE = "chat/v3_async/get_message"
HEADERS = {"Content-Type": "application/json", "access_token": "Test@123"}
USER_NAME = "RozieAI"
PHONE_NUMBER = "+19422139024"
CHANNEL_NAME = "webchat"
CHANNEL_ID = "LC2"
ROSTER_ID = "salon718-Dev"
CLIENT_ID = "FppFWu6inFQ01Foq-N5T2_nYVQwcfZ9GOXqyGMxZkNM"
WELCOME_MESSAGE_FLAG = "profile_found"
LOCATION = "Bay Ridge"
ROSTER_ID = "AirCanada"
# User/Agent Information
# USER_INFO = {
#     "UserName": USER_NAME,
#     "PhoneNumber": PHONE_NUMBER,
#     "ClientId": CLIENT_ID,
#     "WelcomeMessageFlag": WELCOME_MESSAGE_FLAG,
#     "Location": LOCATION,
# }
CHANNEL_INFO = {"channel_id": CHANNEL_ID, "channel_name": CHANNEL_NAME}
# LLM_CONTEXT = {
#     "Customer's Dialed Location": LOCATION,
#     "Customer's Dialed Number": PHONE_NUMBER,
#     "Customer's Name": USER_NAME,
# }

ROSTER_ID = "AirCanada"
# ROSTER_ID = "RozieAir"
USER_INFO = {"UserName": "Shubham"}
LLM_CONTEXT = {}


USER_MESSAGES = [
    "i lost my baggage",
    "whats the process to file a claim?",
    "what are the list of documents are needed to file it?",
    "what amount of compensation i am entitled for?",
    "end",
]
REPORT = defaultdict(list)


def get_initiate_event(chat_id):
    """Builds the initiate chat event payload."""
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": {**USER_INFO, "llmContext": LLM_CONTEXT},
        },
        "channel": {
            **CHANNEL_INFO,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "initiate_event",
                "event_user": {
                    "user_id": {
                        "id": chat_id,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": {**USER_INFO, "llmContext": LLM_CONTEXT},
                },
                # "event_template": {"event_type": "initiate", "rosters_id": ROSTER_ID},
                "event_template": {
                    "event_type": "initiate",
                    "rosters_id": ROSTER_ID,
                    "selected_workflow": "Knowledge_Base",
                },
            }
        ],
    }


def get_send_message_event(user_input, chat_id):
    """Builds the send message event payload."""
    return {
        "version": "1.0",
        "user_info": {
            "user_id": {"id": chat_id, "id_type": "chat_id", "id_resource": "chat"},
            "user_info": USER_INFO,
        },
        "channel": {
            **CHANNEL_INFO,
            "ui_info": {"should_consolidate_buttons": False},
        },
        "incoming_events": [
            {
                "event_id": "message_event",
                "event_user": {
                    "user_id": {
                        "id": chat_id,
                        "id_type": "chat_id",
                        "id_resource": "chat",
                    },
                    "user_info": USER_INFO,
                },
                "event_template": {"event_type": "text", "text": user_input},
            }
        ],
    }


def initiate_chat(chat_id):
    """Sends the initiate chat event to the server."""
    url = f"{BASIC_URL}/{PATH_INITIATE}"
    response = requests.post(url, headers=HEADERS, json=get_initiate_event(chat_id), timeout=30)
    return response.json()


def send_message(user_input, chat_id):
    """Sends a message event to the server."""
    url = f"{BASIC_URL}/{PATH_SEND}"
    response = requests.post(
        url,
        headers=HEADERS,
        json=get_send_message_event(user_input, chat_id),
        timeout=30,
    )
    return response.json()


def receive_message(chat_id):
    """Fetches the latest agent message."""
    url = f"{BASIC_URL}/{PATH_RECEIVE}?chatId={chat_id}"
    response = requests.get(url, headers=HEADERS)
    return response.json()


def run_chat():
    """Main chat loop."""
    chat_id = f"TEST_LOCAL_{uuid4()}"
    print(chat_id)
    user_input_count = 0
    initiate_chat(chat_id)
    last_action_time = time.time()
    print("run_chat")
    while True:
        agent_response = receive_message(chat_id)
        time_taken = round(time.time() - last_action_time, 2)
        next_action = agent_response.get("next_action")

        if next_action == "wait":
            time.sleep(0.2)
            continue
        elif next_action in {"say", "gather"}:
            responses = agent_response.get("response_map", {}).get("responses", {}).get("default", [])
            for response in responses:
                text = response.get("response_template", {}).get("text")
                if text:
                    REPORT[chat_id].append(f"Agent: {time_taken} : " + text)

            if next_action == "gather":
                user_input = USER_MESSAGES[user_input_count]
                REPORT[chat_id].append("User : " + user_input)
                user_input_count += 1
                if user_input.lower() == "end":
                    break
                response = send_message(user_input, chat_id)

            last_action_time = time.time()
        else:
            responses = agent_response.get("response_map", {}).get("responses", {}).get("default", [])
            for response in responses:
                text = response.get("response_template", {}).get("text")
                if text:
                    REPORT[chat_id].append(f"Agent: {time_taken} : " + text)
            break

        time.sleep(0.2)


def run_test():
    """Runs the chat multiple times in parallel."""
    threads = []
    for i in range(5):
        print(f"loop{i}")
        thread = threading.Thread(target=run_chat)
        thread.start()
        threads.append(thread)

    for thread in threads:
        thread.join()


if __name__ == "__main__":
    run_test()

    with open(f"report_{uuid4()}.json", "w") as f:
        json.dump(REPORT, f, indent=4)
