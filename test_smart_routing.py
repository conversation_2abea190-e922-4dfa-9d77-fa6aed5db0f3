#!/usr/bin/env python3
"""
Test Smart Routing Logic
========================

This script validates the corrected smart routing approach where:
- Agents handle requests within their scope
- Agents route to triage only when clearly out-of-scope
- <PERSON><PERSON> handles the routing decisions
"""

def test_smart_routing_scenarios():
    """Test various scenarios to show the smart routing logic."""
    print("🧪 Testing Smart Routing Scenarios")
    print("=" * 60)
    
    scenarios = [
        {
            "context": "User in Booking Flow",
            "user_input": "Where can I fly to from New York?",
            "expected_handler": "Booking Agent",
            "reason": "Booking-related question, stays with booking agent"
        },
        {
            "context": "User in Booking Flow", 
            "user_input": "What are the baggage fees?",
            "expected_handler": "Booking Agent",
            "reason": "Booking-related question, stays with booking agent"
        },
        {
            "context": "User in Booking Flow",
            "user_input": "My flight is delayed",
            "expected_handler": "Triage → Flight Status Agent",
            "reason": "Out-of-scope for booking, routes to triage"
        },
        {
            "context": "User in Booking Flow",
            "user_input": "Something's wrong with my flight",
            "expected_handler": "Triage → Flight Support Agent", 
            "reason": "Out-of-scope for booking, routes to triage"
        },
        {
            "context": "User in Booking Flow",
            "user_input": "I want to cancel my booking",
            "expected_handler": "Triage → Cancellation Agent",
            "reason": "Cancellation is different from booking, routes to triage"
        },
        {
            "context": "User in Flight Status Flow",
            "user_input": "When does my flight depart?",
            "expected_handler": "Flight Status Agent",
            "reason": "Status-related question, stays with status agent"
        },
        {
            "context": "User in Flight Status Flow",
            "user_input": "I want to book another flight",
            "expected_handler": "Triage → Booking Agent",
            "reason": "Out-of-scope for status, routes to triage"
        }
    ]
    
    print("📋 SMART ROUTING SCENARIOS:")
    print()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. Context: {scenario['context']}")
        print(f"   User: \"{scenario['user_input']}\"")
        print(f"   Expected: {scenario['expected_handler']}")
        print(f"   Reason: {scenario['reason']}")
        print()
    
    return True


def test_gen_agent_template_logic():
    """Test that gen_agent template has the right logic."""
    print("🧪 Testing Gen Agent Template Logic")
    print("=" * 60)
    
    try:
        template_path = "llm_app/pipeline/steps/agentic_framework/autogen/resources/prompt_templates/gen_agent.txt"
        
        with open(template_path, 'r') as f:
            content = f.read()
        
        # Check for smart routing logic
        required_patterns = [
            "Smart Request Handling",
            "When to Route to Triage",
            "When to Continue Handling",
            "route_to_triage_agent()",
            "CLEARLY outside your domain",
            "My flight is delayed",
            "Something's wrong with my flight"
        ]
        
        missing_patterns = []
        for pattern in required_patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"❌ [TEST] Missing required patterns: {missing_patterns}")
            return False
        else:
            print("✅ [TEST] All smart routing patterns found")
            print("✅ [TEST] Gen agents will make intelligent routing decisions")
            return True
        
    except Exception as e:
        print(f"❌ [TEST] Error: {str(e)}")
        return False


def show_corrected_conversation_flow():
    """Show how the corrected conversation flow works."""
    print("🎭 Corrected Conversation Flow Examples")
    print("=" * 60)
    
    print("📋 SCENARIO 1: Booking-related request (STAYS with booking agent)")
    print("  1. User: 'I want to book a flight' → Triage routes to Booking Agent")
    print("  2. Booking Agent: 'Great! Where would you like to go?'")
    print("  3. User: 'What are the baggage fees?' → ✅ STAYS with Booking Agent")
    print("  4. Booking Agent: 'Here are the baggage fees...' (CORRECT!)")
    print()
    
    print("📋 SCENARIO 2: Out-of-scope request (ROUTES to triage)")
    print("  1. User: 'I want to book a flight' → Triage routes to Booking Agent")
    print("  2. Booking Agent: 'Great! Where would you like to go?'")
    print("  3. User: 'Something's wrong with my flight' → ✅ ROUTES to Triage")
    print("  4. Triage: 'I understand you were booking, but now you need help with a flight issue.'")
    print("  5. Triage: Routes to Flight Support Agent")
    print("  6. Flight Support Agent: Handles the flight issue properly")
    print()
    
    print("🎯 KEY PRINCIPLES:")
    print("  ✅ Agents handle requests within their expertise")
    print("  ✅ Agents route to triage only when clearly out-of-scope")
    print("  ✅ Triage makes the routing decisions")
    print("  ✅ Users get help from the right specialist")
    
    return True


def main():
    """Run all tests for the smart routing approach."""
    print("🎯 Smart Routing Logic Validation")
    print("=" * 70)
    
    tests = [
        ("Smart Routing Scenarios", test_smart_routing_scenarios),
        ("Gen Agent Template Logic", test_gen_agent_template_logic),
        ("Corrected Conversation Flow", show_corrected_conversation_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {str(e)}")
    
    print("\n" + "=" * 70)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SUCCESS: Smart routing logic is working!")
        print("\n📋 The corrected approach ensures:")
        print("  ✅ Agents handle requests within their scope")
        print("  ✅ Smart routing only when clearly out-of-scope")
        print("  ✅ Triage handles routing decisions")
        print("  ✅ Efficient conversation flow")
        return True
    else:
        print("⚠️  Some tests failed. Check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
