#!/usr/bin/env python3
"""
Quick Evaluation Test Runner
===========================

Simple script to run evaluation tests with common configurations.

Usage Examples:
    # Run all tests sequentially
    python run_evaluation_tests.py

    # Run talk-back tests only
    python run_evaluation_tests.py --category "TALK-BACK"

    # Run tests in parallel
    python run_evaluation_tests.py --parallel

    # Run unknown-info tests
    python run_evaluation_tests.py --category "UNKNOWN"

    # Run conversation sequence tests
    python run_evaluation_tests.py --type sequence
"""

import sys
import os
import subprocess
import argparse

def run_evaluation_tests(category=None, parallel=False, test_type=None, max_workers=3):
    """Run evaluation tests with specified parameters."""
    
    # Build command
    cmd = ["python3", "evaluation_test_runner.py"]
    
    if parallel:
        cmd.extend(["--parallel", "--max-workers", str(max_workers)])
    else:
        cmd.append("--sequential")
    
    if category:
        cmd.extend(["--category", category])
    
    if test_type:
        cmd.extend(["--test-type", test_type])
    
    # Always save results with timestamp
    cmd.extend(["--roster-id", "RozieAir_V2"])
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    print("=" * 60)
    
    # Execute the command
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Test execution failed with return code: {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"❌ Could not find evaluation_test_runner.py")
        print(f"   Make sure you're in the correct directory")
        return False

def main():
    """Main function with predefined test scenarios."""
    parser = argparse.ArgumentParser(description="Quick Evaluation Test Runner")
    parser.add_argument("--category", type=str, help="Test category filter")
    parser.add_argument("--parallel", action="store_true", help="Run tests in parallel")
    parser.add_argument("--type", choices=["single", "sequence", "special"], help="Test type filter")
    parser.add_argument("--workers", type=int, default=3, help="Max parallel workers")
    parser.add_argument("--scenario", choices=[
        "all", "core", "unknown", "conversation", "stress", "quick"
    ], default="quick", help="Predefined test scenarios")
    
    args = parser.parse_args()
    
    print("🧪 Evaluation Test Runner")
    print("=" * 40)
    
    if args.scenario == "all":
        print("📋 Running ALL evaluation tests...")
        success = run_evaluation_tests(parallel=args.parallel, max_workers=args.workers)
        
    elif args.scenario == "core":
        print("📋 Running CORE Shubham requirements tests...")
        categories = ["TALK-BACK", "CLARIFICATION", "UNKNOWN-INFO", "MID-CONVERSATION", "THREE-PATH"]
        success = True
        for category in categories:
            print(f"\n🎯 Testing {category}...")
            if not run_evaluation_tests(category=category, parallel=args.parallel, max_workers=args.workers):
                success = False
                
    elif args.scenario == "unknown":
        print("📋 Running UNKNOWN-INFO tests...")
        success = run_evaluation_tests(category="UNKNOWN", parallel=args.parallel, max_workers=args.workers)
        
    elif args.scenario == "conversation":
        print("📋 Running CONVERSATION SEQUENCE tests...")
        success = run_evaluation_tests(test_type="sequence", parallel=args.parallel, max_workers=args.workers)
        
    elif args.scenario == "stress":
        print("📋 Running STRESS and EDGE CASE tests...")
        categories = ["STRESS", "EDGE", "REALISTIC"]
        success = True
        for category in categories:
            print(f"\n🎯 Testing {category}...")
            if not run_evaluation_tests(category=category, parallel=args.parallel, max_workers=args.workers):
                success = False
                
    elif args.scenario == "quick":
        print("📋 Running QUICK test sample (first 10 tests)...")
        # For quick testing, we'll run talk-back tests only
        success = run_evaluation_tests(category="TALK-BACK", parallel=False, max_workers=1)
        
    else:
        # Custom category/type specified
        success = run_evaluation_tests(
            category=args.category, 
            test_type=args.type, 
            parallel=args.parallel, 
            max_workers=args.workers
        )
    
    if success:
        print(f"\n✅ Evaluation tests completed successfully!")
        print(f"📁 Check the generated JSON file for detailed results")
    else:
        print(f"\n❌ Some tests failed or encountered errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
