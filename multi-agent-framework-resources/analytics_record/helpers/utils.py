from datetime import datetime


def format_datetime(dt: datetime, format_str: str) -> str:
    """
    Formats a datetime object according to the specified format string.

    Args:
        dt (datetime): The datetime object to be formatted.
        format_str (str): The format string to be used for formatting.

    Returns:
        str: The formatted datetime string.
    """
    return dt.strftime(format_str)


def parse_datetime(date_str: str, format_str: str) -> datetime:
    """
    Parses a date string into a datetime object according to the specified format string.

    Args:
        date_str (str): The date string to be parsed.
        format_str (str): The format string that describes the date string.

    Returns:
        datetime: The parsed datetime object.
    """
    return datetime.strptime(date_str, format_str)

