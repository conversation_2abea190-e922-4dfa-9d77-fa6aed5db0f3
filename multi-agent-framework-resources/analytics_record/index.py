import os
import json
from .helpers.dynamo_helper import dynamo_resource_operation
from .helpers.utils import parse_datetime, format_datetime


def conversation_started_event_handler(event_data):
    """
    Handles the "conversation_started" event by storing the event data in DynamoDB.
    """
    dt = parse_datetime(event_data.get("timestamp"), "%Y-%m-%dT%H:%M:%S.%f")
    conversation_record_table_name = os.getenv("CONVERSATION_RECORD_TABLE_NAME")
    conversation_record_table_item = {
        "conversation_id": event_data.get("conversation_id"),
        "status": "in-progress",
        "roster_id": event_data.get("event", [{}])[0].get("rosters_id"),
        "timestamp": str(int(dt.timestamp())),
        "metadata": {
            "rozie_correlation_id": event_data.get("rozie_correlation_id"),
            "application_id": event_data.get("application_id"),
            "conversation_start_timestamp": format_datetime(
                dt, "%B %d, %Y %I:%M:%S %p"
            ),
            "channel_name": event_data.get("custom_fields", {}).get("channel_name"),
            "channel_id": event_data.get("custom_fields", {}).get("channel-id"),
            "language": event_data.get("custom_fields", {}).get("language"),
            "rozie_user_id": event_data.get("custom_fields", {}).get("rozie_user_id"),
            "llm_context": event_data.get("event", {})[0].get("llm_context"),
            "user_context": event_data.get("event", {})[0].get("user_context"),
        },
    }
    if event_data.get("event", [{}])[0].get("selected_workflow"):
        conversation_record_table_item["single_agent_mode"] = True
        conversation_record_table_item["metadata"]["selected_workflow"] = (
            event_data.get("event", {})[0].get("selected_workflow")
        )

    event_record_table_name = os.getenv("EVENT_RECORD_TABLE_NAME")
    event_record_table_item = {
        "conversation_id": event_data.get("conversation_id"),
        "timestamp": str(int(dt.timestamp())),
        "event_data": event_data.get("event", [{}])[0],
        "event_type": "conversation_started",
    }
    conversation_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=conversation_record_table_name,
        Item=conversation_record_table_item,
    )
    event_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=event_record_table_name,
        Item=event_record_table_item,
    )
    if not conversation_record_response or not event_record_response:
        return False
    return True


def request_received_event_handler(event_data):
    """
    Handles the "request_received" event by storing the event data in DynamoDB.
    """
    dt = parse_datetime(event_data.get("timestamp"), "%Y-%m-%dT%H:%M:%S.%f")
    event_record_table_name = os.getenv("EVENT_RECORD_TABLE_NAME")
    event_record_table_item = {
        "conversation_id": event_data.get("conversation_id"),
        "timestamp": str(int(dt.timestamp())),
        "event_data": event_data.get("event", {}),
        "event_type": "request_received",
    }
    event_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=event_record_table_name,
        Item=event_record_table_item,
    )
    if not event_record_response:
        return False
    return True


def response_sent_event_handler(event_data):
    """
    Handles the "response_sent" event by storing the event data in DynamoDB.
    """
    dt = parse_datetime(event_data.get("timestamp"), "%Y-%m-%dT%H:%M:%S.%f")
    event_record_table_name = os.getenv("EVENT_RECORD_TABLE_NAME")
    event_record_table_item = {
        "conversation_id": event_data.get("conversation_id"),
        "timestamp": str(int(dt.timestamp())),
        "event_data": event_data.get("event", {}),
        "event_type": "response_sent",
    }
    event_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=event_record_table_name,
        Item=event_record_table_item,
    )
    if not event_record_response:
        return False
    return True


def conversation_ended_event_handler(event_data):
    """
    Handles the "conversation_ended" event by storing the event data in DynamoDB.
    """
    dt = parse_datetime(event_data.get("timestamp"), "%Y-%m-%dT%H:%M:%S.%f")
    conversation_record_table_name = os.getenv("CONVERSATION_RECORD_TABLE_NAME")
    conversation_record_table_item = dynamo_resource_operation(
        operation="get_item",
        table_name=conversation_record_table_name,
        Key={"conversation_id": event_data.get("conversation_id")},
    )["Item"]
    conversation_record_table_item["status"] = "completed"
    conversation_record_table_item["metadata"]["conversation_end_timestamp"] = (
        format_datetime(dt, "%B %d, %Y %I:%M:%S %p")
    )
    event_record_table_name = os.getenv("EVENT_RECORD_TABLE_NAME")
    event_record_table_item = {
        "conversation_id": event_data.get("conversation_id"),
        "timestamp": str(int(dt.timestamp())),
        "event_data": event_data.get("event", {}),
        "event_type": "conversation_ended",
    }
    conversation_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=conversation_record_table_name,
        Item=conversation_record_table_item,
    )
    event_record_response = dynamo_resource_operation(
        operation="put_item",
        table_name=event_record_table_name,
        Item=event_record_table_item,
    )
    if not conversation_record_response or not event_record_response:
        return False
    return True


def lambda_handler(event, context):
    """
    AWS Lambda function to handle incoming events and store them in DynamoDB.
    """
    # Extract the event data from the incoming request
    print("Analytics record lambda received:", event)
    events_data = json.loads(event.get("body"))
    # Check if the event data is present
    if not events_data:
        return {"statusCode": 400, "body": "No event data provided."}

    for event_data in events_data:
        if event_data.get("event_name") == "conversation_started":
            conversation_started_event_handler(event_data)
        elif event_data.get("event_name") == "request_received":
            request_received_event_handler(event_data)
        elif event_data.get("event_name") == "response_sent":
            response_sent_event_handler(event_data)
        elif event_data.get("event_name") == "conversation_ended":
            conversation_ended_event_handler(event_data)
        else:
            return {
                "statusCode": 400,
                "body": f"Unknown event name: {event_data.get('event_name')}",
            }
    return {"statusCode": 200, "body": "Event processed successfully."}
