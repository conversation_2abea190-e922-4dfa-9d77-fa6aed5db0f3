service: multi-agent-framework-resources

custom:
  dev:
    LayerBucket: scc-layers
  master:
    LayerBucket: scc-layers

provider:
  name: aws
  runtime: python3.10
  stage: ${opt:stage, "dev1"}
  region: ${opt:region, "ca-central-1"}
  endpointType: regional
  lambdaHashingVersion: 20201221
  memorySize: 512
  timeout: 120
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:*
      Resource: "*"

functions:
  analytics_record:
    name: ${self:service}-analytics-record-${self:provider.stage}
    handler: analytics_record/index.lambda_handler
    description: AWS analytics record processor for multi-agent framework system
    environment:
      LOG_LEVEL: INFO
      ENABLE_LOGS: TRUE
      CONVERSATION_RECORD_TABLE_NAME: ${self:service}-conversation-record-${self:provider.stage}
      EVENT_RECORD_TABLE_NAME: ${self:service}-event-record-${self:provider.stage}
    layers:
      - !Ref <PERSON>ayer
    events:
      - http:
          path: /analytics/internal-record
          method: post
          # private: true
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - x-api-key
              - version
              - sessionId
              - Accept
              - Referer
              - sec-ch-ua
              - sec-ch-ua-mobile
              - sec-ch-ua-platform
              - sentry-trace
              - User-Agent
              - x-ffp
              - x-uuid
              - x-uid
              - x-agent-id
              - x-lang

resources:
  extensions:
    ServerlessDeploymentBucket:
      Properties:
        PublicAccessBlockConfiguration:
          BlockPublicAcls: True
          BlockPublicPolicy: True
          IgnorePublicAcls: True
          RestrictPublicBuckets: True
  Resources:
    BotoLambdaLayer:
      Type: AWS::Lambda::LayerVersion
      Properties:
        CompatibleRuntimes:
          - python3.8
          - python3.9
          - python3.10
        Content:
          S3Bucket: ${self:custom.${self:provider.stage}.LayerBucket}
          S3Key: boto3_layer.zip
        LayerName: boto3-python-layer-${self:provider.stage}
    FunctionConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: function_id
            AttributeType: S
        KeySchema:
          - AttributeName: function_id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-functions-config-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    RosterConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: roster_id
            AttributeType: S
        KeySchema:
          - AttributeName: roster_id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-rosters-config-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    AgentConfigTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: agent_id
            AttributeType: S
        KeySchema:
          - AttributeName: agent_id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-agents-config-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    ConversationRecordTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: conversation_id
            AttributeType: S
        KeySchema:
          - AttributeName: conversation_id
            KeyType: HASH
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-conversation-record-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True
    EventRecordTable:
      Type: AWS::DynamoDB::Table
      Properties:
        AttributeDefinitions:
          - AttributeName: conversation_id
            AttributeType: S
          - AttributeName: timestamp
            AttributeType: S
        KeySchema:
          - AttributeName: conversation_id
            KeyType: HASH
          - AttributeName: timestamp
            KeyType: RANGE
        BillingMode: PAY_PER_REQUEST
        TableName: ${self:service}-event-record-${self:provider.stage}
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: True