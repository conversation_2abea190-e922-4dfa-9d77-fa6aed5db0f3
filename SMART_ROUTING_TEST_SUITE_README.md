# Comprehensive Smart Routing Test Suite

This comprehensive test suite validates the smart routing logic implementation based on <PERSON><PERSON><PERSON>'s requirements and covers all edge cases for the enhanced triage agent V2.

## Overview

The test suite includes:

1. **Smart Routing Logic Tests** - Validates agent-specific routing decisions
2. **<PERSON><PERSON><PERSON>'s Requirements Compliance** - Tests talk-back, clarification, graceful fallbacks, etc.
3. **Integration Tests** - End-to-end conversation flows and agent handoffs
4. **Edge Cases** - Malformed inputs, rapid context switching, error handling
5. **Performance Tests** - Response time and memory usage validation

## Files Structure

```
├── comprehensive_smart_routing_test_suite.py  # Main test suite implementation
├── run_smart_routing_tests.py                 # Test runner with scenarios
├── validate_shubham_requirements.py           # Requirements compliance validator
├── test_scenarios_config.json                 # Test scenarios configuration
└── SMART_ROUTING_TEST_SUITE_README.md        # This documentation
```

## Quick Start

### Run All Tests
```bash
python comprehensive_smart_routing_test_suite.py
```

### Run Specific Test Categories
```bash
# Run only routing logic tests
python comprehensive_smart_routing_test_suite.py --category routing

# Run only <PERSON><PERSON><PERSON>'s requirements tests
python comprehensive_smart_routing_test_suite.py --category requirements

# Run integration tests
python comprehensive_smart_routing_test_suite.py --category integration

# Run edge case tests
python comprehensive_smart_routing_test_suite.py --category edge_cases

# Run performance tests
python comprehensive_smart_routing_test_suite.py --category performance
```

### Run with Specific Priority
```bash
# Run high priority tests only
python comprehensive_smart_routing_test_suite.py --priority 1

# Run medium priority tests
python comprehensive_smart_routing_test_suite.py --priority 2
```

### Verbose Output
```bash
python comprehensive_smart_routing_test_suite.py --verbose
```

## Using the Test Runner

The test runner provides additional scenarios and reporting capabilities:

### Run Specific Scenarios
```bash
# Run Shubham's requirements validation
python run_smart_routing_tests.py --scenario shubham_requirements

# Run edge cases testing
python run_smart_routing_tests.py --scenario edge_cases

# Run performance testing
python run_smart_routing_tests.py --scenario performance

# Run integration testing
python run_smart_routing_tests.py --scenario integration
```

### Generate Test Reports
```bash
python run_smart_routing_tests.py --report
```

## Validate Shubham's Requirements

Use the dedicated requirements validator:

```bash
# Basic validation
python validate_shubham_requirements.py

# Detailed validation with explanations
python validate_shubham_requirements.py --detailed

# Generate compliance report
python validate_shubham_requirements.py --report
```

## Test Categories Explained

### 1. Smart Routing Logic Tests

Tests the core routing decisions:
- **Agent Domain Handling**: Agents handle requests within their scope
- **Out-of-Scope Routing**: Agents route inappropriate requests to triage
- **Triage Routing Decisions**: Triage agent makes correct routing choices
- **Mid-conversation Transfer**: Context switching during conversations
- **Generic Request Handling**: Handling of generic but domain-relevant requests

### 2. Shubham's Requirements Compliance

Validates specific requirements:
- **Talk-back Capability**: Conversational responses before routing
- **Clarification Handling**: Asking clarifying questions for ambiguous requests
- **Unknown-info Graceful Fallbacks**: Handling unknown requests gracefully
- **Mid-conversation Transfer Support**: Seamless agent handoffs
- **Three-path Decision Model**: Direct help/route/clarify/out-of-scope paths

### 3. Integration Tests

End-to-end validation:
- **Complete Conversation Flows**: Multi-turn conversations with routing
- **Complex Agent Handoffs**: Multiple agent transfers in one conversation
- **Configuration Validation**: Proper setup and configuration loading
- **Template Compliance**: Prompt template validation

### 4. Edge Cases

Robust error handling:
- **Malformed Input Handling**: Empty, very long, or special character inputs
- **Rapid Context Switching**: Quick changes in conversation direction
- **Agent Unavailability**: Handling when target agents are not available
- **Timeout Scenarios**: Handling of slow responses or timeouts

### 5. Performance Tests

Performance validation:
- **Routing Decision Speed**: Response time for routing decisions
- **Memory Usage Stability**: Memory usage during extended conversations
- **Concurrent Request Handling**: Multiple simultaneous requests
- **Large Conversation Handling**: Performance with long conversation histories

## Test Results and Reporting

### Test Results Format

Each test execution generates:
- **Summary Statistics**: Pass/fail counts, success rate, execution time
- **Detailed Results**: Individual test case results with timing
- **Error Analysis**: Detailed error messages for failed tests
- **Performance Metrics**: Response times and resource usage

### Generated Reports

1. **JSON Results File**: `test_results_YYYYMMDD_HHMMSS.json`
2. **Markdown Report**: `smart_routing_test_report.md`
3. **Compliance Report**: `shubham_requirements_compliance.md`

## Configuration

### Test Scenarios Configuration

The `test_scenarios_config.json` file defines:
- Test scenarios for each category
- Expected behaviors and outcomes
- Validation criteria and thresholds
- Success metrics and compliance requirements

### Customizing Tests

To add new test cases:

1. **Add to Test Suite**: Extend the `_setup_*_tests()` methods in `comprehensive_smart_routing_test_suite.py`
2. **Implement Test Logic**: Add corresponding test methods
3. **Update Configuration**: Add scenarios to `test_scenarios_config.json`
4. **Update Validation**: Extend requirements validation if needed

## Expected Results

### Success Criteria

- **Routing Accuracy**: ≥95% correct routing decisions
- **Response Time**: ≤2 seconds average response time
- **Clarification Success**: ≥90% successful clarification handling
- **Graceful Fallback**: 100% graceful handling of unknown requests
- **Requirements Compliance**: ≥80% compliance with Shubham's requirements

### Common Issues and Solutions

1. **Import Errors**: Ensure Python path includes the multi-agent-framework directory
2. **Configuration Missing**: Check that RozieAir_V2.json configuration exists
3. **Template Not Found**: Verify triage_agent_v2.txt template is present
4. **Performance Issues**: Run with smaller test sets for initial validation

## Integration with CI/CD

The test suite can be integrated into CI/CD pipelines:

```bash
# Run critical tests only
python comprehensive_smart_routing_test_suite.py --priority 1

# Exit with error code if tests fail
echo $?  # 0 = success, 1 = failure
```

## Troubleshooting

### Common Issues

1. **Module Import Errors**
   ```bash
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/multi-agent-framework"
   ```

2. **Configuration File Missing**
   - Ensure `RozieAir_V2.json` exists in the expected location
   - Check file permissions

3. **Template Files Missing**
   - Verify all prompt templates are present
   - Check template syntax and formatting

### Debug Mode

Run with verbose output for debugging:
```bash
python comprehensive_smart_routing_test_suite.py --verbose --category routing
```

## Contributing

To contribute to the test suite:

1. Follow the existing test case structure
2. Add comprehensive documentation
3. Include both positive and negative test cases
4. Ensure proper error handling
5. Update this README with new features

## Support

For issues or questions about the test suite:
1. Check the troubleshooting section
2. Review the generated error logs
3. Validate configuration files
4. Run individual test categories to isolate issues
